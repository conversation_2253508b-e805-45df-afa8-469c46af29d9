export AWS_ACCESS_KEY_ID=********************
export AWS_SECRET_ACCESS_KEY=PDkVbfjDwPniB7Igd7c558Yo85VaGVAqAMfHItLa
export S3_BASE_URL=http://snapraiselogos.s3.us-west-1.amazonaws.com/
export S3_BUCKET=snapraiselogos
export MAGENTO_API_TOKEN=fqpvxto4pq78z5xos5hk0b5hnnjdrley
# export MAGENTO_BASE_URL=https://admin.snapteamstore.com
export MAGENTO_PASSWORD=nbq-PWZ5yhj-tcp6qtk
export MAGENTO_USERNAME=WorkatoConnection
export MAGENTO_WORKER_URL=https://workato.snap.store
export MARCO_ACCOUNT_ID="183"
export MARCO_ACCOUNT_ZIP="98108"
export MARCO_API_KEY=dcacdcd961614c76989c80de9a6c4e3e
export MARCO_API_URL=https://staging.thedreamjunction.com/api/v3
export MARCO_STORE_ACCOUNT_ID: "185"
export MARCO_STORE_API_KEY: 6dccef3ce5e70dd6eab5d2c6831d7193
export MARCO_WEBHOOK_KEY: ****************************************************************
export DATABASE_URL=postgres://postgres:postgres@localhost:5432/store
export NETSUITE_ACCOUNT_ID="4766534-sb2"
export NETSUITE_APPLICATION_ID=95F7DECA-B864-4E8C-BACE-BE07E9CD5F7A
export NETSUITE_CLIENT_KEY=****************************************************************
export NETSUITE_CLIENT_SECRET=****************************************************************
export NETSUITE_CUSTOMER_ID="552117"
export NETSUITE_TOKEN_KEY=****************************************************************
export NETSUITE_TOKEN_SECRET=****************************************************************
export STORE_NETSUITE_API_KEY=helloworld
export SPLITIO_API_KEY=h8e2f2pnv2b8e146pejdp6kr2r8kj8ami3o8
export GRAPHQL_URL=http://localhost:3000/api/graphql
export GRAPHQL_API_KEY="hello"
# Your Medusa backend, should be updated to where you are hosting your server. Remember to update CORS settings for your server. See – https://docs.medusajs.com/usage/configurations#admin_cors-and-store_cors
export MEDUSA_BACKEND_URL=http://localhost:9000

# Your publishable key that can be attached to sales channels. See - https://docs.medusajs.com/development/publishable-api-keys
export NEXT_PUBLIC_MEDUSA_PUBLISHABLE_KEY=pk_e11ec70f2009a5368b5a0ae8cab618170d5472308861c06e9f9a96b236ea50c3

# Your store URL, should be updated to where you are hosting your storefront.
NEXT_PUBLIC_BASE_URL=http://localhost:8000

# Your preferred default region. When middleware cannot determine the user region from the "x-vercel-country" header, the default region will be used. ISO-2 lowercase format.
NEXT_PUBLIC_DEFAULT_REGION=us

# Your Stripe public key. See – https://docs.medusajs.com/add-plugins/stripe
NEXT_PUBLIC_STRIPE_KEY=

# Your Next.js revalidation secret. See – https://nextjs.org/docs/app/building-your-application/data-fetching/fetching-caching-and-revalidating#on-demand-revalidation
REVALIDATE_SECRET=supersecret

# export MAGENTO_CONSUMER_KEY=x9ncmoeirxpom868tdeirdsi7y1geeid
# export MAGENTO_CONSUMER_SECRET=pg68t6cn64ijytivs639nul686r0djxh
# export MAGENTO_ACCESS_TOKEN=3vkagtsngyltbya24t3w69ckd44vfk3p
# export MAGENTO_ACCESS_TOKEN_SECRET=t023q504ebe4hqypahgbeilrytnx0lh7
export MAGENTO_CONSUMER_KEY=blmoqtfdgr83z6dm79ydalxjjq5p5ffq
export MAGENTO_CONSUMER_SECRET=v9wa39oqqvhtbzid3e64t90cffq4yav5
export MAGENTO_ACCESS_TOKEN=1nhxcnoaby9sup10p24731fn259f6hsp
export MAGENTO_ACCESS_TOKEN_SECRET=4te7pbs2fm9113h0bn01y9g0nn86c64z
export MAGENTO_BASE_URL=https://admin.snap.store
export SLACK_NOTIFICATION_CHANNEL=C07P2LFPZRR
export SLACK_ORDER_CREATION_NOTIFICATION_CHANNEL=C082QHH1ARZ
export SLACK_TOKEN=*******************************************************
