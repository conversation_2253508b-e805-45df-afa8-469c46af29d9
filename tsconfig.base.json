{"compileOnSave": false, "compilerOptions": {"allowSyntheticDefaultImports": true, "resolveJsonModule": true, "rootDir": ".", "sourceMap": true, "declaration": false, "esModuleInterop": true, "moduleResolution": "node", "emitDecoratorMetadata": true, "experimentalDecorators": true, "importHelpers": true, "target": "es2015", "types": ["node", "jest"], "module": "esnext", "lib": ["es2021", "dom", "es2020.promise"], "skipLibCheck": true, "skipDefaultLibCheck": true, "baseUrl": ".", "paths": {"@store-monorepo/graphql": ["packages/graphql/src/index.ts"], "@store-monorepo/graphql/types": ["packages/graphql/src/types.ts"], "@store-monorepo/logger": ["packages/logger/src/index.ts"], "@store-monorepo/logo-uploader": ["packages/logo-uploader/src/index.ts"], "@store-monorepo/magento-api": ["packages/magento-api/src/index.ts"], "@store-monorepo/marco-api": ["packages/marco-api/src/index.ts"], "@store-monorepo/medusa-api": ["packages/medusa-api/src/index.ts"], "@store-monorepo/netsuite": ["packages/netsuite/src/index.ts"], "@store-monorepo/order-fulfillment": ["packages/order-fulfillment/src/index.ts"], "@store-monorepo/order-notifications": ["packages/order-notifications/src/index.ts"], "@store-monorepo/repo": ["packages/repo/src/index.ts"], "@store-monorepo/sentry": ["packages/sentry/src/index.ts"], "@store-monorepo/slack-api": ["packages/slack-api/src/index.ts"], "@store-monorepo/splitio-client": ["packages/splitio-client/src/index.ts"], "@store-monorepo/zendesk-api": ["packages/zendesk-api/src/index.ts"]}, "jsx": "react-jsx"}, "exclude": ["node_modules", "tmp"]}