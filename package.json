{"name": "@store-monorepo/source", "version": "0.0.0", "license": "MIT", "scripts": {"worker:start": "cd dist/apps/store/src/temporal && node worker.js", "store:start": "cd dist/apps/store && node -r newrelic main.js", "codegen": "graphql-codegen -c ./codegen.ts"}, "prisma": {"schema": "./apps/store/src/prisma/schema.prisma"}, "pnpm": {"supportedArchitectures": {"os": ["win32", "darwin", "current"], "cpu": ["x64", "arm64"]}}, "private": true, "dependencies": {"@apollo/client": "3.13.6", "@apollo/server": "^4.10.2", "@apollo/subgraph": "^2.7.2", "@aws-sdk/client-s3": "^3.591.0", "@easypost/api": "^8.0.1", "@fullstory/browser": "^2.0.6", "@headlessui/react": "^2.2.0", "@medusajs/admin-sdk": "2.4.0", "@medusajs/cli": "2.4.0", "@medusajs/core-flows": "2.4.0", "@medusajs/framework": "2.4.0", "@medusajs/icons": "^2.6.0", "@medusajs/js-sdk": "latest", "@medusajs/medusa": "2.4.0", "@medusajs/payment-stripe": "^2.4.0", "@medusajs/ui": "latest", "@medusajs/utils": "^2.6.0", "@mikro-orm/core": "6.4.3", "@mikro-orm/knex": "6.4.3", "@mikro-orm/migrations": "6.4.3", "@mikro-orm/postgresql": "6.4.3", "@newrelic/apollo-server-plugin": "6.0.0", "@prisma/client": "5.19.1", "@radix-ui/react-accordion": "^1.2.1", "@react-pdf/renderer": "^3.4.4", "@sentry/node": "8.34.0", "@slack/web-api": "^7.5.0", "@snap-mobile/snap-ui": "25.12.2", "@snap-mobile/toolkit": "^1.1.3", "@splitsoftware/splitio": "^10.28.0", "@stripe/react-stripe-js": "^1.7.2", "@stripe/stripe-js": "^1.29.0", "@temporalio/activity": "^1.8.0", "@temporalio/client": "^1.8.0", "@temporalio/common": "^1.8.0", "@temporalio/worker": "^1.8.0", "@temporalio/workflow": "^1.8.0", "apollo-graphql": "^0.9.7", "awilix": "^8.0.1", "axios": "^1.8.2", "axios-oauth-1.0a": "^0.3.6", "body-parser": "^1.20.2", "eslint-plugin-diff": "^2.0.3", "eslint-plugin-jest": "^28.6.0", "eslint-plugin-package-json": "^0.15.0", "express": "^4.18.1", "graphql": "^16.8.2", "graphql-loader": "^1.2.1", "graphql-middleware": "^6.1.35", "graphql-request": "6.1.0", "graphql-shield": "^7.6.5", "graphql-tag": "^2.12.6", "handlebars": "4.7.8", "lodash": "^4.17.21", "medusa-payment-stripe": "^6.0.11", "newrelic": "^12.7.0", "next": "15.0.3", "node-zendesk": "5.0.12", "oauth-1.0a": "^2.2.6", "pg": "^8.13.0", "puppeteer": "^22.10.0", "qs": "^6.12.1", "react": "18.3.1", "react-country-flag": "^3.1.0", "react-dom": "18.3.1", "server-only": "^0.0.1", "sharp": "^0.33.4", "tailwindcss-radix": "^2.8.0", "tslib": "^2.3.0", "webpack": "^5", "winston": "3.14.2", "winston-slack-webhook-transport": "2.3.5"}, "devDependencies": {"@babel/core": "^7.14.5", "@babel/preset-react": "^7.14.5", "@graphql-codegen/cli": "^5.0.2", "@graphql-codegen/client-preset": "4.2.6", "@graphql-codegen/introspection": "4.0.3", "@graphql-codegen/typescript": "^4.0.6", "@graphql-codegen/typescript-operations": "^4.2.1", "@graphql-codegen/typescript-resolvers": "^4.0.6", "@jest-mock/express": "^2.1.0", "@medusajs/test-utils": "2.4.0", "@medusajs/types": "^2.6.0", "@medusajs/ui-preset": "latest", "@mikro-orm/cli": "6.4.3", "@nx/eslint": "19.3.1", "@nx/eslint-plugin": "19.3.1", "@nx/express": "^19.3.1", "@nx/jest": "19.3.1", "@nx/js": "19.3.1", "@nx/node": "19.3.1", "@nx/react": "19.3.1", "@nx/web": "19.3.1", "@nx/webpack": "19.3.1", "@nx/workspace": "19.3.1", "@pmmmwh/react-refresh-webpack-plugin": "^0.5.7", "@svgr/webpack": "^8.0.1", "@swc-node/register": "~1.9.1", "@swc/cli": "~0.3.12", "@swc/core": "1.5.7", "@swc/helpers": "~0.5.11", "@swc/jest": "^0.2.36", "@testing-library/react": "15.0.6", "@types/aws-lambda": "^8.10.138", "@types/cookie-parser": "^1.4.6", "@types/express": "^4.17.21", "@types/jest": "^29.5.13", "@types/lodash": "^4.14.195", "@types/node": "^20.0.0", "@types/pg": "^8.11.0", "@types/react": "^18.3.2", "@types/react-dom": "^18.2.25", "@types/react-instantsearch-dom": "^6.12.3", "@typescript-eslint/eslint-plugin": "^7.3.0", "@typescript-eslint/parser": "^7.3.0", "ansi-colors": "^4.1.3", "apollo-server-express": "^3.13.0", "autoprefixer": "10.4.13", "babel-jest": "^29.4.1", "babel-loader": "^8.2.3", "concurrently": "^8.2.2", "copy-webpack-plugin": "^12.0.2", "cross-fetch": "4.0.0", "eslint": "~8.57.0", "eslint-config-next": "15.0.3", "eslint-config-prettier": "^9.0.0", "eslint-plugin-import": "2.27.5", "eslint-plugin-jsx-a11y": "6.7.1", "eslint-plugin-react": "7.32.2", "eslint-plugin-react-hooks": "4.6.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.4.1", "jest-environment-node": "^29.4.1", "nock": "13.5.4", "nx": "19.3.1", "postcss": "8.4.38", "prettier": "^2.6.2", "prisma": "5.19.1", "prop-types": "^15.8.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-refresh": "^0.10.0", "supertest": "^7.0.0", "tailwindcss": "3.4.3", "terser-webpack-plugin": "^5.3.10", "ts-jest": "^29.1.0", "ts-node": "^10.9.2", "typescript": "^5.6.2", "vite": "^5.2.11", "webpack-cli": "^5.1.4"}}