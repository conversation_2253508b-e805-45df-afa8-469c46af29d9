# Store ID Correlation Implementation

## Summary

Correlation between `storeCode` and store table IDs. Here's how it works:

### The Problem

- **storeCode**: `"impactvolleyball"` (URL-friendly identifier)
- **Store table ID**: `"store_01JXZE7CJXHWREBSXG3MY0JWAH"` (UUID primary key)

### The Solution

#### 1. Database Schema

The `Store` table has both fields:

```prisma
model Store {
  id        String  @id @default(dbgenerated("CONCAT('store_', RIGHT(uuid_generate_v4()::text, 12))"))
  storeCode String? @map("store_code")
  // ... other fields
}
```

#### 2. Repository Function

Added `findStoreByStoreCode` function in `packages/repo/src/lib/store.ts`:

```typescript
const findStoreByStoreCode = async (storeCode: string) => {
  return await RepoAdapter.store.findFirst({ where: { storeCode } });
};
```

#### 3. GraphQL API

Added GraphQL query in `apps/store/src/api/graphql/schema.graphql`:

```graphql
type Query {
  storeByCode(storeCode: String!): StoreType
}
```

And resolver in `apps/store/src/api/graphql/resolvers.ts`:

```typescript
const storeByCode = async (_: null, { storeCode }: { storeCode: string }) => {
  return await Repo.Store.findStoreByStoreCode(storeCode);
};
```

#### 4. Storefront Integration

Added lookup function in `apps/medusa-store-storefront/src/lib/data/stores.ts`:

```typescript
export const getStoreByCode = async(storeCode: string) => {
  const { data } = await client.query({
    query: StoreByCodeDocument,
    variables: { storeCode }
  });
  return data?.storeByCode || null;
}
```

#### 5. Cart Metadata Enhancement

Updated `addToCart` function to store both IDs:

```typescript
metadata: {
  logoUrl,
  netsuiteId,
  storeId,      // The actual store table ID (store_01JXZE...)
  storeCode     // The URL-friendly code (impactvolleyball)
}
```

### How It Works

1. **User adds product to cart** with `storeCode: "impactvolleyball"`
2. **System looks up store** using GraphQL: `storeByCode(storeCode: "impactvolleyball")`
3. **Gets store record** with `id: "store_01JXZE7CJXHWREBSXG3MY0JWAH"`
4. **Stores both values** in cart metadata:
   - `storeId: "store_01JXZE7CJXHWREBSXG3MY0JWAH"` (for database relations)
   - `storeCode: "impactvolleyball"` (for backward compatibility)

### Benefits

✅ **Proper database relations**: Use UUID for foreign keys and joins
✅ **Backward compatibility**: Keep storeCode for existing systems
✅ **Fallback handling**: If lookup fails, falls back to storeCode
✅ **Multi-store support**: Each cart item knows its exact store origin

### Usage Example

```typescript
// When processing orders
order.items.forEach(item => {
  const storeId = item.metadata.storeId;        // "store_01JXZE7CJXHWREBSXG3MY0JWAH"
  const storeCode = item.metadata.storeCode;    // "impactvolleyball"

  // Use storeId for database queries
  const store = await Store.findById(storeId);

  // Use storeCode for URLs/display
  const storeUrl = `https://${storeCode}.snap.store`;
});
```

This implementation provides the best of both worlds: proper database relationships using UUIDs while maintaining human-readable store codes for URLs and display purposes.
