name: CI Tests

on:
  pull_request:
    branches: [main]

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

permissions:
  contents: read
  deployments: write

env: ${{ secrets }}

jobs:
  tests:
    runs-on: ubuntu-latest
    name: Tests
    environment: test

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - uses: actions/setup-node@v4
        with:
          node-version-file: '.tool-versions'

      - name: Restore NPM Cache
        id: npm-cache-restore
        uses: actions/cache/restore@v4
        with:
          path: |
                ./node_modules
          key: v1-dependencies-${{ hashFiles('pnpm-lock.yaml') }}
          restore-keys: |
            v1-dependencies-

      - name: Install pnpm
        run: npm install -g pnpm

      - name: Authorize Github NPM
        run: npm set //npm.pkg.github.com/:_authToken ${{ secrets.GHP_ACCESS_TOKEN }}

      - name: Install Store Dependencies
        if: steps.npm-cache-restore.outputs.cache-hit != 'true'
        run: pnpm install

      - name: Install Puppeteer Dependencies
        run: pnpm node node_modules/puppeteer/install.mjs

      - name: Cache NPM Dependencies
        id: npm-cache-save
        uses: actions/cache/save@v4
        with:
          path: |
                ./node_modules
          key: v1-dependencies-${{ hashFiles('pnpm-lock.yaml') }}

      - name: Prisma Generate
        run: pnpm nx run store:prisma-generate --skip-nx-cache

      - name: Run Tests
        run: pnpm nx run-many --target=test --all --skip-nx-cache

  graphql-schema-check:
    name: Graphql Schema Check
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 2

      - name: Install GraphQL Hive
        run: npm install -g @graphql-hive/cli

      - name: Schema Check Dev
        if: (github.event_name != 'pull_request' || (github.event_name == 'pull_request' && contains(github.event.pull_request.labels.*.name, 'deploy-dev')))
        run: hive schema:check "apps/store/src/api/graphql/schema.graphql" --service store --token ${{ secrets.DEV_SCHEMA_TOKEN }}

      - name: Schema Check Staging
        if: (github.event_name != 'pull_request' || (github.event_name == 'pull_request' && contains(github.event.pull_request.labels.*.name, 'deploy-staging')))
        run: hive schema:check "apps/store/src/api/graphql/schema.graphql" --service store --token ${{ secrets.STAGING_SCHEMA_TOKEN }}

      - name: Schema Check Prod
        if: github.event_name == 'push'
        run: hive schema:check "apps/store/src/api/graphql/schema.graphql" --service store --token ${{ secrets.PROD_SCHEMA_TOKEN }}

  delete-stale-test-deployments:
    runs-on: ubuntu-latest
    name: Delete Stale Test Deployments
    needs: [tests]
    continue-on-error: true
    if: ${{ always() }}
    steps:
      - name: Extract branch name
        shell: bash
        run: echo "branch=${GITHUB_HEAD_REF:-${GITHUB_REF#refs/heads/}}" >> $GITHUB_OUTPUT
        id: extract_branch

      - name: Delete Stale Test Deployments
        uses: actions/github-script@v7
        with:
          script: |
            const deployments = await github.rest.repos.listDeployments({
              owner: context.repo.owner,
              repo: context.repo.repo,
              ref: "${{ steps.extract_branch.outputs.branch }}",
              environment: 'test'
            });
            console.log(deployments.data)

            const sortedDeployments = deployments.data.sort((a, b) => {
              const aCreatedAt = new Date(a.created_at);
              const bCreatedAt = new Date(b.created_at);
              return aCreatedAt - bCreatedAt;
            });

            sortedDeployments.pop();

            try {
              await Promise.all(
                sortedDeployments.map(async (deployment) => {
                  return github.rest.repos.deleteDeployment({
                    owner: context.repo.owner,
                    repo: context.repo.repo,
                    deployment_id: deployment.id
                  });
                })
              );
            } catch (error) {
              console.log(error);
            }
