name: Code Quality
on:
  pull_request:
    branches: ["main", "master"]
    types:
      - labeled
      - unlabeled
      - synchronize
      - opened
      - reopened
jobs:
  npm-install:
    name: NPM Install
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version-file: '.tool-versions'
      - uses: actions/cache@v4
        id: npm-cache
        with:
          path: node_modules
          key: ${{ runner.os }}-node-${{ hashFiles('pnpm-lock.yaml') }}
      - name: Install pnpm
        run: npm install -g pnpm
      - name: Authorize Github NPM
        run: npm set //npm.pkg.github.com/:_authToken ${{ secrets.GHP_ACCESS_TOKEN }}
      - name: Install dependencies
        if: steps.npm-cache.outputs.cache-hit != 'true'
        run: pnpm install
      - name: Check
        if: steps.npm-cache.outputs.cache-hit == 'true'
        run: echo "Cache Hit"
  code-quality:
    name: Code Quality Checks
    if: ${{ !contains(github.event.pull_request.labels.*.name, 'linting') }}
    needs: npm-install
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - uses: actions/setup-node@v4
        with:
          node-version-file: '.tool-versions'
      - uses: actions/cache@v4
        id: npm-cache
        with:
          path: node_modules
          key: ${{ runner.os }}-node-${{ hashFiles('pnpm-lock.yaml') }}
      - name: Install pnpm
        run: npm install -g pnpm
      - name: Fetch Base Branch to Allow `git diff`
        run: git fetch origin ${{ github.event.pull_request.base.ref }}:${{ github.event.pull_request.base.ref }}
      - name: Run ESLint
        env:
          ESLINT_PLUGIN_DIFF_COMMIT: ${{ github.event.pull_request.base.ref }}
          CI: true
        run: pnpm nx run-many --target=lint --all --skip-nx-cache
  code-coverage:
    name: Code Coverage
    if: ${{ !contains(github.event.pull_request.labels.*.name, 'linting') }}
    needs: npm-install
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - uses: actions/setup-node@v4
        with:
          node-version-file: '.tool-versions'
      - uses: actions/cache@v4
        id: npm-cache
        with:
          path: node_modules
          key: ${{ runner.os }}-node-${{ hashFiles('pnpm-lock.yaml') }}

      - name: Install pnpm
        run: npm install -g pnpm

      - name: Authorize Github NPM
        run: npm set //npm.pkg.github.com/:_authToken ${{ secrets.GHP_ACCESS_TOKEN }}

      - name: Install Puppeteer Dependencies
        run: pnpm node node_modules/puppeteer/install.mjs

      - run: git fetch origin ${{ github.event.pull_request.base.ref }}:${{ github.event.pull_request.base.ref }}

      - name: Prisma Generate
        run: pnpm nx run store:prisma-generate --skip-nx-cache

      - name: Run Test Coverage
        run: pnpm nx run-many --target=test --all --code-coverage --skip-nx-cache
