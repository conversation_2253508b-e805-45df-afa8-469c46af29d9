name: Deploy
on:
  push:
    branches: ["main"]
  pull_request:
    branches: ["main"]
    types: [labeled]
env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}
jobs:
  graphql-schema-publish:
    name: Graphql Schema Publish
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 2

      - name: Install GraphQL Hive
        run: npm install -g @graphql-hive/cli

      - name: Schema Publish Dev
        if: (github.event_name != 'pull_request' || (github.event_name == 'pull_request' && contains(github.event.pull_request.labels.*.name, 'deploy-dev')))
        run: hive schema:publish "apps/store/src/api/graphql/schema.graphql" --url ${{ secrets.SERVICE_URL }} --service store --token ${{ secrets.DEV_SCHEMA_TOKEN }} --author store-ci --commit ${{ github.sha }}

      - name: Schema Publish Staging
        if: (github.event_name != 'pull_request' || (github.event_name == 'pull_request' && contains(github.event.pull_request.labels.*.name, 'deploy-staging')))
        run: hive schema:publish "apps/store/src/api/graphql/schema.graphql" --url ${{ secrets.SERVICE_URL }} --service store --token ${{ secrets.STAGING_SCHEMA_TOKEN }} --author store-ci --commit ${{ github.sha }}
      - name: Schema Publish Prod
        if: github.event_name == 'push'
        run: hive schema:publish "apps/store/src/api/graphql/schema.graphql" --url ${{ secrets.SERVICE_URL }} --service store --token ${{ secrets.PROD_SCHEMA_TOKEN }} --author store-ci --commit ${{ github.sha }}

  build-dev:
    name: Publish Dev Image
    if: ${{ github.event_name != 'pull_request' || (github.event_name == 'pull_request' && contains(github.event.pull_request.labels.*.name, 'deploy-dev')) }}
    runs-on: ubuntu-latest
    needs: [graphql-schema-publish]
    environment: development
    permissions:
      contents: read
      packages: write
      id-token: write

    steps:
      - name: Generate build ID
        id: tag
        run: |
            sha=${GITHUB_SHA::8}
            ts=$(date +%s)
            echo "TAG=dev-${sha}-${ts}" >> $GITHUB_OUTPUT
      - name: Checkout repository
        uses: actions/checkout@v4
      - name: Setup Docker buildx
        uses: docker/setup-buildx-action@v3
      - name: Log into registry ${{ env.REGISTRY }}
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}
      - name: Extract Docker metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
          tags: |
            type=schedule
            type=ref,event=branch
            type=ref,event=pr
            type=semver,pattern={{version}}
            type=semver,pattern={{major}}.{{minor}}
            type=semver,pattern={{major}}
            type=raw,value=${{ steps.tag.outputs.TAG }}

      - name: Build and push image
        uses: docker/build-push-action@v5
        with:
          context: .
          build-args: |
            GHP_ACCESS_TOKEN=${{ secrets.GHP_ACCESS_TOKEN }}
            NEXT_PUBLIC_MEDUSA_PUBLISHABLE_KEY=${{ secrets.NEXT_PUBLIC_MEDUSA_PUBLISHABLE_KEY }}
            NEXT_PUBLIC_STRIPE_KEY=${{ secrets.NEXT_PUBLIC_STRIPE_KEY }}
            MEDUSA_BACKEND_URL=${{ secrets.MEDUSA_BACKEND_URL }}
          file: Dockerfile
          push: true
          tags: |
            ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
  build-staging:
    name: Publish Staging Image
    if: ${{ github.event_name != 'pull_request' || (github.event_name == 'pull_request' && contains(github.event.pull_request.labels.*.name, 'deploy-staging')) }}
    runs-on: ubuntu-latest
    needs: [graphql-schema-publish]
    environment: staging
    permissions:
      contents: read
      packages: write
      id-token: write
    steps:
      - name: Generate build ID
        id: tag
        run: |
            sha=${GITHUB_SHA::8}
            ts=$(date +%s)
            echo "TAG=staging-${sha}-${ts}" >> $GITHUB_OUTPUT
      - name: Checkout repository
        uses: actions/checkout@v4
      - name: Setup Docker buildx
        uses: docker/setup-buildx-action@v3
      - name: Log into registry ${{ env.REGISTRY }}
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}
      - name: Extract Docker metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
          tags: |
            type=schedule
            type=ref,event=branch
            type=ref,event=pr
            type=semver,pattern={{version}}
            type=semver,pattern={{major}}.{{minor}}
            type=semver,pattern={{major}}
            type=raw,value=${{ steps.tag.outputs.TAG }}
      - name: Build and push Docker image
        id: build-and-push
        uses: docker/build-push-action@v5
        with:
          context: .
          build-args: |
            GHP_ACCESS_TOKEN=${{ secrets.GHP_ACCESS_TOKEN }}
            NEXT_PUBLIC_MEDUSA_PUBLISHABLE_KEY=${{ secrets.NEXT_PUBLIC_MEDUSA_PUBLISHABLE_KEY }}
            NEXT_PUBLIC_STRIPE_KEY=${{ secrets.NEXT_PUBLIC_STRIPE_KEY }}
            MEDUSA_BACKEND_URL=${{ secrets.MEDUSA_BACKEND_URL }}
          file: Dockerfile
          push: true
          tags: |
            ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
  build-stable:
    name: Publish Stable Image
    if: ${{ github.event_name == 'push' }}
    runs-on: ubuntu-latest
    needs: [graphql-schema-publish]
    environment: production
    permissions:
      contents: read
      packages: write
      id-token: write
    steps:
      - name: Generate build ID
        id: tag
        run: |
            sha=${GITHUB_SHA::8}
            ts=$(date +%s)
            echo "TAG=stable-${sha}-${ts}" >> $GITHUB_OUTPUT
      - name: Checkout repository
        uses: actions/checkout@v4
      - name: Setup Docker buildx
        uses: docker/setup-buildx-action@v3
      - name: Log into registry ${{ env.REGISTRY }}
        if: github.event_name != 'pull_request'
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}
      - name: Extract Docker metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
          tags: |
            type=schedule
            type=ref,event=branch
            type=ref,event=pr
            type=semver,pattern={{version}}
            type=semver,pattern={{major}}.{{minor}}
            type=semver,pattern={{major}}
            type=raw,value=${{ steps.tag.outputs.TAG }}
      - name: Build and push Docker image
        id: build-and-push
        uses: docker/build-push-action@v5
        with:
          context: .
          build-args: |
            GHP_ACCESS_TOKEN=${{ secrets.GHP_ACCESS_TOKEN }}
            NEXT_PUBLIC_MEDUSA_PUBLISHABLE_KEY=${{ secrets.NEXT_PUBLIC_MEDUSA_PUBLISHABLE_KEY }}
            NEXT_PUBLIC_STRIPE_KEY=${{ secrets.NEXT_PUBLIC_STRIPE_KEY }}
            MEDUSA_BACKEND_URL=${{ secrets.MEDUSA_BACKEND_URL }}
            NODE_ENV=production
          file: Dockerfile
          push: ${{ github.event_name != 'pull_request' }}
          tags: |
            ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
