name: Git Hygiene
on:
  pull_request:
    branches: ["main", "master"]
jobs:
  lint-pr-name:
    name: <PERSON><PERSON>ull Request Name
    runs-on: ubuntu-latest
    steps:
      - uses: actions/github-script@v7
        env:
          prTitle: "${{github.event.pull_request.title}}"
          prLabels: "${{toJson(github.event.pull_request.labels.*.name)}}"
        with:
          result-encoding: string
          script: |
            const { prTitle, prLabels } = process.env
            console.log(JSON.stringify(prTitle))
            console.log(JSON.stringify(prLabels))
            const prStartsWithTicket = /^[A-Z]{2,}-\d{1,4}\s[A-Z]/.test(prTitle)
            const prCreatedBySnyk = /^\[Snyk\]/.test(prTitle)
            console.log()
            if (!(prStartsWithTicket || prCreatedBySnyk)) {
              core.setFailed(
                `Your Github Pull Request must be titled with a Jira ticket number and provide at least a 4-word ` +
                `description with no extra brackets, dashes, colons, or spaces. Valid example, ` +
                `"XXX-456 Describe an awesome change". Please rename your Github Pull Request title to resolve.`
              )
            }
  lint-branch-name:
    name: Lint Branch Name
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/github-script@v7
        with:
          result-encoding: string
          script: |
            // Regex Validates for format: PROJ-123-an-example-branch-name
            //   * Jira project code: 2 or more capital letters
            //   * Jira ticket number: Between 1 and 4 digits
            //   * Seperated by dashes, no underscores
            //   * Numbers may be in the branch name as well
            //   * All lowercase
            const validateBranchName = (branchName) => {
              const branchFormat = /^[A-Z]{2,}-\d{1,4}-(?:[a-z0-9\-]+-)*[a-z0-9\-]+$/
              const branchFormatFromSnyk = /^snyk-upgrade-[a-z0-9]+$/
              if (!(branchFormat.test(branchName) || branchFormatFromSnyk.test(branchName))) {
                core.setFailed(
                  `Branch names in PRs must be in the format of XXX-456-short-description-here2, ` +
                  `where XXX-456 references a corresponding Jira ticket. ` +
                  `See https://bit.ly/3DEGPGw to resolve issue`
                )
              }
            }
            // Regex Validates for invalid Gitflow names:
            //   * Starts with feat/
            //   * Starts with feature/
            //   * Starts with chore/
            //   * Starts with fix/
            //   * Contains feat-
            //
            const preventGitFlow = (branchName) => {
              const gitFlowFormat = /(feat\W|feature\/|fix\/|chore\/)/
              if (gitFlowFormat.test(branchName)) {
                core.setFailed(
                  `Branch names in PRs should not use Gitflow. e.g. (feat, fix, chore) ` +
                  `See https://bit.ly/3DEGPGw to resolve issue`
                )
              }
            }
            const currentBranch = context.payload.pull_request.head.ref
            validateBranchName(currentBranch)
            preventGitFlow(currentBranch)
  checkout-pr-commits:
    name: Checkout PR Commits
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
          ref: main
      - id: checkout1
        uses: actions/github-script@v7
        env:
          branch: "${{github.head_ref || github.event.pull_request.head.sha}}"
        with:
          result-encoding: string
          script: |
            const { branch } = process.env
            const checkout = await exec.getExecOutput('git', ['checkout', '--track', `origin/${branch}`])
            const output = await exec.getExecOutput('git', ['log', `main..${branch}`, '--format=format:%h'])
            const gitShas = output.stdout.split(/\n/)
            const gitMessages = await Promise.all(gitShas.map(async (sha) => {
              const messageInfo = await exec.getExecOutput('git', ['show', '--format=format:%h:|:%s:|:%b:|:%ai', '-s', sha])
              return messageInfo.stdout
            }))
            core.setOutput('gitMessages', gitMessages);
    outputs:
      gitMessages: ${{ steps.checkout1.outputs.gitMessages }}
  lint-ticket-number:
    name: Lint Ticket Number
    needs: checkout-pr-commits
    runs-on: ubuntu-latest
    steps:
      - uses: actions/github-script@v7
        env:
          gitMessages: "${{needs.checkout-pr-commits.outputs.gitMessages}}"
        with:
          result-encoding: string
          script: |
            const { gitMessages: gitMessageInput } = process.env
            const gitMessages = JSON.parse(gitMessageInput).map(msg => msg.split(":|:"))
            const invalidMessages = gitMessages.map((message) => {
              const [sha, title, description] = message
              let startsWithTicket = /^[A-Z]{2,}-\d{1,4}\s[^\W\s]/.test(title)
              if (/revert|merge/.test(title.toLowerCase())) {
                startsWithTicket = true
              }
              if(title.startsWith("fix: upgrade")) {
                startsWithTicket = true
              }
              return [sha, startsWithTicket]
            }).filter((result) => {
              return !result[1]
            }).map(result => result[0])
            console.log()
            if (invalidMessages.length > 0) {
              core.setFailed(
                `Git commit titles must start with a Jira ticket number and provide at least a 4-word ` +
                `description with no extra brackets, dashes, colons, or spaces. Valid example, ` +
                `"XXX-456 Describe an awesome change". See https://bit.ly/3BY2SGN to resolve. ` +
                `The following commits do not meet this criteria: ${invalidMessages}`)
            } else {
              console.log("Commit messages are in the correct format.")
            }
  lint-commit-titles:
    name: Lint Commit Titles
    needs: checkout-pr-commits
    runs-on: ubuntu-latest
    steps:
      - uses: actions/github-script@v7
        env:
          gitMessages: "${{needs.checkout-pr-commits.outputs.gitMessages}}"
        with:
          result-encoding: string
          script: |
            const NON_IMPERATIVE_WORDS = [
              'adds', 'added', 'creates', 'created',
              'updates', 'updated', 'fixes', 'fixed',
              'moves', 'moved', 'changes', 'changed',
              'removes', 'removed', 'uses', 'used'
            ]
            const validations = {
              validCapitalization: title => /^[A-Z]/.test(title),
              validLength: (subject) => subject.length <= 72 || /revert|merge/.test(subject.toLowerCase()),
              validPunctuation: title => /[^\.\?\s]/.test(title[title.length - 1]),
              validDescriptiveTitle: title => title.split(/\s/).length >= 4,
              validMood: (title) => {
                firstWord = title.toLowerCase().split(/\s/)[0]
                return !NON_IMPERATIVE_WORDS.some((w) => {
                  return (new RegExp(`${w}`)).test(firstWord)
                })
              },
              validTrunkMessage: (title) => !/(feat[:\s]|fix:|style:|chore)/.test(title)
            }
            const { gitMessages: gitPayload } = process.env
            const gitMessages = JSON.parse(gitPayload).map(msg => msg.split(":|:"))
            const validationResults = gitMessages.map((message) => {
              const [sha, subject, _description] = message
              if (subject.startsWith("fix: upgrade")) return false;
              const title = subject.split(/^[A-Z]{2,}-\d{1,4}\s/)[1] || subject
              return [
                sha,
                validations.validCapitalization(title),
                validations.validLength(subject),
                validations.validPunctuation(title),
                validations.validDescriptiveTitle(title),
                validations.validMood(title),
                validations.validTrunkMessage(title)
              ]
            }).filter(Boolean)
            const invalidCapitalizationShas = validationResults.filter(r => !r[1]).map(r => r[0])
            const invalidLengthShas         = validationResults.filter(r => !r[2]).map(r => r[0])
            const invalidPunctuationShas    = validationResults.filter(r => !r[3]).map(r => r[0])
            const invalidDescriptiveTitles  = validationResults.filter(r => !r[4]).map(r => r[0])
            const invalidMoodShas           = validationResults.filter(r => !r[5]).map(r => r[0])
            const invalidTrunkShas          = validationResults.filter(r => !r[6]).map(r => r[0])
            if (invalidCapitalizationShas.length > 0) {
              core.setFailed(
                `Commit titles must be capitalized and use proper grammar. See https://bit.ly/3BY2SGN to resolve. ` +
                `The following commits do not meet this criteria: ${invalidCapitalizationShas}`
              )
            }
            if (invalidLengthShas.length > 0) {
              core.setFailed(
                `Commit titles must be 72 characters or less. Consider ` +
                `shortening the title and writing more in the description. See https://bit.ly/3BY2SGN to resolve. ` +
                `The following commits do not meet this criteria: ${invalidLengthShas}`
              )
            }
            if (invalidPunctuationShas.length > 0) {
              core.setFailed(
                `Commit titles should not end with punctuation or spaces. See https://bit.ly/3BY2SGN to resolve. ` +
                `The following commits do not meet this criteria: ${invalidPunctuationShas}`
              )
            }
            if (invalidDescriptiveTitles.length > 0) {
              core.setFailed(
                `Commit titles must provide atleast a 4-word description. See https://bit.ly/3BY2SGN to resolve. ` +
                `The following commits do not meet this criteria: ${invalidDescriptiveTitles}`
              )
            }
            if (invalidMoodShas.length > 0) {
              core.setFailed(
                `Commits should be written in the imperative mood, describing the commit in ` +
                `present tense, second person. Commits should not describe what the author did ` +
                `or use third person. For example, "Create financial module" not "Created/Creates ` +
                `financial module". See https://bit.ly/3BY2SGN to resolve. The following commits ` +
                `do not meet this criteria: ${invalidMoodShas}`
              )
            }
            if (invalidTrunkShas.length > 0) {
              core.setFailed(
                `Commit titles should not use 'Conventional Commit' terminology or use emojis like ` +
                `'feat', 'chore', or 'fix'. See https://bit.ly/3BY2SGN to resolve. The following ` +
                `commits do not meet this criteria: ${invalidTrunkShas}`
              )
            }
  lint-commit-descriptions:
    name: Lint Commit Descriptions
    needs: checkout-pr-commits
    runs-on: ubuntu-latest
    steps:
      - uses: actions/github-script@v7
        env:
          gitMessages: "${{needs.checkout-pr-commits.outputs.gitMessages}}"
        with:
          result-encoding: string
          script: |
            const { gitMessages: gitMessageInput } = process.env
            const gitMessages = JSON.parse(gitMessageInput).map(msg => msg.split(":|:"))
            const commitDates = gitMessages.map(message => message[3])
            const { 0 : latestCommit, [commitDates.length - 1] : earliestCommit} = commitDates;
            const prAge = (new Date(latestCommit) - new Date(earliestCommit)) / 1000 / 60
            const prOlderThan24Hrs = prAge >= 1440
              const validationResults = gitMessages.map((message) => {
              const [sha, subject, description, authorDateStr] = message
              const authorDate = new Date(authorDateStr)
              let validLength = true
              let validPunctuation = true
              let validFix = true
              const title = subject.split(/^[A-Z]{2,}-\d{1,4}\s/)[1] || subject
              if (/[Ff]ix(?!\s+[Tt]ypo)/.test(title) && !description) {
                validFix = false
              }
              if (description) {
                if (/```/.test(description)) {
                  validLength = true
                } else {
                  lines = description.split(/\n/).filter((line) => {
                    return line && !/http|www/.test(line.toLowerCase()) && !/^\s{2,}/.test(line) && !/`/.test(line)
                  })
                  validLength = lines.every(l => l.length <= 78)
                }
                validPunctuation = /[A-Z][\S\s]+\./.test(description)
              }
              return [sha, validLength, validPunctuation, validFix]
            })
            const invalidLengthShas = validationResults.filter(r => !r[1]).map(r => r[0])
            const invalidPunctuationShas = validationResults.filter(r => !r[2]).map(r => r[0])
            const invalidFixShas = validationResults.filter(r => !r[3]).map(r => r[0])
            console.log()
            if (invalidLengthShas.length > 0) {
              core.setFailed(
                `Commit descriptions must be wrapped at 72 characters. See https://bit.ly/3BY2SGN to resolve. ` +
                `The following commits do not meet this criteria: ${invalidLengthShas}`
              )
            }
            if (invalidPunctuationShas.length > 0) {
              core.setFailed(
                `Commit descriptions must use proper capitalization, punctuation, and grammar. See ` +
                `https://bit.ly/3BY2SGN to resolve. The following commits do not meet ` +
                `this criteria: ${invalidPunctuationShas}`
              )
            }
            if (invalidFixShas.length > 0) {
              core.setFailed(
                `Commits titled "Fix" must include a description describing what was previously broken, ` +
                `what the desired behavior is, and why this change fixes it. See https://bit.ly/3BY2SGN ` +
                `to resolve. The following commits do not meet this criteria: ${invalidFixShas}`
              )
            }
            if (prOlderThan24Hrs && gitMessages.map(m => m[2]).every(m => !m)) {
              core.setFailed(
                `PRs that have commits spanning longer than 24 hours must have a description on at least ` +
                `1 commit. See https://bit.ly/3BY2SGN to resolve.`
              )
            }
