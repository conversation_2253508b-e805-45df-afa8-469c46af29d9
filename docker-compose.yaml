version: '3.8'
services:
  store:
    extends:
      file: docker-compose.base.yaml
      service: nxapp-backend-base
    environment:
      - TEMPORAL_URL=temporal:7233
      - TASK_QUEUE=store
    volumes:
      - .:/node_modules
    ports:
      - 3000:3000
    networks:
      - snap
      - temporal-network

  store-worker:
    extends:
      file: docker-compose.base.yaml
      service: nxapp-backend-base
    command: pnpm run worker:start
    environment:
      - TEMPORAL_URL=temporal:7233
      - TASK_QUEUE=store
    volumes:
      - .:/node_modules
    networks:
      - snap
      - temporal-network

  store-db:
    image: postgres:13
    restart: unless-stopped
    environment:
      POSTGRES_USER: root
      POSTGRES_PASSWORD: password
      POSTGRES_DB: store
    ports:
      - '5444:5432'
    volumes:
      - postgres-data:/var/lib/postgresql/data
    networks:
      - snap
      - temporal-network

  # temporal-web:
  #   image: temporalio/web:1.13.0
  #   ports:
  #     - "8088:8088"
  #   environment:
  #     - TEMPORAL_GRPC_ENDPOINT=temporal:7233
  #   depends_on:
  #     - temporal

volumes:
  postgres-data:

networks:
  snap:

    driver: bridge
    name: snap
  temporal-network:
    external: true
    driver: bridge
    name: temporal-network
