{"name": "medusa-store-storefront", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/medusa-store-storefront/src", "projectType": "application", "tags": [], "targets": {"build": {"executor": "nx:run-commands", "options": {"command": "next build", "cwd": "apps/medusa-store-storefront"}}, "dev": {"executor": "nx:run-commands", "options": {"command": "npx next dev --turbopack -p 8000", "cwd": "apps/medusa-store-storefront"}}, "serve": {"executor": "nx:run-commands", "options": {"command": "npx next start", "cwd": "apps/medusa-store-storefront"}}}}