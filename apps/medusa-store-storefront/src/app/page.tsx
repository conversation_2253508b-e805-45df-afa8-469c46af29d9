import { Metadata } from "next";

import Hero from "@modules/directory/components/hero";
import News from "@modules/directory/components/news";
import ProductFeed from "@modules/directory/components/product-feed";
import States from "@modules/directory/components/states";
import Features from "@modules/directory/components/features";
import DirectoryLayout from "@modules/layout/templates/directory-layout";

export const metadata: Metadata = {
  title: "Snap! Store Home Page",
  description:
    "Support your team and show your pride! Browse official gear from your school, club, or organization."
};

export default async function HomeDirectory() {
  return (
    <DirectoryLayout>
      <div className="flex-grow">
        <Hero />
        <News />
        <ProductFeed />
        <div className="bg-gray-50 py-20 mb-20">
          <States />
        </div>
        <Features />
      </div>
    </DirectoryLayout>
  );
}
