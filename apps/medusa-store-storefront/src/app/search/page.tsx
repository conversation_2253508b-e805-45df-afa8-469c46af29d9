import { Suspense } from 'react';
import { Metadata } from 'next';

import StoreSearchForm from '@modules/directory/components/store-search-form';
import StoreSearchResult from '@modules/directory/components/store-search-result';
import Pagination from '@modules/directory/components/pagination';

import { StoreSearchCriteria } from '@lib/hooks/use-store-search';
import { storeSearch } from '@lib/data/stores';

export const metadata: Metadata = {
  title: 'Snap! Store Home Page',
  description:
    'Support your team and show your pride! Browse official gear from your school, club, or organization.'
};

export type SearchDirectoryProps = {
  searchParams: Promise<StoreSearchCriteria>
};

const STORES_PAGE_SIZE = 24;

export default async function SearchDirectory({ searchParams }: SearchDirectoryProps) {
  const { name, state, page = 0 } = await searchParams;
  const searchResult = await storeSearch(
    { name, state },
    { limit: STORES_PAGE_SIZE, offset: STORES_PAGE_SIZE * page }
  );

  return (
    <div className="flex-grow mb-6">
      <section className="bg-[#09224C] text-white mb-8">
        <h1 className="content-container text-2xl font-semibold py-8">Find Your Store</h1>
      </section>

      <section className="content-container text-gray-900 mb-10">
        <h2 className="text-2xl font-semibold mb-4">Custom Spirit Wear for Every Program</h2>
        <p className="mb-4 text-lg">
          Explore custom spirit wear for all of our athletic teams and academic programs. Find your
          store to shop personalized gear, show your support, and rep your community with pride.
        </p>

        <Suspense>
          <StoreSearchForm />
        </Suspense>

        <h2 className="my-6 text-xl font-semibold">Store Directory</h2>

        <Suspense>
          <Pagination position="top"
                      itemCount={searchResult?.total}
                      pageSize={STORES_PAGE_SIZE}
                      currentPage={page} />
        </Suspense>

        <StoreSearchResult stores={searchResult?.stores} />

        <Suspense>
          <Pagination position="bottom"
                      itemCount={searchResult?.total}
                      pageSize={STORES_PAGE_SIZE}
                      currentPage={page} />
        </Suspense>
      </section>
    </div>
  );
}
