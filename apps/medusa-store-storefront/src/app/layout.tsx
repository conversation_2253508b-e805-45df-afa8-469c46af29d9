/* eslint-disable @next/next/no-sync-scripts */
/* eslint-disable import/group-exports */
import { Metadata } from "next";

import { getBaseURL } from "@lib/util/env";

import "styles/directory.css";

export const metadata: Metadata = {
  metadataBase: new URL(getBaseURL()),
  title: "Snap Store",
  description: "Snap Store",
  icons: [
    { rel: "icon", url: "/favicon.ico" },
    { rel: "apple-touch-icon", url: "/apple-touch-icon.png" }
  ],
  openGraph: {
    type: "website",
    url: "https://store.snap.app",
    title: "Snap! Store Home Page",
    description: "Support your team and show your pride! Browse official gear from your school, club, or organization.",
    siteName: "Snap! Store Home Page",
    images: [{
      url: "https://store.snap.app/assets/images/directory/opengraph-image.png",
      width: 1200,
      height: 630
    }]
  },
  twitter: {
    images: "https://store.snap.app/assets/images/directory/twitter-image.png"
  }
};

export default async function DirectoryRootLayout(props: { children: React.ReactNode }) {
  return (
    <html lang="en" data-mode="light" className="h-full">
      <head>
        <link rel="preconnect" href="https://ui.snapraise.com/" />
        <link rel="stylesheet" href="https://ui.snapraise.com/v25/css/snap-ui.min.css" />
        <script type="module" src="https://ui.snapraise.com/v25/build/snap-ui.esm.js"></script>
        <script noModule src="https://ui.snapraise.com/v25/build/snap-ui.js"></script>
      </head>
      <body className="h-[100dvh]">
        {props.children}
      </body>
    </html>
  );
}
