import React from "react";
import Header from "@modules/directory/templates/header";
import Footer from "@modules/directory/templates/footer";
import FullStory from "@modules/fullstory/components/fullstory";

export default function DirectoryLayout({ children }: { children: React.ReactNode }) {
  return (
    <main className="relative">
      <Header />
      {children}
      <Footer />
      <FullStory />
    </main>
  );
}