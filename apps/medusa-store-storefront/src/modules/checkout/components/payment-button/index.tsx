/* eslint-disable import/no-internal-modules */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable max-depth */
/* eslint-disable max-statements */
/* eslint-disable sonarjs/no-identical-functions */
/* eslint-disable max-lines */
/* eslint-disable sonarjs/cognitive-complexity */
/* eslint-disable id-denylist */
/* eslint-disable functional/no-return-void */
/* eslint-disable max-lines-per-function */
/* eslint-disable import/no-relative-parent-imports */
/* eslint-disable complexity */
"use client";

import { Button } from "@medusajs/ui";
import { placeOrder } from "@lib/data/cart";
import { useRouter } from "next/navigation";
import { useState } from "react";
import Constants from "@lib/constants";
import ErrorMessage from "../error-message";
import StripePaymentButton from "../stripe";

type PaymentButtonProps = {
  cart: any
  "data-testid": string
}

const PaymentButton: React.FC<PaymentButtonProps> = ({
  cart,
  "data-testid": dataTestId,
}) => {
  const notReady =
    !cart ||
    !cart.shipping_address ||
    !cart.billing_address ||
    !cart.email ||
    (cart.shipping_methods?.length ?? 0) < 1;

  const paymentSession = cart.payment_collection?.payment_sessions?.[0];

  switch (true) {
  case Constants.isStripe(paymentSession?.provider_id):
    return (
      <StripePaymentButton
        notReady={notReady}
        cart={cart}
        data-testid={dataTestId}
      />
    );
  case Constants.isManual(paymentSession?.provider_id):
    return (
      <ManualTestPaymentButton
        notReady={notReady}
        cart={cart}
        data-testid={dataTestId}
      />
    );
  default:
    return <Button disabled>Select a payment method</Button>;
  }
};

const ManualTestPaymentButton = ({
  notReady,
  "data-testid": dataTestId,
}: {
  notReady: boolean
  cart: any
  "data-testid": string
}) => {
  const [submitting, setSubmitting] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const router = useRouter();

  const handlePayment = async () => {
    setSubmitting(true);
    setErrorMessage(null);

    try {
      const data = await placeOrder();
      if (data && data.id) {
        router.push(`/order/confirmed/${data.id}`);
      } else {
        setErrorMessage("Unable to complete order, please try again");
      }
    } catch (error: any) {
      setErrorMessage(
        error.message || "An unexpected error occurred. Please try again."
      );
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <>
      <Button
        disabled={notReady}
        isLoading={submitting}
        onClick={handlePayment}
        size="large"
        data-testid={dataTestId}
      >
        Place order
      </Button>
      <ErrorMessage
        error={errorMessage}
        data-testid="manual-payment-error-message"
      />
    </>
  );
};

export default PaymentButton;
