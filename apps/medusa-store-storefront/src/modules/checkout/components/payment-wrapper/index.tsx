/* eslint-disable functional/no-return-void */
"use client";

import { loadStripe, Stripe } from "@stripe/stripe-js";
import React, { useEffect, useState } from "react";
import { HttpTypes, StorePaymentSession } from "@medusajs/types";
import Constants from "@lib/constants";
import StripeWrapper, { StripeContext } from "./stripe-wrapper";

type PaymentWrapperProps = {
  cart: HttpTypes.StoreCart
  children: React.ReactNode
}

const PaymentWrapper: React.FC<PaymentWrapperProps> = ({ cart, children }) => {
  const [isClient, setIsClient] = useState(false);
  const [paymentSess, setPaymentSession] = useState<StorePaymentSession | undefined>();
  const [isStripeSess, setIsStripeSession] = useState(false);
  const [stripeKey, setStripeKey] = useState<string|undefined>();
  const [stripePromise, setPromise] = useState<Promise<Stripe|null>|null>();

  useEffect(() => {
    setIsClient(true);
    setStripeKey(process.env.NEXT_PUBLIC_STRIPE_KEY);
  }, []);

  useEffect(() => {
    if (stripeKey) {
      setPromise(loadStripe(stripeKey));
    }
  }, [stripeKey]);

  useEffect(() => {
    setPaymentSession(cart.payment_collection?.payment_sessions?.find((s) => s.status === "pending"));
  }, [cart]);

  useEffect(() => {
    const sessionCheck = Constants.isStripe(paymentSess?.provider_id) &&
      paymentSess !== undefined &&
      stripePromise !== null;
    setIsStripeSession(sessionCheck);
  }, [paymentSess, stripeKey, stripePromise]);
  if (
    isClient && isStripeSess && stripePromise
  ) {
    console.log("StripeWrapper used");
    return (
      <StripeWrapper
        paymentSession={paymentSess!}
        stripeKey={stripeKey}
        stripePromise={stripePromise}
      >
        {children}
      </StripeWrapper>
    );
  }
  // Provide a default context value when Stripe isn't being used
  return (
    <StripeContext.Provider value={{ isReady: false }}>
      {children}
    </StripeContext.Provider>
  );
};

export default PaymentWrapper;
