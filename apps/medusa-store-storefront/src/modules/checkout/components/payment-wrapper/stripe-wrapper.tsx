/* eslint-disable import/group-exports */
"use client";

import { Stripe } from "@stripe/stripe-js";
import { Elements } from "@stripe/react-stripe-js";
import { HttpTypes } from "@medusajs/types";
import { createContext, useContext } from "react";
import Constants from "@lib/constants";

type StripeWrapperProps = {
  paymentSession: HttpTypes.StorePaymentSession
  stripeKey?: string
  stripePromise: Promise<Stripe | null> | null
  children: React.ReactNode
}

// Enhance the context to provide more information
type StripeContextType = {
  isReady: boolean;
  clientSecret?: string;
}

export const StripeContext = createContext<StripeContextType>({ isReady: false });

// Create a hook for easier consumption of the context
export const useStripeContext = () => useContext(StripeContext);

const StripeWrapper: React.FC<StripeWrapperProps> = ({
  paymentSession,
  stripeKey,
  stripePromise,
  children,
}) => {
  if (!Constants.isStripe(paymentSession.provider_id)) {
    return null;
  }
  const clientSecret = paymentSession?.data?.client_secret as string | undefined;

  if (!stripeKey) {
    throw new Error(
      "Stripe key is missing. Set NEXT_PUBLIC_STRIPE_KEY environment variable."
    );
  }

  if (!stripePromise) {
    throw new Error(
      "Stripe promise is missing. Make sure you have provided a valid Stripe key."
    );
  }

  if (!clientSecret) {
    throw new Error(
      "Stripe client secret is missing. Cannot initialize Stripe."
    );
  }
  return (
    <StripeContext.Provider value={{ isReady: true, clientSecret }}>
      <Elements options={{
        clientSecret,
      }} stripe={stripePromise}>
        {children}
      </Elements>
    </StripeContext.Provider>
  );
};

export default StripeWrapper;
