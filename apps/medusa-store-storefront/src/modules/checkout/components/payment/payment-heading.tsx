/* eslint-disable functional/no-return-void */
/* eslint-disable functional/no-mixed-types */
"use client";

import { CheckCircleSolid } from "@medusajs/icons";
import { Heading, Text, clx } from "@medusajs/ui";

type PaymentHeadingProps = {
  isOpen: boolean;
  paymentReady: boolean;
  handleEdit: () => void;
};

const PaymentHeading = ({ isOpen, paymentReady, handleEdit }: PaymentHeadingProps) => {
  return (
    <div className="flex flex-row items-center justify-between mb-6">
      <Heading
        level="h2"
        className={clx(
          "flex flex-row text-3xl-regular gap-x-2 items-baseline",
          {
            "opacity-50 pointer-events-none select-none":
              !isOpen && !paymentReady,
          }
        )}
      >
        Payment
        {!isOpen && paymentReady && <CheckCircleSolid />}
      </Heading>
      {!isOpen && paymentReady && (
        <Text>
          <button
            onClick={handleEdit}
            className="text-ui-fg-interactive hover:text-ui-fg-interactive-hover"
            data-testid="edit-payment-button"
          >
            Edit
          </button>
        </Text>
      )}
    </div>
  );
};

export default PaymentHeading;
