/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import { CreditCard } from "@medusajs/icons";
import { Container, Text } from "@medusajs/ui";
import Constants from "@lib/constants";

type PaymentSummaryProps = {
  cart: any;
  paymentReady: boolean;
  activeSession: any;
  selectedPaymentMethod: string;
  cardBrand: string;
  cardPostal: string;
  paidByGiftcard: boolean;
};

const PaymentSummary = ({
  cart,
  paymentReady,
  activeSession,
  selectedPaymentMethod,
  cardBrand,
  cardPostal,
  paidByGiftcard,
}: PaymentSummaryProps) => {
  if (paidByGiftcard) {
    return <GiftCardPaymentSummary />;
  }

  if (cart && paymentReady && activeSession) {
    return <ActivePaymentSummary
      selectedPaymentMethod={selectedPaymentMethod}
      cardBrand={cardBrand}
      cardPostal={cardPostal}
    />;
  }

  return null;
};

const GiftCardPaymentSummary = () => (
  <div className="flex flex-col w-1/3">
    <Text className="txt-medium-plus text-ui-fg-base mb-1">
      Payment method
    </Text>
    <Text
      className="txt-medium text-ui-fg-subtle"
      data-testid="payment-method-summary"
    >
      Gift card
    </Text>
  </div>
);

const ActivePaymentSummary = ({
  selectedPaymentMethod,
  cardBrand,
  cardPostal,
}: Pick<PaymentSummaryProps, "selectedPaymentMethod" | "cardBrand" | "cardPostal">) => (
  <div className="flex items-start gap-x-1 w-full">
    <div className="flex flex-col w-1/3">
      <Text className="txt-medium-plus text-ui-fg-base mb-1">
        Payment method
      </Text>
      <Text
        className="txt-medium text-ui-fg-subtle"
        data-testid="payment-method-summary"
      >
        {Constants.paymentInfoMap[selectedPaymentMethod]?.title ||
          selectedPaymentMethod}
      </Text>
    </div>
    <PaymentDetailsSummary
      selectedPaymentMethod={selectedPaymentMethod}
      cardBrand={cardBrand}
      cardPostal={cardPostal}
    />
  </div>
);

const PaymentDetailsSummary = ({
  selectedPaymentMethod,
  cardBrand,
  cardPostal,
}: Pick<PaymentSummaryProps, "selectedPaymentMethod" | "cardBrand" | "cardPostal">) => (
  <div className="flex flex-col w-1/3">
    <Text className="txt-medium-plus text-ui-fg-base mb-1">
      Payment details
    </Text>
    <div
      className="flex gap-2 txt-medium text-ui-fg-subtle items-center"
      data-testid="payment-details-summary"
    >
      <Container className="flex items-center h-7 w-fit p-2 bg-ui-button-neutral-hover">
        {Constants.paymentInfoMap[selectedPaymentMethod]?.icon || (
          <CreditCard />
        )}
      </Container>
      <Text>
        {Constants.isStripe(selectedPaymentMethod) && cardBrand
          ? `${cardBrand} (${cardPostal})`
          : "Card details entered"}
      </Text>
    </div>
  </div>
);

export default PaymentSummary;
