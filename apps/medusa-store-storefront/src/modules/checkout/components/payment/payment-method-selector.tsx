/* eslint-disable import/no-relative-parent-imports */
/* eslint-disable import/no-internal-modules */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable functional/no-return-void */
/* eslint-disable functional/no-mixed-types */
"use client";

import { RadioGroup } from "@headlessui/react";
import { CardElement } from "@stripe/react-stripe-js";
import { Text } from "@medusajs/ui";
import PaymentContainer from "@modules/checkout/components/payment-container";
import Constants from "@lib/constants";
import { useStripeContext } from "../payment-wrapper/stripe-wrapper";

type PaymentMethodSelectorProps = {
  availablePaymentMethods: any[];
  selectedPaymentMethod: string;
  setSelectedPaymentMethod: (value: string) => void;
  stripeReady: boolean;
  handleCardChange: (event: any) => void;
  cardError: string | null;
  cart: any;
};

const StripeCardSection = ({
  handleCardChange,
  cardError
}: {
  stripeReady: boolean;
  handleCardChange: (event: any) => void;
  cardError: string | null;
}) => {
  const { isReady } = useStripeContext();

  if (!isReady) return null;

  return (
    <div className="mt-5 transition-all duration-150 ease-in-out">
      <Text className="txt-medium-plus text-ui-fg-base mb-1">
        Enter your card details:
      </Text>
      <div className="bg-ui-bg-field border border-ui-border-base rounded-md p-3">
        <CardElement
          options={{
            style: {
              base: {
                fontSize: "16px",
                color: "#424770",
                "::placeholder": {
                  color: "#aab7c4",
                },
              },
              invalid: {
                color: "#9e2146",
              },
            },
          }}
          onChange={handleCardChange}
        />
      </div>
      {cardError && (
        <Text className="text-red-500 text-sm mt-1">
          {cardError}
        </Text>
      )}
    </div>
  );
};

const PaymentMethodSelector = ({
  availablePaymentMethods,
  selectedPaymentMethod,
  setSelectedPaymentMethod,
  stripeReady,
  handleCardChange,
  cardError,
  cart,
}: PaymentMethodSelectorProps) => {
  return (
    <>
      <RadioGroup
        value={selectedPaymentMethod}
        onChange={(value: string) => setSelectedPaymentMethod(value)}
      >
        {availablePaymentMethods.map((paymentMethod) => (
          <PaymentContainer
            paymentInfoMap={Constants.paymentInfoMap}
            paymentProviderId={paymentMethod.id}
            key={paymentMethod.id}
            selectedPaymentOptionId={selectedPaymentMethod}
            cart={cart}
          />
        ))}
      </RadioGroup>

      {Constants.isStripe(selectedPaymentMethod) && (
        <StripeCardSection
          stripeReady={stripeReady}
          handleCardChange={handleCardChange}
          cardError={cardError}
        />
      )}
    </>
  );
};

export default PaymentMethodSelector;
