/* eslint-disable complexity */
/* eslint-disable import/no-relative-parent-imports */
/* eslint-disable import/max-dependencies */
/* eslint-disable import/no-internal-modules */
/* eslint-disable no-nested-ternary */
/* eslint-disable max-lines */
/* eslint-disable functional/no-return-void */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable max-lines-per-function */
"use client";

import { Button } from "@medusajs/ui";
import Constants from "@lib/constants";
import { initiatePaymentSession } from "@lib/data/cart";
import { useStripeContext } from "@modules/checkout/components/payment-wrapper/stripe-wrapper";
import ErrorMessage from "@modules/checkout/components/error-message";
import Divider from "@modules/common/components/divider";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { useCallback, useEffect, useState } from "react";

// Import new components
import PaymentHeading from "./payment-heading";
import PaymentMethodSelector from "./payment-method-selector";
import PaymentSummary from "./payment-summary";

const Payment = ({
  cart,
  availablePaymentMethods,
}: {
  cart: any
  availablePaymentMethods: any[]
}) => {
  const activeSession = cart.payment_collection?.payment_sessions?.find(
    (paymentSession: any) => paymentSession.status === "pending"
  );

  const [cardBrand, setCardBrand] = useState("");
  const [cardPostal, setCardPostal] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState(
    activeSession?.provider_id ?? ""
  );
  // Add state to track card validation
  const [cardComplete, setCardComplete] = useState(false);
  const [cardError, setCardError] = useState<string | null>(null);

  const searchParams = useSearchParams();
  const router = useRouter();
  const pathname = usePathname();

  const isOpen = searchParams.get("step") === "payment";


  const { isReady: stripeReady } = useStripeContext();

  const paidByGiftcard =
    cart?.gift_cards && cart?.gift_cards?.length > 0 && cart?.total === 0;

  const paymentReady =
    (activeSession && cart?.shipping_methods.length !== 0) || paidByGiftcard;

  // Determine if the continue button should be disabled
  const isContinueDisabled = () => {
    // If paid by gift card, always enable
    if (paidByGiftcard) return false;

    // If no payment method selected, disable
    if (!selectedPaymentMethod) return true;

    // If Stripe is selected, check card validation
    if (Constants.isStripe(selectedPaymentMethod) && stripeReady) {
      return !cardComplete || !!cardError;
    }

    // For other payment methods
    return false;
  };

  const createQueryString = useCallback(
    (name: string, value: string) => {
      const params = new URLSearchParams(searchParams);
      params.set(name, value);

      return params.toString();
    },
    [searchParams]
  );

  const handleEdit = () => {
    router.push(`${pathname}?${createQueryString("step", "payment")}`, {
      scroll: false,
    });
  };

  const handleSubmit = async () => {
    setIsLoading(true);
    try {
      if (!activeSession) {
        await initiatePaymentSession(cart, {
          provider_id: selectedPaymentMethod,
        });
      }
      return router.push(
        `${pathname}?${createQueryString("step", "review")}`,
        {
          scroll: false,
        }
      );
    } catch (handleError: any) {
      setError(handleError.message);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    setError(null);
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isOpen]);

  useEffect(() => {
    console.log(`payment`, { stripeReady });
  }, [stripeReady]);

  useEffect(() => {
    const initiateSession = async () => {
      if (selectedPaymentMethod) {
        try {
          setIsLoading(true);
          await initiatePaymentSession(cart, {
            provider_id: selectedPaymentMethod,
          });
        } catch (initPSError: any) {
          setError(initPSError.message);
        } finally {
          setIsLoading(false);
        }
      }
    };

    initiateSession();
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedPaymentMethod]);
  // Enhanced card change handler to track validation
  const handleCardChange = (event: any) => {
    if (event.brand) {
      setCardBrand(event.brand);
    }

    if (event.value.postalCode) {
      setCardPostal(event.value.postalCode);
    }

    setCardComplete(event.complete);

    if (event.error) {
      setCardError(event.error.message);
    } else {
      setCardError(null);
    }
  };

  return (
    <div className="bg-white">
      <PaymentHeading
        isOpen={isOpen}
        paymentReady={paymentReady}
        handleEdit={handleEdit}
      />

      <div>
        <div className={isOpen ? "block" : "hidden"}>
          {!paidByGiftcard && availablePaymentMethods?.length && (
            <PaymentMethodSelector
              availablePaymentMethods={availablePaymentMethods}
              selectedPaymentMethod={selectedPaymentMethod}
              setSelectedPaymentMethod={setSelectedPaymentMethod}
              stripeReady={stripeReady}
              handleCardChange={handleCardChange}
              cardError={cardError}
              cart={cart}
            />
          )}

          {paidByGiftcard && (
            <div className="flex flex-col w-1/3">
              <PaymentSummary
                cart={cart}
                paymentReady={false}
                activeSession={null}
                selectedPaymentMethod=""
                cardBrand=""
                cardPostal=""
                paidByGiftcard={true}
              />
            </div>
          )}

          <ErrorMessage
            error={error}
            data-testid="payment-method-error-message"
          />

          <Button
            size="large"
            className="mt-6"
            onClick={handleSubmit}
            isLoading={isLoading}
            disabled={isContinueDisabled()}
            data-testid="submit-payment-button"
          >
            Continue to review
          </Button>
        </div>

        <div className={isOpen ? "hidden" : "block"}>
          <PaymentSummary
            cart={cart}
            paymentReady={paymentReady}
            activeSession={activeSession}
            selectedPaymentMethod={selectedPaymentMethod}
            cardBrand={cardBrand}
            cardPostal={cardPostal}
            paidByGiftcard={paidByGiftcard}
          />
        </div>
      </div>
      <Divider className="mt-8" />
    </div>
  );
};

export default Payment;
