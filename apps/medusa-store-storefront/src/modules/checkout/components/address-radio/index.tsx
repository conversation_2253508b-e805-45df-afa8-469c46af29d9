"use client";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { RadioGroup } from "@headlessui/react";
import { Text, clx } from "@medusajs/ui";
import React, { type JSX } from "react";
import { updateAddress } from "@lib/data/cart";

type AddressRadioProps = {
  shippingAddress: any;
  altAddress: any;
  valid: boolean;
}
const addressEquals = (a: any, b: any) => {
  return (
    a.first_name === b.first_name &&
    a.last_name === b.last_name &&
    a.address_1 === b.address_1 &&
    a.address_2 === b.address_2 &&
    a.city === b.city &&
    a.province === b.province &&
    a.postal_code === b.postal_code
  );
};
const AddressRadio: React.FC<AddressRadioProps> = ({
  shippingAddress,
  altAddress,
  valid
}) => {
  const router = useRouter();
  const pathname = usePathname();
  const { id, ...rawShippingAddress } = shippingAddress;
  const shippingAddressJSON = JSON.stringify(rawShippingAddress);
  const altAddressJSON = JSON.stringify(altAddress);
  const handleEdit = () => {
    router.push(`${pathname}?step=address`);
  };
  return (
    <>
      {valid && !addressEquals(shippingAddress, altAddress) && (
        <RadioGroup
          value={shippingAddressJSON}
          onChange={(addressJson: string) => updateAddress(JSON.parse(addressJson))}
        >
          <RadioGroup.Option
            key={shippingAddressJSON}
            value={shippingAddressJSON}
            disabled={false}
            className={clx(
              "flex flex-col gap-x-2 text-small-regular cursor-pointer py-4 border rounded-rounded px-8 mb-2 hover:shadow-borders-interactive-with-active",
              {
                "border-ui-border-interactive":
              altAddressJSON !== shippingAddressJSON
              }
            )}
          >
            <div className="flex flex-col items-start gap-x-1 w-full">
              <div className="flex flex-col start">
                <div className="flex">
                  {/* <Radio value={JSON.stringify(shippingAddress)} /> */}
                  <Text className="txt-medium-plus text-ui-fg-base mb-1">
                Original Address
                  </Text>
                </div>

                <Text className="txt-medium text-ui-fg-subtle">
                  {shippingAddress.first_name} {shippingAddress.last_name}
                </Text>
                <Text className="txt-medium text-ui-fg-subtle">
                  {shippingAddress.address_1}
                </Text>
                <Text className="txt-medium text-ui-fg-subtle">
                  {shippingAddress.address_2}
                </Text>
                <Text className="txt-medium text-ui-fg-subtle">
                  {shippingAddress.city}, {shippingAddress.province} {shippingAddress.postal_code}
                </Text>
              </div>
            </div>
          </RadioGroup.Option>
          <RadioGroup.Option
            key={altAddressJSON}
            value={altAddressJSON}
            disabled={false}
            checked={true}
            className={clx(
              "flex flex-col gap-x-2 text-small-regular cursor-pointer py-4 border rounded-rounded px-8 mb-2 hover:shadow-borders-interactive-with-active",
              {
                "border-ui-border-interactive":
              shippingAddressJSON === altAddressJSON
              }
            )}
          >
            <div className="flex flex-col items-start gap-x-1 w-full ">
              <div className="flex flex-col start">
                <Text className="txt-medium-plus text-ui-fg-base mb-1">
                Suggested Address
                </Text>
                {/* <Radio checked={true} /> */}
                <Text className="txt-medium text-ui-fg-subtle">
                  {altAddress.first_name} {altAddress.last_name}
                </Text>
                <Text className="txt-medium text-ui-fg-subtle">
                  {altAddress.address_1}
                </Text>
                <Text className="txt-medium text-ui-fg-subtle">
                  {altAddress.address_2}
                </Text>
                <Text className="txt-medium text-ui-fg-subtle">
                  {altAddress.city}, {altAddress.province} {altAddress.postal_code}
                </Text>

              </div>

            </div>

          </RadioGroup.Option>
        </RadioGroup>)}
      {valid && addressEquals(shippingAddress, altAddress) &&(
        <div className="flex flex-col items-start gap-x-1 w-1/2 ">
          <div className="flex flex-col start">
            <div className="flex">
              <Text className="txt-medium-plus text-ui-fg-base mb-1">
              Address
              </Text>
            </div>

            <Text className="txt-medium text-ui-fg-subtle">
              {shippingAddress.first_name} {shippingAddress.last_name}
            </Text>
            <Text className="txt-medium text-ui-fg-subtle">
              {shippingAddress.address_1}
            </Text>
            <Text className="txt-medium text-ui-fg-subtle">
              {shippingAddress.address_2}
            </Text>
            <Text className="txt-medium text-ui-fg-subtle">
              {shippingAddress.city}, {shippingAddress.province} {shippingAddress.postal_code}
            </Text>
          </div>
        </div>
      )}
      {!valid && (
        <div className="flex flex-col items-start gap-x-1 w-1/2 ">
          <div className="flex flex-col start">
            <Text className="txt-medium-plus text-ui-fg-base mb-1">
                  Address
            </Text>
            <Text className="txt-medium text-ui-fg-subtle mb-2">
                  We were unable to validate the address.
            </Text>
            <Text className="txt-medium text-ui-fg-subtle">
              <div
                className="flex flex-col gap-x-2 text-small-regular cursor-pointer py-4 border rounded-rounded px-8 mb-2"
              >
                <div className="flex flex-col items-start gap-x-1 w-full ">
                  <div className="flex start">
                    <Text className="txt-medium text-ui-fg-subtle">
                      The address is not deliverable.
                    </Text>
                  </div>
                </div>
              </div>
              <div className="flex flex-col  gap-x-1 mb-2">
                <div className="flex start">
                  <Text className="txt-medium text-ui-fg-subtle">
                    If the address below is correct, then you don't need to do anything. To change your address,&nbsp;
                    <button
                      onClick={handleEdit}
                      className="text-ui-fg-interactive hover:text-ui-fg-interactive-hover"
                      data-testid="edit-address-button"
                    >click here
                    </button>.
                  </Text>
                </div>
              </div>
            </Text>
            <div
              className={clx(
                "flex flex-col gap-x-2 text-small-regular cursor-pointer py-4 border rounded-rounded px-8 mb-2 ",
                {
                  "border-ui-border-interactive":
              altAddressJSON !== shippingAddressJSON
                }
              )}
            >
              <div className="flex flex-col items-start gap-x-1 w-full ">
                <div className="flex flex-col start">
                  <div className="flex">
                    <Text className="txt-medium-plus text-ui-fg-base mb-1">
                      Original Address
                    </Text>
                  </div>
                  <Text className="txt-medium text-ui-fg-subtle">
                    {shippingAddress.first_name} {shippingAddress.last_name}
                  </Text>
                  <Text className="txt-medium text-ui-fg-subtle">
                    {shippingAddress.address_1}
                  </Text>
                  <Text className="txt-medium text-ui-fg-subtle">
                    {shippingAddress.address_2}
                  </Text>
                  <Text className="txt-medium text-ui-fg-subtle">
                    {shippingAddress.city}, {shippingAddress.province} {shippingAddress.postal_code}
                  </Text>
                </div>
              </div>
            </div>
          </div>
        </div>
      )
      }
    </>
  );
};

export default AddressRadio;
