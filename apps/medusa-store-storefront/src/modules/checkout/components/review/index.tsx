/* eslint-disable import/no-relative-parent-imports */
/* eslint-disable import/no-internal-modules */
/* eslint-disable functional/no-return-void */
/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import { CheckCircleSolid } from "@medusajs/icons";
import { Heading, Text, clx } from "@medusajs/ui";
import Divider from "@modules/common/components/divider";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { useCallback } from "react";
import PaymentButton from "../payment-button";

const ReviewHeader = ({
  isOpen,
  previousStepsCompleted,
  handleEdit
}: {
  isOpen: boolean;
  previousStepsCompleted: boolean;
  handleEdit: () => void
}) => (
  <div className="flex flex-row items-center justify-between mb-6">
    <Heading
      level="h2"
      className={clx(
        "flex flex-row text-3xl-regular gap-x-2 items-baseline",
        {
          "opacity-50 pointer-events-none select-none":
            !isOpen && !previousStepsCompleted,
        }
      )}
    >
      Review
      {!isOpen && previousStepsCompleted && <CheckCircleSolid />}
    </Heading>
    {!isOpen && previousStepsCompleted && (
      <Text>
        <button
          onClick={handleEdit}
          className="text-ui-fg-interactive hover:text-ui-fg-interactive-hover"
        >
          Edit
        </button>
      </Text>
    )}
  </div>
);

const ReviewContent = ({ isOpen, previousStepsCompleted, cart }: { isOpen: boolean; previousStepsCompleted: boolean; cart: any }) => {
  if (!isOpen) return null;

  if (!previousStepsCompleted) return null;

  return (
    <>
      <div className="flex items-start gap-x-1 w-full mb-6">
        <div className="w-full">
          <Text className="txt-medium-plus text-ui-fg-base mb-1">
            By clicking the Place Order button, you confirm that you have
            read, understand and accept our Terms of Use, Terms of Sale and
            Returns Policy and acknowledge that you have read Store&apos;s
            Privacy Policy.
          </Text>
        </div>
      </div>
      <PaymentButton cart={cart} data-testid="submit-order-button" />
    </>
  );
};

const Review = ({ cart }: { cart: any }) => {
  const searchParams = useSearchParams();
  const router = useRouter();
  const pathname = usePathname();

  const isOpen = searchParams.get("step") === "review";

  const previousStepsCompleted =
    cart &&
    cart.shipping_address &&
    cart.shipping_methods &&
    cart.shipping_methods.length > 0 &&
    cart.payment_collection?.payment_sessions?.length > 0;

  const createQueryString = useCallback(
    (name: string, value: string) => {
      const params = new URLSearchParams(searchParams);
      params.set(name, value);
      return params.toString();
    },
    [searchParams]
  );

  const handleEdit = () => {
    router.push(`${pathname}?${createQueryString("step", "review")}`, {
      scroll: false,
    });
  };

  return (
    <div className="bg-white">
      <ReviewHeader
        isOpen={isOpen}
        previousStepsCompleted={false}
        handleEdit={handleEdit}
      />
      <div>
        <div className={isOpen ? "block" : "hidden"}>
          <ReviewContent
            isOpen={isOpen}
            previousStepsCompleted={previousStepsCompleted}
            cart={cart}
          />
        </div>
      </div>
      <Divider className="mt-8" />
    </div>
  );
};

export default Review;
