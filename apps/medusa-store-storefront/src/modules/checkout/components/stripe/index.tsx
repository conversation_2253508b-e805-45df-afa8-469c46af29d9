/* eslint-disable complexity */
/* eslint-disable max-lines-per-function */
/* eslint-disable import/no-relative-parent-imports */
/* eslint-disable import/no-internal-modules */
/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import {
  useElements,
  useStripe,
} from "@stripe/react-stripe-js";
import { useState } from "react";
import { placeOrder } from "@lib/data/cart";
import { StripeCardElement } from "@stripe/stripe-js";
import ErrorMessage from "../error-message";
import { useStripeContext } from "../payment-wrapper/stripe-wrapper";
import Helpers from "./stripe-payment-helpers";

type StripePaymentButtonParams = {
  notReady: boolean
  cart: any
  "data-testid"?: string
};
const StripePaymentButton = ({ notReady,cart,"data-testid": dataTestId }: StripePaymentButtonParams) => {
  const [submitting, setSubmitting] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  const stripe = useStripe();
  const elements = useElements();
  const { isReady: stripeReady, clientSecret } = useStripeContext();

  const isDisabled = notReady || submitting || !stripe || !elements || !stripeReady;

  const handlePayment = async () => {
    setSubmitting(true);
    setErrorMessage(null);

    if (!Helpers.isStripeReady(stripe, elements, clientSecret)) return finalize("Payment system not ready. Please try again.");
    const card = Helpers.getCardElement(elements);
    if (!card) return finalize("Card information not found. Please try again.");
    return await stripeConfirmPayment(card);
  };

  const stripeConfirmPayment = async (card: StripeCardElement): Promise<{ errorMessage: string | null, submitting: boolean }> => {
    try {
      const result = await confirmPayment(card);
      if (result.error) {
        return finalize(
          result.error.message || "An error occurred with your payment.",
          { code: result.error.code, declineCode: result.error.decline_code, chargeId: result.error.charge });
      }
      if (Helpers.isPaymentSuccessful(result.paymentIntent.status)) {
        await handleOrderPlacement();
      }
    } catch (paymentException: any) {
      console.error("Payment processing error:", paymentException);
      finalize(paymentException.message || "An unexpected error occurred.");
    } finally {
      setSubmitting(false);
    }
    return { errorMessage: null, submitting: false };
  };

  const confirmPayment = (card: StripeCardElement) => {
    return stripe!.confirmCardPayment(clientSecret!, {
      payment_method: {
        card,
        billing_details: Helpers.generateBillingData(cart),
        metadata: Helpers.generateMetadata(cart),
      },
    });
  };

  const handleOrderPlacement = async () => {
    try {
      const orderResponse = await placeOrder();
      if (!orderResponse?.id) {
        finalize("Unable to complete order, please try again");
      }
    } catch (orderException: any) {
      console.error("Order completion error:", orderException);
      finalize(orderException.message || "Error completing your order");
    }
  };
  const finalize = (message: string, extra?: any) => {
    setErrorMessage(message);
    setSubmitting(false);
    if (extra) {
      console.log(`Extra details: ${JSON.stringify(extra)}`, message);
    }
    return { errorMessage: message, submitting: false };
  };
  return (
    <div>
      <button
        onClick={handlePayment}
        disabled={isDisabled}
        className="w-full flex items-center justify-center bg-ui-button-primary hover:bg-ui-button-primary-hover text-ui-button-text py-3 rounded disabled:opacity-50"
        data-testid={dataTestId}
      >
        {submitting ? "Processing..." : "Place order"}
      </button>
      {errorMessage && (
        <ErrorMessage
          error={errorMessage}
          data-testid="payment-error-message"
        />
      )}
    </div>
  );
};

export default StripePaymentButton;
