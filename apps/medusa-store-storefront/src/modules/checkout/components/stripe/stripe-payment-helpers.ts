import { StoreCart } from "@medusajs/types";
import { CardElement } from "@stripe/react-stripe-js";
import { Stripe, StripeElements } from "@stripe/stripe-js";

const isStripeReady = (stripe?: Stripe | null, elements?: StripeElements | null, clientSecret?: string) => {
  if (!stripe || !elements || !clientSecret) {
    console.error("Stripe not initialized or missing client secret");
    return false;
  }
  return true;
};
const generateBillingData = (cart: StoreCart) => {
  return {
    name: `${cart.billing_address?.first_name} ${cart.billing_address?.last_name}`,
    email: cart.email,
    phone: cart.billing_address?.phone,
    address: {
      city: cart.billing_address?.city,
      country: cart.billing_address?.country_code,
      line1: cart.billing_address?.address_1,
      line2: cart.billing_address?.address_2,
      postal_code: cart.billing_address?.postal_code,
      state: cart.billing_address?.province,
    },
  };
};
const generateMetadata = ({ id, email }: StoreCart) => {
  return {
    cart_id: id,
    customer_email: email || null,
    order_source: "MEDUSA",
    product: "Store",
  };
};
const isPaymentSuccessful = (status: string) => {
  return status === "succeeded" || status === "requires_capture";
};
const getCardElement = (elements: StripeElements | null) => {
  const card = elements?.getElement(CardElement);
  if (!card) console.error("Card element not found");
  return card;
};
const StripePaymentButtonHelper = {
  isStripeReady,
  generateBillingData,
  generateMetadata,
  isPaymentSuccessful,
  getCardElement
};
export default StripePaymentButtonHelper;
