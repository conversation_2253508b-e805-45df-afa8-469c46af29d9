import React from "react";

export interface StyleReactProps {
  class?: string;
  className?: string;
  style?: { [key: string]: any };
}

export type StencilReactExternalProps<PropType, ElementType> = PropType &
  Omit<React.HTMLAttributes<ElementType>, "style"> &
  StyleReactProps;

export const setRef = (
  ref: React.ForwardedRef<any> | React.Ref<any> | undefined,
  value: any,
) => {
  if (typeof ref === "function") {
    ref(value);
  } else if (ref != null) {
    // Cast as a MutableRef so we can assign current
    (ref as React.MutableRefObject<any>).current = value;
  }
};

export const mergeRefs =
  (
    ...refs: (React.ForwardedRef<any> | React.Ref<any> | undefined)[]
  ): React.RefCallback<any> =>
  (value: any) => {
    refs.forEach((ref) => {
      setRef(ref, value);
    });
  };

export const createForwardRef = <PropType, ElementType>(
  ReactComponent: any,
  displayName: string,
) => {
  const forwardRef = (
    props: StencilReactExternalProps<PropType, ElementType>,
    ref: React.ForwardedRef<ElementType>,
  ) => <ReactComponent {...props} forwardedRef={ref} />;
  forwardRef.displayName = displayName;

  return React.forwardRef(forwardRef);
};

export * from "./attachProps.ts";
export * from "./case.ts";
