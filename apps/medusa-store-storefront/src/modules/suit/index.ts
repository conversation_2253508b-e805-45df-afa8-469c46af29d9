import { JSX } from "@snap-mobile/snap-ui/dist/types";
import { createReactComponent } from "./create-component/create-component";

export const SnapButton = createReactComponent<
  JSX.SnapButton,
  HTMLSnapButtonElement
>("snap-button");

export const SnapInput = createReactComponent<
  JSX.SnapInput,
  HTMLSnapInputElement
>("snap-input");

export const SnapSelectMenu = createReactComponent<
  JSX.SnapSelectMenu,
  HTMLSnapSelectMenuElement
>("snap-select-menu");

export const SnapGlobalHeader = createReactComponent<
  JSX.SnapGlobalHeader,
  HTMLSnapGlobalHeaderElement
>("snap-global-header", undefined, undefined);

export const SnapMainNavigation = createReactComponent<
  JSX.SnapMainNavigation,
  HTMLSnapMainNavigationElement
>("snap-main-navigation");

export const SnapLogo = createReactComponent<JSX.SnapLogo, HTMLSnapLogoElement>(
  "snap-logo",
);

export const SnapProductLogo = createReactComponent<JSX.SnapProductLogo, HTMLSnapProductLogoElement>(
  "snap-product-logo",
);

export const SnapSession = createReactComponent<
  JSX.SnapSession,
  HTMLSnapSessionElement
>("snap-session");

export const SnapHeaderWrapper = createReactComponent<
  JSX.SnapHeaderWrapper,
  HTMLSnapHeaderWrapperElement
>("snap-header-wrapper");

export const SnapDropdown = createReactComponent<
  JSX.SnapDropdown,
  HTMLSnapDropdownElement
>("snap-dropdown");

export const SnapCloseButton = createReactComponent<
  JSX.SnapCloseButton,
  HTMLSnapCloseButtonElement
>("snap-close-button");

export const SnapIcon = createReactComponent<JSX.SnapIcon, HTMLSnapIconElement>(
  "snap-icon",
);

export const SnapPagination = createReactComponent<
  JSX.SnapPagination, HTMLSnapPaginationElement
>("snap-pagination");
