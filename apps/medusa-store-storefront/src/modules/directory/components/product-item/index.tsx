import Image from 'next/image';

import { toCurrency } from '@lib/util/format';

export type ProductItemProps = {
  category: string;
  name: string;
  price: number;
  imageUrl: string;
};

export default async function ProductItem(props: ProductItemProps) {
  const { category, name, price, imageUrl } = props;
  return (
    <figure className="product-item">
      <div className="bg-gray-50 px-2 py-4 mb-2">
        <Image src={imageUrl} alt={name} width={200} height={200} className="w-full" />
      </div>
      <figcaption className="text-left leading-4">
        <div className="text-sm text-second mb-1">{category}</div>
        <div className="font-semibold mb-0.5">{name}</div>
        <div className="text-xs">Starting at {toCurrency(price)}</div>
      </figcaption>
    </figure>
  );
}
