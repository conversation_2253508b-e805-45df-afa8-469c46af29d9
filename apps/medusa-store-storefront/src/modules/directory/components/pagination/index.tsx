"use client";

import type { SnapPaginationCustomEvent } from "@snap-mobile/snap-ui/dist/types/components";

import { SnapPagination } from "@modules/suit";
import { useRouter, useSearchParams } from "next/navigation";

import "./style.scss";

export interface PaginationProps {
  position: "top" | "bottom";
  itemCount: number;
  pageSize: number;
  currentPage: number;
}

export default function Pagination({ position, itemCount, pageSize, currentPage }: PaginationProps) {
  const router = useRouter();
  const searchParams = useSearchParams();

  const onPageChange = (e: SnapPaginationCustomEvent<number>) => {
    const query = Object.fromEntries(searchParams.entries());

    const params = new URLSearchParams(Object.fromEntries(
      Object.entries({ ...query, page: String(e.detail) }).filter(([name, value]) => value) as [string, string][]
    ));
    router.push(`/search?${params.toString()}`);
  };

  return (
    <div className="mb-6">
      <SnapPagination itemCount={itemCount}
        pageSize={pageSize}
        currentPage={currentPage}
        nav={position === "bottom"}
        className={`position-${position}`}
        onSnap-pagination-page-changed={onPageChange} />
    </div>
  );
}
