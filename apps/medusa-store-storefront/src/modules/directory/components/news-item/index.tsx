import './style.scss'

type NewsItemProps = {
  title: string
  text: string
  link: string
  href?: string
  imageUrl: string
  background: string
}

export default async function NewsItem(props: NewsItemProps) {
  const { title, text, link, imageUrl, background, href } = props;

  return (
    <div className="news-item py-12 pr-8 bg-none" style={{ backgroundColor: background, backgroundImage: `url(${imageUrl})` }}>
      <h2 className="text-2xl font-semibold mb-2">{title}</h2>
      <p className="mb-4">{text}</p>
      <p className="font-bold"><a href={href} target="_blank">{link}</a></p>
    </div>
  );
}
