import type { StoreType } from '@lib/graphql/types';

import StoreSearchBlock from "@modules/directory/components/store-search-block"

import {splitStores} from '@lib/util/split-stores';

export type StoreSearchResultProps = {
  stores: StoreType[]
}

type StoreAlphaBlock = [letter: string, stores: StoreType[]]

export default async function StoreSearchResult({ stores }: StoreSearchResultProps) {
  if (stores.length === 0) {
    return (
      <div className="pt-10 pb-44 text-center">
        <svg xmlns="http://www.w3.org/2000/svg" width="250" height="200" viewBox="0 0 250 200" fill="none" className="block mx-auto">
          <path
            d="M93 135C91.1435 135 89.363 134.262 88.0503 132.95C86.7375 131.637 86 129.857 86 128C86 126.143 86.7375 124.363 88.0503 123.05C89.363 121.737 91.1435 121 93 121H54C52.1435 121 50.363 120.263 49.0503 118.95C47.7375 117.637 47 115.857 47 114C47 112.143 47.7375 110.363 49.0503 109.05C50.363 107.737 52.1435 107 54 107H94C95.8565 107 97.637 106.263 98.9497 104.95C100.263 103.637 101 101.857 101 100C101 98.1435 100.263 96.363 98.9497 95.0503C97.637 93.7375 95.8565 93 94 93H69C67.1435 93 65.363 92.2625 64.0503 90.9497C62.7375 89.637 62 87.8565 62 86C62 84.1435 62.7375 82.363 64.0503 81.0503C65.363 79.7375 67.1435 79 69 79H109C107.143 79 105.363 78.2625 104.05 76.9497C102.737 75.637 102 73.8565 102 72C102 70.1435 102.737 68.363 104.05 67.0503C105.363 65.7375 107.143 65 109 65H207C208.857 65 210.637 65.7375 211.95 67.0503C213.263 68.363 214 70.1435 214 72C214 73.8565 213.263 75.637 211.95 76.9497C210.637 78.2625 208.857 79 207 79H167C168.857 79 170.637 79.7375 171.95 81.0503C173.262 82.363 174 84.1435 174 86C174 87.8565 173.262 89.637 171.95 90.9497C170.637 92.2625 168.857 93 167 93H189C190.857 93 192.637 93.7375 193.95 95.0503C195.263 96.363 196 98.1435 196 100C196 101.857 195.263 103.637 193.95 104.95C192.637 106.263 190.857 107 189 107H178.826C173.951 107 170 110.134 170 114C170 117.866 176 121 176 121C177.857 121 179.637 121.737 180.95 123.05C182.263 124.363 183 126.143 183 128C183 129.857 182.263 131.637 180.95 132.95C179.637 134.262 177.857 135 176 135H93ZM200 100C200 98.6155 200.411 97.2622 201.18 96.111C201.949 94.9599 203.042 94.0627 204.321 93.5328C205.6 93.003 207.008 92.8644 208.366 93.1345C209.723 93.4046 210.971 94.0713 211.95 95.0503C212.929 96.0292 213.595 97.2765 213.865 98.6344C214.136 99.9922 213.997 101.4 213.467 102.679C212.937 103.958 212.04 105.051 210.889 105.82C209.738 106.589 208.384 107 207 107C205.143 107 203.363 106.263 202.05 104.95C200.737 103.637 200 101.857 200 100Z"
            fill="#DBEAFE" />
          <path
            d="M120.5 133C139.002 133 154 118.002 154 99.5C154 80.9985 139.002 66 120.5 66C101.998 66 87 80.9985 87 99.5C87 118.002 101.998 133 120.5 133Z"
            fill="#DBEAFE" stroke="#3B82F6" strokeWidth="2.5" strokeMiterlimit="10" />
          <path
            d="M115.132 125.494C116.905 125.8 118.7 125.969 120.5 126C126.572 126.003 132.463 123.922 137.186 120.106C141.91 116.289 145.182 110.968 146.456 105.03C147.729 99.0918 146.927 92.8968 144.184 87.4787C141.441 82.0607 136.922 77.7476 131.382 75.2595C125.842 72.7714 119.617 72.2588 113.744 73.8073C107.872 75.3559 102.709 78.8718 99.1164 83.7682C95.5239 88.6646 93.7197 94.6451 94.0049 100.711C94.2901 106.778 96.6475 112.562 100.684 117.1"
            fill="white" />
          <path
            d="M115.132 125.494C116.905 125.8 118.7 125.969 120.5 126C126.572 126.003 132.463 123.922 137.186 120.106C141.91 116.289 145.182 110.968 146.456 105.03C147.729 99.0918 146.927 92.8968 144.184 87.4787C141.441 82.0607 136.922 77.7476 131.382 75.2595C125.842 72.7714 119.617 72.2588 113.744 73.8073C107.872 75.3559 102.709 78.8718 99.1164 83.7682C95.5239 88.6646 93.7197 94.6451 94.0049 100.711C94.2901 106.778 96.6475 112.562 100.684 117.1"
            stroke="#3B82F6" strokeWidth="2.5" strokeMiterlimit="10" strokeLinecap="round" />
          <path d="M103.797 120.075C105.962 121.837 108.392 123.245 110.997 124.247" stroke="#3B82F6" strokeWidth="2.5"
                strokeMiterlimit="10" strokeLinecap="round" />
          <path
            d="M121 81C118.504 80.9972 116.032 81.4868 113.726 82.4407C111.419 83.3945 109.324 84.794 107.559 86.5588C105.794 88.3237 104.395 90.4193 103.441 92.7258C102.487 95.0322 101.997 97.5041 102 100"
            stroke="#60A5FA" strokeWidth="2.5" strokeMiterlimit="10" strokeLinecap="round" />
          <path d="M148 126L154 132" stroke="#3B82F6" strokeWidth="2.5" strokeMiterlimit="10" />
          <path
            d="M167.542 150.389C166.247 150.389 165.03 149.885 164.115 148.97L153.03 137.885C151.14 135.995 151.14 132.92 153.03 131.03C153.945 130.115 155.162 129.611 156.457 129.611C157.751 129.611 158.968 130.115 159.884 131.03L170.969 142.116C172.859 144.005 172.859 147.08 170.969 148.97C170.054 149.885 168.836 150.389 167.542 150.389Z"
            fill="#BFDBFE" />
          <path
            d="M156.457 130.861C155.496 130.861 154.593 131.235 153.914 131.914C152.511 133.317 152.511 135.598 153.914 137.001L164.999 148.086C165.678 148.765 166.582 149.139 167.542 149.139C168.503 149.139 169.406 148.765 170.085 148.086C171.488 146.684 171.488 144.402 170.085 143L159 131.914C158.321 131.235 157.418 130.861 156.457 130.861ZM156.457 128.361C158.017 128.361 159.577 128.956 160.768 130.146L171.853 141.232C174.234 143.613 174.234 147.473 171.853 149.854C169.472 152.235 165.612 152.235 163.231 149.854L152.146 138.768C149.765 136.388 149.765 132.527 152.146 130.146C153.336 128.956 154.897 128.361 156.457 128.361Z"
            fill="#3B82F6" />
          <path d="M158 133L169 144" stroke="white" strokeWidth="2.5" strokeMiterlimit="10" strokeLinecap="round" />
        </svg>
        <h2 className="text-lg text-gray-800 font-semibold mt-10">No Stores Found</h2>
        <p className="mt-3">No matches found. Try checking your spelling or using different keywords.</p>
        <p className="mt-8">Feel like this is an error? <NAME_EMAIL>.</p>
      </div>
    );
  }

  const blocks: StoreAlphaBlock[] = splitStores(stores);

  return (
    <div className="mb-10">
      {blocks.map(([letter, stores])=> <StoreSearchBlock letter={letter} stores={stores} key={letter}/>)}
    </div>
  );
}
