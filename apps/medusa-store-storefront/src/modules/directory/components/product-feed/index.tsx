import ProductItem, { ProductItemProps } from '../product-item';

export default async function ProductFeed() {
  const products: ProductItemProps[] = [
    {
      name: 'OGIO Caliber 2.0 Polo',
      category: 'Polos',
      price: 29,
      imageUrl: '/assets/images/directory/product1.png'
    },
    {
      name: 'Badger Mesh Pocketed Short',
      category: 'Shorts',
      price: 30,
      imageUrl: '/assets/images/directory/product2.png'
    },
    {
      name: 'T-Shirt Hoodie Lightweight',
      category: 'Sweatshirts & Hoodies',
      price: 35,
      imageUrl: '/assets/images/directory/product3.png'
    },
    {
      name: 'Nike Brasilia Medium Backpack',
      category: 'Bags',
      price: 84,
      imageUrl: '/assets/images/directory/product4.png'
    },
    {
      name: 'Women\'s Haley Flannel Pants',
      category: 'Pants',
      price: 51,
      imageUrl: '/assets/images/directory/product5.png'
    },
  ];

  return (
    <section className="content-container text-center mb-20">
      <h2 className="text-2xl font-semibold">Popular Products</h2>
      <p>Our Top Selling Items</p>

      <div className="grid md:grid-cols-3 lg:grid-cols-5 gap-8 my-4">
        {products.map((product: ProductItemProps) => <ProductItem {...product} key={product.name} />)}
      </div>
    </section>
  );
}
