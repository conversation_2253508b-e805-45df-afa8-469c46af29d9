import type { StoreType } from '@lib/graphql/types';

import StoreSearchItem from '@modules/directory/components/store-search-item';

export type StoreSearchBlockProps = {
  letter: string;
  stores: StoreType[];
};

export default async function StoreSearchBlock({ letter, stores }: StoreSearchBlockProps) {
  return (
    <div className="mb-8">
      <h3 className="text-xl text-gray-800 font-semibold uppercase mb-6">{letter}</h3>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-x-10 gap-y-6">
        {stores.map((store: StoreType) =>
          <StoreSearchItem key={store.id} store={store} />
        )}
      </div>
    </div>
  );
}
