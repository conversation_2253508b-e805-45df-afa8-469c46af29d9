'use client';

import { Suspense } from 'react';

import StoreSearchForm from '@modules/directory/components/store-search-form';

import './style.scss';

export default function Hero() {
  return (
    <div className="hero flex flex-col justify-center text-white border-y border-black mb-12">
      <section className="content-container text-center">
        <h1 className="text-3xl font-semibold">Find Your Team’s Fan Store</h1>
        <p className="text-lg">
          Support your team and show your pride! Browse official gear from your school, club, or
          organization.
        </p>

        <div className="bg-white text-black max-w-[1000px] p-4 border border-gray-200 rounded-xl mx-auto mt-16">
          <Suspense>
            <StoreSearchForm />
          </Suspense>
        </div>
      </section>
    </div>
  );
}
