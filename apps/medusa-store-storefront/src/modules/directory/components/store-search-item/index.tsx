'use client';

import { FullStory } from '@fullstory/browser';

import { SnapIcon } from '@modules/suit';
import type { StoreType } from '@lib/graphql/types';

export type StoreSearchItemProps = {
  store: StoreType
}

export default function StoreSearchItem({ store }: StoreSearchItemProps) {
  const url = `${store.storeUrl}/?utm_source=directorypage`;
  const name = store.schoolName ?? store.name;

  const onStoreUrlClick = async (moduleId: string) => {
    await FullStory('trackEventAsync', {
      name: 'Component Clicked',
      properties: {
        buttonText: name,
        moduleId: `${name[0].toUpperCase()}-${moduleId}`,
        url
      }
    })
  }

  return (
    <div className="border border-gray-200 shadow-sm rounded-md text-sm p-4 flex gap-4">
      <div className="flex-grow">
        <div>
          <a href={url} className="text-blue-600 font-bold" title={name} target="_blank" onClick={() => onStoreUrlClick('name')}>
            {name}
          </a>
        </div>
        <div>
          <span className="text-gray-500">Location:</span> {store.city}, {store.state}
        </div>
        <div>
          <span className="text-gray-500">Organization:</span> {store.organizationName}
        </div>
      </div>
      <a href={url} title={store.name} target="_blank" onClick={() => onStoreUrlClick('arrow')}>
        <SnapIcon icon="chevron-right-solid" color="#2563EB"></SnapIcon>
      </a>
    </div>
  );
}
