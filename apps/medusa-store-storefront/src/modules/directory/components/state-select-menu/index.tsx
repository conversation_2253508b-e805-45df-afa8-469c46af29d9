import { SnapSelectMenuOption } from '@snap-mobile/snap-ui/dist/types/utils';

import { SnapSelectMenu } from '@modules/suit';

import { getStates } from '@lib/data/states';

export type StateSelectMenuProps = {
  value: string
  onChange?: (value: string) => void
}

export default function StateSelectMenu({ value, onChange }: StateSelectMenuProps) {
  const options: SnapSelectMenuOption[] = [
    {
      name: 'Select a State',
      value: '',
      selected: !value
    },
    ...getStates().map(([code, name]) => ({
      name: name,
      value: code,
      selected: code === value
    }))
  ];

  const onSelect = (updated: Array<SnapSelectMenuOption>): void => {
    const selected = updated.find((option) => option.selected);
    onChange?.(selected?.value ?? '');
  };

  return (
    <SnapSelectMenu selectMenuOptions={options}
                    onSnap-select-menu-updated={(e) => onSelect(e.detail)}
                    modalType="drawer"
                    modalTitle="Select a State"
                    className="md:w-60" />
  );
}
