"use client";

import { FormEvent, useState, useEffect } from "react";
import { useRouter, useSearchPara<PERSON>, ReadonlyURLSearchParams } from "next/navigation";

import { SnapButton, SnapInput, SnapIcon } from "@modules/suit";
import StateSelectMenu from "@modules/directory/components/state-select-menu";

import { StoreSearchCriteriaName } from "@lib/hooks/use-store-search";

const CRITERIA_NAMES: StoreSearchCriteriaName[] = ["name", "state"] as const;

const fromSearchParams = (searchParams: ReadonlyURLSearchParams): Record<string, string> => {
  const entries = searchParams?.entries();

  return entries
    ? Object.fromEntries([...entries].filter?.(([name]) => CRITERIA_NAMES.includes(name as StoreSearchCriteriaName)))
    : {};
};

export default function StoreSearchForm() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [query, setQuery] = useState(fromSearchParams(searchParams));

  const onInput = (name: StoreSearchCriteriaName, value: string) => {
    setQuery((prev) => ({ ...prev, [name]: value ?? undefined }));
  };

  const onSubmit = (e: FormEvent) => {
    e.preventDefault();

    const params = new URLSearchParams(Object.fromEntries(
      Object.entries(query).filter(([name, value]) => value) as [string, string][]
    ));
    router.push(`/search?${params.toString()}`);
  };

  useEffect(() => {
    setQuery(fromSearchParams(searchParams));
  }, [searchParams]);

  return (
    <form method="get"
      action="/search"
      className="flex flex-col md:flex-row gap-4"
      onSubmit={onSubmit}>

      <div className="flex-grow -mt-[2px] relative">
        <SnapInput _id="name"
          name="name"
          value={query.name ?? ""}
          placeholder="Enter School Name"
          icon="search-line"
          iconPosition="left"
          className="w-full"
          onInput={(e) => onInput("name", (e.target as HTMLInputElement).value)} />

        {query.name && (
          <SnapIcon icon="x-solid"
            size="xs"
            color="#d0d9e4"
            className="absolute top-[50%] -translate-y-[35%] right-2 cursor-pointer"
            onClick={() => onInput("name", "")} />
        )}

      </div>


      <StateSelectMenu value={query.state ?? ""}
        onChange={(value) => onInput("state", value)} />

      <SnapButton size="sm" variant="primary" buttonType="submit">Search</SnapButton>
    </form>
  );
}
