import Link from "next/link";

import { getStates } from "@lib/data/states";

import "./style.scss";

export default async function States() {
  const states = getStates();

  return (
    <section className="states content-container">
      <h2 className="text-2xl font-semibold text-center">Explore Stores by State</h2>
      <p className="text-center">Over 500+ Stores</p>

      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-x-2 gap-y-2 max-w-[1000px] mx-auto mt-10">
        {states.map(([code, name]) => <Link href={{ pathname: `/search`, query: { state: code } }}
          key={code}
          className="text-blue-700 font-bold">
          {name}
        </Link>)}
      </div>
    </section>
  );
}
