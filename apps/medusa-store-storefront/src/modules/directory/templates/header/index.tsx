"use client";

import Link from "next/link";

import { SnapProductLogo } from "@modules/suit";

import "./style.scss";

export default function Header() {
  return (
    <header>
      <div className="bg-store py-4 border-b-1 border-gray-100">
        <nav className="content-container">
          <ul className="flex gap-6">
            <li className="text-white font-bold">
              <a href="https://helpdesk.snapraise.com/snap-store-refund-cancellation-policy">Shipping Policy</a>
            </li>
            <li className="text-white font-bold">
              <a href="https://helpdesk.snapraise.com/store">Customer Support</a>
            </li>
          </ul>
        </nav>
      </div>

      <div className="content-container py-6">
        <div className="md:flex md:gap-12 items-center px-4">
          <Link href="/">
            <SnapProductLogo product="snap-product-logo-store" variant="1line" className="block w-[250px] mb-4 md:mb-0"/>
          </Link>
          <ul className="flex gap-4 text-gray-600">
            <li>
              <Link href="/search">Find Your Store</Link>
            </li>
            <li>
              <a href="https://snapraise.com/store/">About Store</a>
            </li>
            <li>
              <a href="https://helpdesk.snapraise.com/store#faqs">FAQs</a>
            </li>
          </ul>
        </div>
      </div>
    </header>
  );
}
