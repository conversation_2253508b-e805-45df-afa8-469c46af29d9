"use client";
import ProductActions from "@modules/products/components/product-actions";
import { useState } from "react";
import ImageGallery from "@modules/products/components/image-gallery";
import { HttpTypes } from "@medusajs/types";

type VariantPickerProps = {
  product: HttpTypes.StoreProduct
  region: HttpTypes.StoreRegion
  countryCode: string
  storeCode: string
}

export default function VariantPicker({ product, region, countryCode, storeCode }: VariantPickerProps) {
  const [images, setImages] = useState([{ url: product.thumbnail, rank: 0 }].concat(product.images.slice(1)));
  const handleSetImages = (imgs: typeof images) => {
    setImages(imgs);
    return true;
  };
  return (
    <>
      <div className="block w-full relative">
        <ImageGallery images={images} />
      </div>
      <div className="flex flex-col small:sticky small:top-48 small:py-0 small:max-w-[300px] w-full py-8 gap-y-12">
        {/* <ProductOnboardingCta /> */}


        <ProductActions
          disabled={false}
          product={product}
          handleSetImages={handleSetImages}
          region={region}
          countryCode={countryCode}
          storeCode={storeCode}
        />


        {/* <ProductActionsWrapper id={product.id} region={region} /> */}

      </div>
    </>);
}
