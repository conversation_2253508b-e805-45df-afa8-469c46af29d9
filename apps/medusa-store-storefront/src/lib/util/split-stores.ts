import type { StoreType } from '@lib/graphql/types';

export type StoreAlphaBlock = [letter: string, stores: StoreType[]];

type StoreAlphaHash = Record<string, StoreType[]>;

export const splitStores = (stores: StoreType[]): StoreAlphaBlock[] => {
  if (stores.length === 0) {
    return [];
  }

  const blocks = stores
    .filter(a => a.name)
    .map(store => ({ ...store, name: store.name?.replace(/\d{4}$/g, '').trim() }))
    .sort((a, b) => (a.schoolName ?? a.name)! > (b.schoolName ?? b.name)! ? 1 : -1)
    .reduce((acc, store) => {
      const firstLetter = (store.schoolName ?? store.name)?.[0].toLowerCase();
      if (!firstLetter) return acc;

      if (!acc[firstLetter]) acc[firstLetter] = [store];
      else acc[firstLetter] = [...acc[firstLetter], store];

      return acc;
    }, {} as StoreAlphaHash);

  return Object.entries(blocks);
};
