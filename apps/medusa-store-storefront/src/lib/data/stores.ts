'use server';

import { gql } from '@apollo/client';

import client from '@lib/graphql/client';
import type { StoreSearchWhereInput, StoreSearchPaginationInput, StoreSearchResponse } from '@lib/graphql/types';

const StoreSearchDocument = gql`
  query StoreSearch($where: StoreSearchWhereInput, $pagination: StoreSearchPaginationInput) {
    storeSearch(pagination: $pagination, where: $where) {
      limit
      offset
      total
      stores {
        id
        name
        organizationName
        storeUrl
        state
        city
        teamId
        schoolName
      }
    }
  }
`;

export const storeSearch = async(
  where: StoreSearchWhereInput,
  pagination: StoreSearchPaginationInput): Promise<StoreSearchResponse> => {
  const { data } = await client.query({ query: StoreSearchDocument, variables: { where, pagination } });
  return data?.storeSearch;
}
