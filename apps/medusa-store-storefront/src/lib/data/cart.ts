/* eslint-disable etc/no-commented-out-code */
/* eslint-disable import/no-internal-modules */
/* eslint-disable functional/no-let */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable id-denylist */
/* eslint-disable max-lines-per-function */
/* eslint-disable functional/immutable-data */
/* eslint-disable import/prefer-default-export */
/* eslint-disable func-style */
/* eslint-disable @typescript-eslint/no-shadow */
/* eslint-disable max-statements */
/* eslint-disable max-lines */
/* eslint-disable no-warning-comments */
/* eslint-disable import/group-exports */
"use server";

import { sdk } from "@lib/config";
import medusaError from "@lib/util/medusa-error";
import { HttpTypes } from "@medusajs/types";
import { revalidateTag } from "next/cache";
import { redirect } from "next/navigation";
import {
  getAuthHeaders,
  getCacheOptions,
  getCacheTag,
  getCartId,
  removeCartId,
  setCartId,
} from "./cookies";
import { getRegion } from "./regions";
import { getStoreByCode } from "./stores";

export async function retrieveCart() {
  const cartId = await getCartId();

  if (!cartId) {
    return null;
  }

  const headers = {
    ...(await getAuthHeaders()),
  };

  const next = {
    ...(await getCacheOptions("carts")),
  };

  return await sdk.client.
    fetch<HttpTypes.StoreCartResponse>(`/store/carts/${cartId}`, {
      method: "GET",
      query: {
        fields:
          "*items, *region, *items.product, *items.variant, *items.thumbnail, *items.metadata, +items.total, *promotions",
      },
      headers,
      next,
      // Cache: "force-cache",
    }).
    then(({ cart }) => cart).
    catch(() => null);
}

export async function getOrSetCart(countryCode: string) {
  const region = await getRegion(countryCode);

  if (!region) {
    throw new Error(`Region not found for country code: ${countryCode}`);
  }

  let cart = await retrieveCart();

  const headers = {
    ...(await getAuthHeaders()),
  };

  if (!cart) {
    const cartResp = await sdk.store.cart.create(
      { region_id: region.id },
      {},
      headers
    );
    cart = cartResp.cart;

    await setCartId(cart.id);

    const cartCacheTag = await getCacheTag("carts");
    revalidateTag(cartCacheTag);
  }

  if (cart && cart?.region_id !== region.id) {
    await sdk.store.cart.update(cart.id, { region_id: region.id }, {}, headers);
    const cartCacheTag = await getCacheTag("carts");
    revalidateTag(cartCacheTag);
  }

  return cart;
}

export async function updateCart(data: HttpTypes.StoreUpdateCart) {
  const cartId = await getCartId();

  if (!cartId) {
    throw new Error("No existing cart found, please create one before updating");
  }

  const headers = {
    ...(await getAuthHeaders()),
  };

  return sdk.store.cart.
    update(cartId, data, {}, headers).
    then(async ({ cart }) => {
      const cartCacheTag = await getCacheTag("carts");
      revalidateTag(cartCacheTag);
      return cart;
    }).
    catch(medusaError);
}

export async function addToCart({
  variantId,
  quantity,
  countryCode = "us",
  logoUrl,
  netsuiteId,
  storeCode
}: {
  variantId: string
  quantity: number
  countryCode: string
  logoUrl: string
  netsuiteId: string
  storeCode: string
}) {
  if (!variantId) {
    throw new Error("Missing variant ID when adding to cart");
  }

  const store = await getStoreByCode(storeCode);
  const storeId = store?.id || storeCode;

  const cart = await getOrSetCart(countryCode);

  if (!cart) {
    throw new Error("Error retrieving or creating cart");
  }

  const headers = {
    ...(await getAuthHeaders()),
  };

  await sdk.store.cart.
    createLineItem(
      cart.id,
      {
        variant_id: variantId,
        quantity,
        metadata: {
          logoUrl,
          netsuiteId,
          storeId
        }
      },
      {},
      headers
    ).
    then(async () => {
      const cartCacheTag = await getCacheTag("carts");
      revalidateTag(cartCacheTag);
    }).
    catch(medusaError);
}

export async function updateLineItem({
  lineId,
  quantity,
}: {
  lineId: string
  quantity: number
}) {
  if (!lineId) {
    throw new Error("Missing lineItem ID when updating line item");
  }

  const cartId = await getCartId();

  if (!cartId) {
    throw new Error("Missing cart ID when updating line item");
  }

  const headers = {
    ...(await getAuthHeaders()),
  };

  await sdk.store.cart.
    updateLineItem(cartId, lineId, { quantity }, {}, headers).
    then(async () => {
      const cartCacheTag = await getCacheTag("carts");
      revalidateTag(cartCacheTag);
    }).
    catch(medusaError);
}

export async function deleteLineItem(lineId: string) {
  if (!lineId) {
    throw new Error("Missing lineItem ID when deleting line item");
  }

  const cartId = await getCartId();

  if (!cartId) {
    throw new Error("Missing cart ID when deleting line item");
  }

  const headers = {
    ...(await getAuthHeaders()),
  };

  await sdk.store.cart.
    deleteLineItem(cartId, lineId, headers).
    then(async () => {
      const cartCacheTag = await getCacheTag("carts");
      revalidateTag(cartCacheTag);
    }).
    catch(medusaError);
}

export async function setShippingMethod({
  cartId,
  shippingMethodId,
}: {
  cartId: string
  shippingMethodId: string
}) {
  const headers = {
    ...(await getAuthHeaders()),
  };

  return sdk.store.cart.
    addShippingMethod(cartId, { option_id: shippingMethodId }, {}, headers).
    then(async () => {
      const cartCacheTag = await getCacheTag("carts");
      revalidateTag(cartCacheTag);
    }).
    catch(medusaError);
}

export async function initiatePaymentSession(
  cart: HttpTypes.StoreCart,
  data: {
    provider_id: string
    context?: Record<string, unknown>
  }
) {
  const headers = {
    ...(await getAuthHeaders()),
  };

  return sdk.store.payment.
    initiatePaymentSession(cart, data, {}, headers).
    then(async (resp) => {
      const cartCacheTag = await getCacheTag("carts");
      revalidateTag(cartCacheTag);
      return resp;
    }).
    catch(medusaError);
}

export async function applyPromotions(codes: string[]) {
  const cartId = await getCartId();

  if (!cartId) {
    throw new Error("No existing cart found");
  }

  const headers = {
    ...(await getAuthHeaders()),
  };

  return sdk.store.cart.
    update(cartId, { promo_codes: codes }, {}, headers).
    then(async () => {
      const cartCacheTag = await getCacheTag("carts");
      revalidateTag(cartCacheTag);
    }).
    catch(medusaError);
}

export async function applyGiftCard(code: string) {
  //   Const cartId = getCartId()
  //   if (!cartId) return "No cartId cookie found"
  //   try {
  //     await updateCart(cartId, { gift_cards: [{ code }] }).then(() => {
  //       revalidateTag("cart")
  //     })
  //   } catch (error: any) {
  //     throw error
  //   }
}

export async function removeDiscount(code: string) {
  // Const cartId = getCartId()
  // if (!cartId) return "No cartId cookie found"
  // try {
  //   await deleteDiscount(cartId, code)
  //   revalidateTag("cart")
  // } catch (error: any) {
  //   throw error
  // }
}

export async function removeGiftCard(
  codeToRemove: string,
  giftCards: any[]
  // GiftCards: GiftCard[]
) {
  //   Const cartId = getCartId()
  //   if (!cartId) return "No cartId cookie found"
  //   try {
  //     await updateCart(cartId, {
  //       gift_cards: [...giftCards]
  //         .filter((gc) => gc.code !== codeToRemove)
  //         .map((gc) => ({ code: gc.code })),
  //     }).then(() => {
  //       revalidateTag("cart")
  //     })
  //   } catch (error: any) {
  //     throw error
  //   }
}

export async function submitPromotionForm(
  currentState: unknown,
  formData: FormData
) {
  const code = formData.get("code") as string;
  try {
    await applyPromotions([code]);
  } catch (e: any) {
    return e.message;
  }
}

export async function validateAddress(address: any) {
  if (!address?.address_1) {
    return null;
  }
  const headers = {
    ...(await getAuthHeaders()),
  };

  return sdk.client.
    fetch<{ products: HttpTypes.StoreProduct[]; count: number }>(
      `/store/addresses/validate`,
      {
        method: "POST",
        headers,
        cache: "no-cache",
        body: address
      }
    ).
    then((res) => {
      return res;
    });
}

export async function updateAddress(address: any, id: string) {
  const req = {
    id,
    shipping_address: address
  };
  await updateCart(req);
}
// TODO: Pass a POJO instead of a form entity here
export async function setAddresses(currentState: unknown, formData: FormData) {
  try {
    if (!formData) {
      throw new Error("No form data found when setting addresses");
    }
    const cartId = getCartId();
    if (!cartId) {
      throw new Error("No existing cart found when setting addresses");
    }

    const data = {
      shipping_address: {
        first_name: formData.get("shipping_address.first_name"),
        last_name: formData.get("shipping_address.last_name"),
        address_1: formData.get("shipping_address.address_1"),
        address_2: "",
        company: formData.get("shipping_address.company"),
        postal_code: formData.get("shipping_address.postal_code"),
        city: formData.get("shipping_address.city"),
        country_code: formData.get("shipping_address.country_code"),
        province: formData.get("shipping_address.province"),
        phone: formData.get("shipping_address.phone"),
      },
      email: formData.get("email"),
    } as any;
    const sameAsBilling = formData.get("same_as_billing");
    if (sameAsBilling === "on") data.billing_address = data.shipping_address;

    if (sameAsBilling !== "on")
      data.billing_address = {
        first_name: formData.get("billing_address.first_name"),
        last_name: formData.get("billing_address.last_name"),
        address_1: formData.get("billing_address.address_1"),
        address_2: "",
        company: formData.get("billing_address.company"),
        postal_code: formData.get("billing_address.postal_code"),
        city: formData.get("billing_address.city"),
        country_code: formData.get("billing_address.country_code"),
        province: formData.get("billing_address.province"),
        phone: formData.get("billing_address.phone"),
      };

    await updateCart(data);
  } catch (e: any) {
    return e.message;
  }
  redirect(
    `/${formData.get("storeCode")}/checkout?step=delivery`
  );
}

export async function placeOrder() {
  const cartId = await getCartId();

  if (!cartId) {
    throw new Error("No existing cart found when placing an order");
  }

  const headers = {
    ...(await getAuthHeaders()),
  };

  try {
    const cartRes = await sdk.store.cart.
      complete(cartId, {}, headers).
      then(async (cartRes) => {
        const cartCacheTag = await getCacheTag("carts");
        revalidateTag(cartCacheTag);
        return cartRes;
      }).
      catch((error) => {
        console.error("Error completing cart:", error);
        if (error.message && error.message.includes("CORS")) {
          throw new Error("Network error: CORS issue detected. Please contact support.");
        }
        throw medusaError(error);
      });

    if (cartRes?.type === "order") {
      const countryCode =
        cartRes.order.shipping_address?.country_code?.toLowerCase();
      removeCartId();
      redirect(`/${countryCode}/order/${cartRes?.order.id}/confirmed`);
    }

    return cartRes.cart;
  } catch (error) {
    console.error("Caught error in placeOrder:", error);
    throw error;
  }
}

/**
 * Updates the countrycode param and revalidates the regions cache
 * @param regionId
 * @param countryCode
 */
export async function updateRegion(countryCode: string, currentPath: string) {
  const cartId = await getCartId();
  const region = await getRegion(countryCode);

  if (!region) {
    throw new Error(`Region not found for country code: ${countryCode}`);
  }

  if (cartId) {
    await updateCart({ region_id: region.id });
    const cartCacheTag = await getCacheTag("carts");
    revalidateTag(cartCacheTag);
  }

  const regionCacheTag = await getCacheTag("regions");
  revalidateTag(regionCacheTag);

  const productsCacheTag = await getCacheTag("products");
  revalidateTag(productsCacheTag);

  redirect(`/${countryCode}${currentPath}`);
}

export async function listCartOptions() {
  const cartId = await getCartId();
  const headers = {
    ...(await getAuthHeaders()),
  };
  const next = {
    ...(await getCacheOptions("shippingOptions")),
  };

  return await sdk.client.fetch<{
    shipping_options: HttpTypes.StoreCartShippingOption[]
  }>("/store/shipping-options", {
    query: { cart_id: cartId },
    next,
    headers,
    cache: "force-cache",
  });
}
