"use server";

import { sdk } from "@lib/config";
import { getAuthHeaders } from "./cookies";

// eslint-disable-next-line import/prefer-default-export
export const testWorkflow = async () => {
  const headers = {
    ...(await getAuthHeaders()),
  };
  return sdk.client.
    fetch(
      `/store/test-workflow`,
      {
        method: "GET",
        query: {},
        headers,
        cache: "no-cache",
      }
    );
};
