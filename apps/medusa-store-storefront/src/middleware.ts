import { HttpTypes } from "@medusajs/types";
import { NextRequest, NextResponse } from "next/server";

const BACKEND_URL = process.env.MEDUSA_BACKEND_URL;
const PUBLISHABLE_API_KEY = process.env.NEXT_PUBLIC_MEDUSA_PUBLISHABLE_KEY;
const DEFAULT_REGION = process.env.NEXT_PUBLIC_DEFAULT_REGION || "us";


/**
 * Middleware to handle region selection and onboarding status.
 */


export async function middleware(request: NextRequest) {
  const url = request.nextUrl;
  const pathname = url.pathname;
  const storeCode = pathname.split("/")[1];

  if (
    pathname.startsWith("/api") ||
    pathname.startsWith("/_next") ||
    pathname.startsWith("/favicon.ico") ||
    pathname.startsWith("/images") ||
    pathname.startsWith("/assets")
  ) {
    return NextResponse.next();
  }

  // If (!storeCode) {
  //   return NextResponse.redirect(new URL(`/home`, request.url));
  // }


  // Check if the url is home directory
  if (request.nextUrl.pathname.startsWith("/search")) {
    return NextResponse.next();
  }

  // Check if the url is a static asset
  if (request.nextUrl.pathname.includes(".")) {
    return NextResponse.next();
  }

  const redirectPath =
    request.nextUrl.pathname === "/" ? "" : request.nextUrl.pathname;

  const queryString = request.nextUrl.search ? request.nextUrl.search : "";

  if (pathname.startsWith(`/${storeCode}`)) {
    return NextResponse.rewrite(new URL("", request.url));
  }

  return NextResponse.next();
}

export const config = {
  matcher: [
    "/((?!api|_next/static|_next/image|health-checks|favicon.ico|images|assets|png|svg|jpg|jpeg|gif|webp).*)",
  ],
};
