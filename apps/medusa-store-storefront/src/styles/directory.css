@import "tailwindcss/base";
@import "tailwindcss/components";
@import "tailwindcss/utilities";

:root {
  --color-raise: #1269C1;
  --color-store: #051C31;
  --store-color-second: #767676;
}

:root {
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: light;
  color: #0F172A;
  background-color: #ffffff;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

* {
  scrollbar-width: thin;
  box-sizing: border-box;
}

.content-container {
  @apply max-w-[1440px] w-full mx-auto px-6;
}

main {
  min-height: 100%;
  display: flex;
  flex-direction: column;
}

.bg-store {
  background-color: var(--color-store);
}

.bg-raise {
  background-color: var(--color-raise);
}

.text-second {
  color: var(--store-color-second);
}
