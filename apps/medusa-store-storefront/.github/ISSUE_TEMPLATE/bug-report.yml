name: Bug report for the Medusa Next.js Starter
description: File a bug report.
title: "[Bug]: "
labels: ["status: needs triaging", "bug"]
body:
  - type: markdown
    attributes:
      value: "## System information"
  - type: markdown
    attributes:
      value: |
        The system information will help us reproduce the issue in the same environment
  - type: textarea
    attributes:
      label: Package.json file
      description: Copy/paste the contents of the `package.json` file. No need to use backticks
      placeholder: No need to use markdown backticks. Just copy/paste the contents of the file
      render: JSON
    validations:
      required: true
  - type: input
    attributes:
      label: Node.js version
      description: Copy/paste the output of `node -v` command.
      placeholder: v21.0.0
    validations:
      required: true
  - type: input
    attributes:
      label: Operating system name and version
    validations:
      required: true
  - type: input
    attributes:
      label: Browser name
  - type: markdown
    attributes:
      value: "## Describe the issue"
  - type: markdown
    attributes:
      value: |
        Please explain your issue in-depth along with the relevant screenshots and code snippets
  - type: textarea
    attributes:
      label: What happended?
      placeholder: A clear and concise description of what the bug is
    validations:
      required: true
  - type: textarea
    attributes:
      label: Expected behavior
    validations:
      required: true
  - type: textarea
    attributes:
      label: Actual behavior
    validations:
      required: true
  - type: markdown
    attributes:
      value: "## Reproduction"
  - type: markdown
    attributes:
      value: |
        Providing a reproduction repo allows us to quickly validate the issue and get back to you.
  - type: input
    attributes:
      label: Link to reproduction repo
      description: Please reproduce the issue in isolation and share it as a Github repo with us
    validations:
      required: true
