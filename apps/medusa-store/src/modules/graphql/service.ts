import { ApolloClient, createHttpLink, gql, InMemoryCache } from "@apollo/client";
import { setContext } from "@apollo/client/link/context";
import { OrderInput, OrderResult } from "src/workflows/orders/types";


import { EntityManager } from "typeorm";

const OrderMutationDocument = gql`
  mutation CreateOrder($order: OrderInput!) {
    createStoreOrder(order: $order) {
      status
      errors
    }
  }
`;

export type PluginOptions = {
  graphql_api_key: string;
  graphql_url: string;
}

type InjectedDependencies = {
  manager: EntityManager;
}

class GraphqlClientService {
  declare protected manager_: EntityManager;
  declare protected transactionManager_: EntityManager;

  protected options_: PluginOptions;
  protected client_: ApolloClient;

  constructor(deps: InjectedDependencies, options: PluginOptions) {
    this.options_ = options;
    const authLink = setContext((_, { headers }) => {
      return {
        headers: {
          ...headers,
          "x-api-key": this.options_.graphql_api_key
        }
      };
    });
    const httpLink = createHttpLink({
      uri: this.options_.graphql_url
    });
    this.client_ = new ApolloClient({
      link: authLink.concat(httpLink),
      cache: new InMemoryCache()
    });
  }

  async createStoreOrder(order: OrderInput): Promise<OrderResult> {
    const body = { mutation: OrderMutationDocument, variables: { order } };
    try {
      const { data } = await this.client_.mutate(body);
      return data;
    } catch (e) {
      console.log(JSON.stringify(e));
    }
  }
}

export default GraphqlClientService;
