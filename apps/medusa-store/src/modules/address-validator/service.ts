/* eslint-disable functional/no-this-expressions, functional/no-classes */
import { result } from "lodash";
import { EntityManager } from "typeorm";

export type PluginOptions = {
  google_validation_api_key: string;
}

type InjectedDependencies = {
  manager: EntityManager;
}

class AddressService {
  declare protected manager_: EntityManager;
  declare protected transactionManager_: EntityManager;
  protected options_: PluginOptions;
  declare protected baseUrl : string;
  constructor(deps: InjectedDependencies, options: PluginOptions) {
    this.options_ = options;
    this.baseUrl = `https://addressvalidation.googleapis.com/v1:validateAddress?key=`;
  }

  async validate(address: any): Promise<any> {
    const { address_1, address_2, postal_code, city, province, first_name, last_name, company, phone } = address;
    const resp = await fetch(`${this.baseUrl}${this.options_.google_validation_api_key}`, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({
        address: {
          addressLines: [address_1, address_2],
          regionCode: "US",
          locality: city,
          administrativeArea: province,
          postalCode: postal_code,
          recipients: [`${first_name} ${last_name}`]
        }
      }),
    });
    const json = await resp.json();
    const valid = !json.result.verdict.hasUnconfirmedComponents;
    if (!valid) {
      return { valid: false, address: null };
    }
    const postalAddress = json.result.address.postalAddress;
    const uspsAddress = json.result.uspsData.standardizedAddress;
    const { addressLines } = postalAddress;
    return {
      valid: true,
      address: {
        first_name: address.first_name,
        last_name: address.last_name,
        address_1: uspsAddress.firstAddressLine,
        company: company || "" ,
        address_2: addressLines.at(1) || "",
        postal_code: `${uspsAddress.zipCode}-${uspsAddress.zipCodeExtension}`,
        province: uspsAddress.state,
        country_code: "us",
        phone,
        city: uspsAddress.city
      }
    };
  }
}

export default AddressService;
