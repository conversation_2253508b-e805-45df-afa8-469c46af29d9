import { model } from "@medusajs/framework/utils";
import Brand from "../../brand/models/brand";
export const StoreScope = model.define("store_scope", {
  id: model.id().primaryKey(),
  enabled: model.boolean().default(true),
  name: model.text(),
  code: model.text(),
  domain: model.text().searchable(),
  primary_color: model.text(),
  favicon: model.text(),
  header_logo: model.text(),
  hat_logo: model.text(),
  digital_logo: model.text(),
  embroidery_logo: model.text(),
  stripe_credit_card_descriptor: model.text().nullable()
});

export default StoreScope;
