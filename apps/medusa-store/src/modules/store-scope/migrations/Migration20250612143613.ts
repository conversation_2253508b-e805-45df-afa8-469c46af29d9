import { Migration } from '@mikro-orm/migrations';

export class Migration20250612143613 extends Migration {

  override async up(): Promise<void> {
    this.addSql(`create table if not exists "store_scope" ("id" text not null, "enabled" boolean not null default true, "name" text not null, "code" text not null, "domain" text not null, "primary_color" text not null, "favicon" text not null, "header_logo" text not null, "hat_logo" text not null, "digital_logo" text not null, "embroidery_logo" text not null, "stripe_credit_card_descriptor" text null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "store_scope_pkey" primary key ("id"));`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_store_scope_deleted_at" ON "store_scope" (deleted_at) WHERE deleted_at IS NULL;`);
  }

  override async down(): Promise<void> {
    this.addSql(`drop table if exists "store_scope" cascade;`);
  }

}
