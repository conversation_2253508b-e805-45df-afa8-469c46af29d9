import fs from "fs";
import axios, { AxiosInstance, AxiosResponse, Method } from "axios";
import { EntityManager } from "typeorm";
import addOAuthInterceptor from "axios-oauth-1.0a";
export type PluginOptions = {
  magento_url: string;
  consumer_key: string;
  consumer_secret: string;
  access_token: string;
  access_token_secret: string;
  image_prefix?: string;
}

type InjectedDependencies = {
  manager: EntityManager;
  productService: ProductService;
}

export type MagentoFilters = {
  field: string;
  value: string;
  condition_type?: string;
}

type SearchCriteria = {
  currentPage: number;
  pageSize: number;
  filterGroups?: MagentoFilters[][];
  storeId?: string;
  currencyCode?: string;
}

export enum MagentoProductTypes {
  CONFIGURABLE = "configurable",
  SIMPLE = "simple"
}

class MagentoClientService {
  declare protected manager_: EntityManager;
  declare protected transactionManager_: EntityManager;
  protected apiBaseUrl_: string;
  protected options_: PluginOptions;
  protected client_: AxiosInstance;
  protected defaultStoreId_: string;
  protected defaultCurrencyCode_: string;
  protected defaultImagePrefix_: string;
  protected productService_: ProductService;

  constructor(deps: InjectedDependencies, options: PluginOptions) {
    console.info("container", Object.keys(deps));
    this.options_ = options;
    this.apiBaseUrl_ = `${options.magento_url}/rest/default/V1`;

    this.client_ = axios.create({
      headers: {
        Accept: "application/json"
      },
      timeout: 30000,
    });

    addOAuthInterceptor(this.client_, {
      algorithm: "HMAC-SHA256",
      key: options.consumer_key,
      secret: options.consumer_secret,
      token: options.access_token,
      tokenSecret: options.access_token_secret
    });

    this.client_.interceptors.request.use(null, (error) => {
      console.log("error path 1");
      throw new Error(
        error.response?.data?.message || error.request?.data?.message || error.message || "An error occurred while sending the request."
      );
    });

    this.client_.interceptors.response.use(null, (error) => {
      console.log("error path 2");
      console.log(error);
      throw new Error(
        error.response?.data?.message || error.request?.data?.message || error.message || "An error occurred while sending the request."
      );
    });

    this.defaultImagePrefix_ = options.image_prefix;
  }

  async retrieveAttributes() {
    const color = await this.sendRequest(`/products/attributes/color`).then(async ({ data }) => data.options.reduce((acc, attr) => {
      acc[attr.value] = attr.label;
      return acc;
    }, {}));
    const size = await this.sendRequest("/products/attributes/size").then(async ({ data }) => data.options.reduce((acc, attr) => {
      acc[attr.value] = attr.label;
      return acc;
    }, {}));

    const category = await this.retrieveCategories().then(async ({ data }) => data.items.reduce((acc, el) => {
      acc[el.id] = el.name;
      return acc;
    }, {}));

    return {
      color,
      size,
      category
    };
  }

  async retrieveProducts(type?: MagentoProductTypes, lastUpdatedTime?: string, filters?: MagentoFilters[][]): Promise<Record<string, any>[]> {
    const searchCriteria: SearchCriteria = {
      currentPage: 1,
      pageSize: 500,
      filterGroups: []
    };

    if (type) {
      searchCriteria.filterGroups.push([
        {
          field: "type_id",
          value: type,
          condition_type: "eq"
        }
      ]);
    }

    if (lastUpdatedTime) {
      searchCriteria.filterGroups.push([
        {
          field: "updated_at",
          value: lastUpdatedTime,
          condition_type: "gt"
        }
      ]);
    }

    if (filters) {
      filters.forEach((filterGroup) => {
        const newFilterGroup: MagentoFilters[] = filterGroup.map((filter) => ({
          field: filter.field,
          value: filter.value,
          condition_type: filter.condition_type || "eq"
        }));

        searchCriteria.filterGroups.push(newFilterGroup);
      });
    }

    let allProducts = [];
    let hasMore = true;

    while (hasMore) {
      const { data } = await this.sendRequest(`/products?${this.formatSearchCriteriaQuery(searchCriteria)}`);
      await this.retrieveDefaultConfigs();
      let options;
      if (type === MagentoProductTypes.CONFIGURABLE) {
        options = await this.retrieveOptions();
      }

      for (let i = 0; i < data.items.length; i++) {
        data.items[i].media_gallery_entries = data.items[i].media_gallery_entries?.map((entry) => {
          entry.url = `${this.defaultImagePrefix_}${entry.file}`;
          return entry;
        });

        if (data.items[i].extension_attributes?.configurable_product_options) {
          data.items[i].extension_attributes?.configurable_product_options.forEach((option) => {
            option.values = options.find((o) => o.attribute_id == option.attribute_id)?.options || [];
          });
        }
      }

      allProducts = allProducts.concat(data.items);
      hasMore = data.total_count > allProducts.length;
      searchCriteria.currentPage++;
    }

    return allProducts;
  }

  async retrieveScopes(): Promise<Record<string, any>[]> {
    // Const data = await fs.readFileSync("./scopes.json");
    // return JSON.parse(data);
    const searchCriteria: SearchCriteria = {
      currentPage: 1,
      pageSize: 500,
      filterGroups: []
    };


    let allScopes = [];
    let hasMore = true;
    const url = `/snapraise-storescopeapi/getallstoresscopes?${this.formatSearchCriteriaQuery(searchCriteria)}`;

    while (hasMore) {
      try {
        const { data } = await this.sendRequest(`/snapraise-storescopeapi/getallstoresscopes?${this.formatSearchCriteriaQuery(searchCriteria)}`);

        if (!data.total_stores) {
          hasMore = false;
          console.log("all stores retrieved");
        }
        allScopes = allScopes.concat(data.items);
        searchCriteria.currentPage++;
      } catch (e) {
        console.log(searchCriteria.currentPage);
        searchCriteria.currentPage++;
      }
    }
    return allScopes;
  }

  async retrieveProductImages(items: Record<string, any>[]): Promise<Record<string, any>[]> {
    if (!this.defaultStoreId_ || !this.defaultCurrencyCode_) {
      throw new Error(
        "Default Store ID and Default Currency Code must be set first."
      );
    }

    const { data } = await this.sendRequest(`/products-render-info?${this.formatSearchCriteriaQuery({
      currentPage: 1,
      pageSize: items.length,
      filterGroups: [
        [
          {
            field: "entity_id",
            value: items.map((item) => item.id).join(","),
            condition_type: "in"
          }
        ]
      ],
      storeId: this.defaultStoreId_,
      currencyCode: this.defaultCurrencyCode_
    })}`);

    return items.map((item) => {
      const itemData = data.items.find((i) => i.id == item.id);
      if (itemData) {
        item.images = itemData.images || [];
      }

      return item;
    });
  }

  async retrieveDefaultConfigs() {
    if (this.defaultImagePrefix_) {
      return;
    }

    const { data } = await this.sendRequest(`/store/storeConfigs`);

    const defaultStore = data.length ? data.find((store) => store.code === "default") : data;

    if (!this.defaultImagePrefix_) {
      this.defaultImagePrefix_ = `${defaultStore.base_media_url}catalog/product`;
    }
  }

  async retrieveOptionValues(title: string): Promise<Record<string, any>[]> {
    return this.sendRequest(`/products/attributes/${title}`).
      then(({ data }) => {
        return data.options.filter((values) => values.value.length > 0);
      });
  }

  async retrieveOptions(): Promise<Record<string, any>[]> {
    const searchCriteria: SearchCriteria = {
      currentPage: 1,
      pageSize: 500
    };

    return this.sendRequest(`/products/attributes?${this.formatSearchCriteriaQuery(searchCriteria)}`).
      then(({ data }) => {
        return data.items;
      });
  }

  async retrieveInventoryData(sku: string): Promise<AxiosResponse<any>> {
    return this.sendRequest(`/stockItems/${encodeURIComponent(sku)}`);
  }

  async retrieveSimpleProductsAsVariants(productIds: string[]): Promise<Record<string, any>[]> {
    return this.retrieveProducts(MagentoProductTypes.SIMPLE, null, [
      [
        {
          field: "entity_id",
          value: productIds.join(","),
          condition_type: "in"
        }
      ]
    ]).
      then(async (products) => {
        return await Promise.all(products.map(async (variant) => {
          const { data } = await this.retrieveInventoryData(variant.sku);

          return {
            ...variant,
            stockData: data
          };
        }));
      });
  }

  async retrieveCategories(lastUpdatedTime?: string): Promise<AxiosResponse<any>> {
    const searchCriteria: SearchCriteria = {
      currentPage: 1,
      pageSize: 500,
      filterGroups: [
        [
          {
            field: "name",
            value: "Root Catalog,Default Category",
            condition_type: "nin"
          }
        ]
      ]
    };

    if (lastUpdatedTime) {
      searchCriteria.filterGroups.push([
        {
          field: "updated_at",
          value: lastUpdatedTime,
          condition_type: "gt"
        },
      ]);
    }

    return await this.sendRequest(`/categories/list?${this.formatSearchCriteriaQuery(searchCriteria)}`);
  }

  async sendRequest(path: string, method: Method = "GET", data?: Record<string, any>): Promise<AxiosResponse<any>> {
    return this.client_.request({
      url: `${this.apiBaseUrl_}${path}`,
      method,
      data
    });
  }

  formatSearchCriteriaQuery(searchCriteria: SearchCriteria): string {
    let query = `searchCriteria[currentPage]=${searchCriteria.currentPage}&searchCriteria[pageSize]=${searchCriteria.pageSize}`;

    if (searchCriteria.filterGroups?.length) {
      searchCriteria.filterGroups.map((filterGroup, index) => {
        filterGroup.map((filter, filterIndex) => {
          query += `&searchCriteria[filterGroups][${index}][filters][${filterIndex}][field]=${filter.field}&searchCriteria[filterGroups][${index}][filters][${filterIndex}][value]=${filter.value}&searchCriteria[filterGroups][${index}][filters][${filterIndex}][condition_type]=${filter.condition_type}`;
        });
      });
    }

    if (searchCriteria.storeId) {
      query += `&storeId=${searchCriteria.storeId}`;
    }

    if (searchCriteria.currencyCode) {
      query += `&currencyCode=${searchCriteria.currencyCode}`;
    }

    return query;
  }
}

export default MagentoClientService;
