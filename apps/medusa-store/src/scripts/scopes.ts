import fs from "fs";
import {
  createProductsWorkflow,
  createInventoryLevelsWorkflow,
  createApiKeysWorkflow,
  linkSalesChannelsToApiKeyWorkflow,
  createShippingProfilesWorkflow,
  createProductCategoriesWorkflow,
  createFulfillmentSetsWorkflow,
  createShippingOptionsWorkflow,
  linkSalesChannelsToStockLocationWorkflow,
  updateStoresWorkflow,
} from "@medusajs/medusa/core-flows";
import { CreateInventoryLevelInput, ExecArgs } from "@medusajs/framework/types";
import {
  ContainerRegistrationKeys,
  Module,
  Modules,
  ProductStatus,
} from "@medusajs/framework/utils";
import { STORE_SCOPE_MODULE } from "src/modules/store-scope";
import {
  retrieveAttributesWorkflow,
  retrieveConfigurableProductsWorkflow,
  retrieveSimpleProductsWorkflow,
  retrieveCategoriesWorkflow
} from "../workflows/import-products";
import { BRAND_MODULE } from "../modules/brand";
import { MAGENTO_IMPORT_MODULE } from "../modules/magento-import";
import marcoProducts from "./marco-products";

const BrandMapping = {
  20: "Snap",
  18: "Nike",
  17: "ChampionTeam Wear",
  19: "Sanmar",
  21: "SSActivewear",
  22: "Strideline",
  592: "Puma",
  14: "Adidas",
  23: "Under Armour",
  591: "New Balance",
  558: "Boxercraft",
  559: "TheGame",
  16: "Bespoke",
  15: "Badger"
};

const toStoreScope = (scope) => {
  return {
    enabled: scope.enabled === "1",
    name: scope.name,
    code: scope.code,
    domain: scope.domain,
    primary_color: scope.primary_color,
    favicon: scope.favicon,
    header_logo: scope.header_logo,
    hat_logo: scope.hat_logo,
    digital_logo: scope.digital_logo,
    embroidery_logo: scope.embroidery_logo,
    stripe_credit_card_descriptor: scope.stripe_credit_card_descriptor
  };
};
export default async function seedDemoData({ container }: ExecArgs) {
  const link = container.resolve("link");
  const brandModuleService = container.resolve(BRAND_MODULE);
  const brands = await (brandModuleService as any).listBrands({});

  const brandMap = brands.reduce((map,brand) => {
    map[brand.name] = brand.id;

    return map;
  }, {});
  const storeScopesService = container.resolve(STORE_SCOPE_MODULE);
  const magentoImport = container.resolve(MAGENTO_IMPORT_MODULE);
  const scopes = await magentoImport.retrieveScopes();
  const createdScopes = await storeScopesService.createStoreScopes(scopes.map(toStoreScope));

  const scopeIdsMap = createdScopes.reduce((map, scope) => {
    map[scope.domain] = scope.id;
    return map;
  }, {});
  const links = [];
  scopes.forEach((scope) => {
    scope.whiteListManufacture?.forEach((brand) => {
      links.push({
        [STORE_SCOPE_MODULE]: {
          store_scope_id: scopeIdsMap[scope.domain],
        },
        [BRAND_MODULE]: {
          brand_id: (brandMap[BrandMapping[brand.value] || 576])
        },
      });
    });
  });

  await link.create(links);
}
