import fs from "fs";
import {
  createProductsWorkflow,
  createInventoryLevelsWorkflow,
  createApiKeysWorkflow,
  linkSalesChannelsToApiKeyWorkflow,
  createShippingProfilesWorkflow,
  createProductCategoriesWorkflow,
  createFulfillmentSetsWorkflow,
  createShippingOptionsWorkflow,
  linkSalesChannelsToStockLocationWorkflow,
  updateStoresWorkflow,
} from "@medusajs/medusa/core-flows";
import { CreateInventoryLevelInput, ExecArgs } from "@medusajs/framework/types";
import {
  ContainerRegistrationKeys,
  Module,
  Modules,
  ProductStatus,
} from "@medusajs/framework/utils";
import {
  retrieveAttributesWorkflow,
  retrieveConfigurableProductsWorkflow,
  retrieveSimpleProductsWorkflow,
  retrieveCategoriesWorkflow
} from "../workflows/import-products";
import { BRAND_MODULE } from "../modules/brand";
import marcoProducts from "./marco-products";

const BrandMapping = {
  20: "Snap",
  18: "Nike",
  17: "ChampionTeam Wear",
  19: "Sanmar",
  21: "SSActivewear",
  22: "Strideline",
  592: "Puma",
  14: "Adidas",
  23: "Under Armour",
  591: "New Balance",
  558: "Boxercraft",
  559: "TheGame",
  16: "Bespoke",
  15: "Badger"
};

export default async function seedDemoData({ container }: ExecArgs) {
  try {
    const brandModuleService = container.resolve(BRAND_MODULE);
    const brands = await (brandModuleService as any).listBrands({});

    const brandMap = brands.reduce((map,brand) => {
      map[brand.name] = brand.id;

      return map;
    }, {});

    const link = container.resolve("link");
    const logger = container.resolve(ContainerRegistrationKeys.LOGGER);
    const query = container.resolve(ContainerRegistrationKeys.QUERY);
    const salesChannelModuleService = container.resolve(Modules.SALES_CHANNEL);
    const storeModuleService = container.resolve(Modules.STORE);
    const productService = container.resolve(Modules.PRODUCT);
    const stockLocationService = container.resolve(Modules.STOCK_LOCATION);
    const apiKeyService = container.resolve(Modules.API_KEY);
    const remoteLink = container.resolve(ContainerRegistrationKeys.REMOTE_LINK);
    const fulfillmentModuleService = container.resolve(Modules.FULFILLMENT);

    logger.info("Seeding store data...");
    const [store] = await storeModuleService.listStores();
    const defaultSalesChannel = await salesChannelModuleService.listSalesChannels({
      name: "Default Sales Channel",
    });
    // Const categories = await retrieveCategoriesWorkflow(container).run();

    // const uniqueCategories = Object.values(categories.result.reduce((acc, cat) => {
    //   acc[cat.name.toLowerCase()] = cat;
    //   return acc;
    // }, {}));

    // Await createProductCategoriesWorkflow(
    //   container
    // ).run({
    //   input: {
    //     product_categories: uniqueCategories.map((category: any) => ({ name: category.name, is_active: true, handle: category.custom_attributes.find((attribute) => attribute.attribute_code === "url_key")?.value, metadata: { magento_id: category.id } })),
    //   },
    // });
    const { result: attributes } = await retrieveAttributesWorkflow(container).run();

    // Const { result: configurables } = await retrieveConfigurableProductsWorkflow(container).run();
    // const { result: simples } = await retrieveSimpleProductsWorkflow(container).run();
    // await fs.writeFileSync("./configurables.json", JSON.stringify(configurables));
    // await fs.writeFileSync("./simples.json", JSON.stringify(simples));
    const configurableJSON = await fs.readFileSync("./configurables.json", "utf-8");
    const simplesJSON = await fs.readFileSync("./simples-formatted.json", "utf-8");
    const configurables = JSON.parse(configurableJSON);
    const simples = JSON.parse(simplesJSON);
    // Return;
    const filteredConfigurables = process.env.NODE_ENV === "production" ? configurables.filter(c => c.id !== 9427).filter(c => !c.sku.startsWith("SNP")): configurables.filter(c => c.id !== 9427).filter(p => !p.sku.match("SNP-BW6620"));
    const filteredSimples = process.env.NODE_ENV === "production" ? simples.filter(product => marcoProducts.has(product?.custom_attributes?.find(v => v.attribute_code === "netsuite_id")?.value)).filter(s => !s.sku.startsWith("SNP")) : simples.filter(p => p.sku !== "SNP-BW6620-MARO-XXS");

    const cats = await productService.listProductCategories({}, {
      select: ["id", "name", "handle", "is_active", "metadata"]
    });

    await updateStoresWorkflow(container).run({
      input: {
        selector: { id: store.id },
        update: {
          supported_currencies: [
            {
              currency_code: "usd",
              is_default: true,
            },
          ],
          default_sales_channel_id: defaultSalesChannel[0].id,
        },
      },
    });

    logger.info("Seeding product data...");

    const productParams = normalizeProducts(filteredSimples, filteredConfigurables, attributes, cats, defaultSalesChannel[0].id);

    // Await createProductsWorkflow(container).run({
    //   input: {
    //     products: productParams
    //   }
    // });
    const products = await productService.listProducts();
    const links = [];

    for (const product of products) {
      try {
        links.push({
          [Modules.PRODUCT]: {
            product_id: product.id,
          },
          [BRAND_MODULE]: {
            brand_id: (brandMap[BrandMapping[product.metadata.brand] || 576])
          }
        });
      } catch (e) {
        console.log(product.metadata.brand);
        throw e;
      }
    }

    await link.create(links);

    logger.info("Finished seeding product data.");

    logger.info("Seeding inventory levels.");

    const stockLocations = await stockLocationService.listStockLocations({
      name: "US Warehouse",
    });

    if (!stockLocations.length) {
      throw new Error("Stock location 'US Warehouse' not found");
    }

    const stockLocation = stockLocations[0];

    const { data: inventoryItems } = await query.graph({
      entity: "inventory_item",
      fields: ["id"],
    });

    const inventoryLevels: CreateInventoryLevelInput[] = [];
    for (const inventoryItem of inventoryItems) {
      const inventoryLevel = {
        location_id: stockLocation.id,
        stocked_quantity: 1000000,
        inventory_item_id: inventoryItem.id,
      };
      inventoryLevels.push(inventoryLevel);
    }

    await createInventoryLevelsWorkflow(container).run({
      input: {
        inventory_levels: inventoryLevels,
      },
    });

    logger.info("Finished seeding inventory levels data.");

    logger.info("Seeding API key data...");

    const apiKeys = await apiKeyService.listApiKeys({
      title: "Webshop",
    });

    let apiKey;
    if (!apiKeys.length) {
      const { result: apiKeyResult } = await createApiKeysWorkflow(container).run({
        input: {
          api_keys: [
            {
              title: "Webshop",
              type: "publishable",
              created_by: "",
            },
          ],
        },
      });
      apiKey = apiKeyResult[0];
    } else {
      apiKey = apiKeys[0];
    }

    await linkSalesChannelsToApiKeyWorkflow(container).run({
      input: {
        id: apiKey.id,
        add: [defaultSalesChannel[0].id],
      },
    });

    logger.info("Finished seeding API key data.");

    logger.info("Seeding shipping profile data...");

    const shippingProfiles = await fulfillmentModuleService.listShippingProfiles({
      name: "Default",
    });

    let shippingProfile;
    if (!shippingProfiles.length) {
      const { result: shippingProfileResult } = await createShippingProfilesWorkflow(container).run({
        input: {
          data: [
            {
              name: "Default",
              type: "default",
            },
          ],
        },
      });
      shippingProfile = shippingProfileResult[0];
    } else {
      shippingProfile = shippingProfiles[0];
    }

    logger.info("Finished seeding shipping profile data.");

    logger.info("Seeding fulfillment sets data...");

    const fulfillmentSets = await fulfillmentModuleService.listFulfillmentSets({
      name: "US Warehouse delivery",
    });

    let fulfillmentSet;
    if (!fulfillmentSets.length) {
      const { result: fulfillmentSetResult } = await createFulfillmentSetsWorkflow(container).run({
        input: {
          data: [
            {
              name: "US Warehouse delivery",
              type: "shipping",
              service_zones: [
                {
                  name: "United States",
                  geo_zones: [
                    {
                      country_code: "us",
                      type: "country",
                    },
                  ],
                },
              ],
            },
          ],
        },
      });
      fulfillmentSet = fulfillmentSetResult[0];
    } else {
      fulfillmentSet = fulfillmentSets[0];
    }

    await remoteLink.create({
      [Modules.STOCK_LOCATION]: {
        stock_location_id: stockLocation.id,
      },
      [Modules.FULFILLMENT]: {
        fulfillment_set_id: fulfillmentSet.id,
      },
    });

    logger.info("Finished seeding fulfillment sets data.");

    logger.info("Seeding shipping options data...");

    const shippingOptions = await fulfillmentModuleService.listShippingOptions({
      name: "Standard Shipping",
    });

    if (!shippingOptions.length) {
      await createShippingOptionsWorkflow(container).run({
        input: [
          {
            name: "Standard Shipping",
            price_type: "flat",
            provider_id: "manual_manual",
            service_zone_id: fulfillmentSet.service_zones[0].id,
            shipping_profile_id: shippingProfile.id,
            type: {
              label: "Standard",
              description: "Ship in 2-3 days.",
              code: "standard",
            },
            prices: [
              {
                currency_code: "usd",
                amount: 10,
              },
            ],
            rules: [
              {
                attribute: "enabled_in_store",
                value: '"true"',
                operator: "eq",
              },
              {
                attribute: "is_return",
                value: "false",
                operator: "eq",
              },
            ],
          },
          {
            name: "Express Shipping",
            price_type: "flat",
            provider_id: "manual_manual",
            service_zone_id: fulfillmentSet.service_zones[0].id,
            shipping_profile_id: shippingProfile.id,
            type: {
              label: "Express",
              description: "Ship in 24 hours.",
              code: "express",
            },
            prices: [
              {
                currency_code: "usd",
                amount: 20,
              },
            ],
            rules: [
              {
                attribute: "enabled_in_store",
                value: '"true"',
                operator: "eq",
              },
              {
                attribute: "is_return",
                value: "false",
                operator: "eq",
              },
            ],
          },
        ],
      });
    }

    logger.info("Finished seeding shipping options data.");

    await linkSalesChannelsToStockLocationWorkflow(container).run({
      input: {
        id: stockLocation.id,
        add: [defaultSalesChannel[0].id],
      },
    });

    logger.info("Finished seeding stock location data.");
  } catch (e) {
    console.log(e);
  }
}

const removeHtmlTags = (str: string): string => {
  if ((str === null) || (str === "")) {
    return "";
  }

  str = str.toString();

  return str.replaceAll(/(<([^>]+)>)/ig, "");
};

const normalizeProducts = (simpleProducts, configurableProducts, attributes, cats, salesChannelId) => {
  return Object.values(configurableProducts.reduce((simples, product) => {
    const filteredSimples = simpleProducts.filter(p => {
      const simpleAttr = p.custom_attributes.find(p1 => p1.attribute_code === "url_key");
      const configurableAttr = product.custom_attributes.find(p1 => p1.attribute_code === "url_key");
      return simpleAttr.value.match(configurableAttr.value);
    });

    if (filteredSimples.length) {
      const normalized = normalizeProduct(product, filteredSimples, attributes, cats, salesChannelId);
      simples[normalized?.handle] = normalized;
    }

    return simples;
  }, {}));
};

const extractUrl = (url: string): string | null => {
  try {
    const urlObj = new URL(url);
    const params = new URLSearchParams(urlObj.search);
    return params.get("original");
  } catch (error) {
    console.error("Invalid URL:", error);
    return null;
  }
};

const normalizeProduct = (product: Record<string, any>, variants: any[], attributes, cats: any, salesChannelId): any => {
  const productVariantCategories = [...new Set(variants.flatMap(v => v.custom_attributes.find(attr => attr.attribute_code === "category_ids").value).flat())].
    map(categoryId => cats.find(cat => cat.name.toLowerCase() === attributes.category[categoryId].toLowerCase()).id);
  return {
    title: product.name,
    handle: product.custom_attributes?.find((attribute) => attribute.attribute_code === "url_key")?.value,
    category_ids: productVariantCategories,
    description: removeHtmlTags(product.custom_attributes?.find((attribute) => attribute.attribute_code === "description")?.value || ""),
    external_id: product.id,
    status: product.status == 1 ? ProductStatus.PUBLISHED : ProductStatus.DRAFT,
    images: product.media_gallery_entries?.map((img) => ({ url: img.url, rank: img.priority })) || [],
    thumbnail: product.custom_attributes?.find((attr) => attr.attribute_code === "thumbnail")?.value,
    options: [
      {
        title: "Size",
        values: [...new Set(variants.map(v => attributes.size[v.custom_attributes.find(attr => attr.attribute_code === "size").value]))]
      },
      {
        title: "Color",
        values: [...new Set(variants.map(v => attributes.color[v.custom_attributes.find(attr => attr.attribute_code === "color")?.value]))]
      }
    ],
    variants: variants.map(v => normalizeVariant(v, attributes)),
    sales_channels: [
      {
        id: salesChannelId,
      },
    ],
    metadata: {
      brand: product.custom_attributes.find(attr => attr.attribute_code === "manufacturer")?.value || "20"
    }
  };
};

const parsePrice = (price: any): number => {
  return price;
};

const normalizeVariant = (variant: any, attributes) => {
  return {
    title: attributes.size[variant.custom_attributes.find(a => a.attribute_code === "size").value],
    prices: [{
      amount: parsePrice(variant.price),
      currency_code: "usd"
    }],
    sku: variant.sku,
    weight: variant.weight || 0,
    images: variant.media_gallery_entries?.map((img) => ({ url: img.url, rank: img.position })) || [],
    options: {
      Size: attributes.size[variant.custom_attributes.find(a => a.attribute_code === "size").value],
      Color: attributes.color[variant.custom_attributes.find(a => a.attribute_code === "color").value]
    },
    metadata: {
      magento_id: variant.id,
      netsuite_id: variant?.custom_attributes?.find(v => v.attribute_code === "netsuite_id")?.value,
      images: variant.media_gallery_entries?.map((img) => ({ url: img.url, priority: img.priority })) || [],
      thumbnail: variant.custom_attributes?.find(v => v.attribute_code === "thumbnail")?.value,
      small_image: variant.custom_attributes?.find(v => v.attribute_code === "small_image")?.value
    }
  };
};
