import {
  BRAND_MODULE
} from "../modules/brand";

export default async function seedBrand({ container }) {
  const brandModuleService = container.resolve(BRAND_MODULE);
  const brands = ["TheGame", "Strideline", "Sanmar", "SSActivewear","Puma", "Nike", "New Balance", "ChampionTeam Wear", "Boxercraft", "Bespoke", "Badger", "Adidas", "Under Armour", "Snap", "Strideline"];
  const existingBrands = await brandModuleService.listBrands({
    name: brands
  });

  const brandSet = new Set(brands);
  existingBrands.forEach(brand => {
    brandSet.delete(brand.name);
  });

  const newBrands = [...brandSet];


  await brandModuleService.createBrands(newBrands.map(name => ({ name })));
}
