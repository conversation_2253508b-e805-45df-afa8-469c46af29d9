/* eslint-disable max-lines-per-function */
/* eslint-disable id-denylist, func-style, import/prefer-default-export */
import { MedusaRequest, MedusaResponse } from "@medusajs/framework";
import { getOrderDetailWorkflow, createOrderFulfillmentWorkflow, completeOrderWorkflow } from "@medusajs/medusa/core-flows";

export async function PATCH(
  req: MedusaRequest,
  res: MedusaResponse
): Promise<void> {
  try {
    const { trackingInfo } = req.body as {
      trackingInfo?: {
        netsuiteOrderId?: string | null,
        carrierName?: string | null,
        trackingNumber?: string | null,
        trackingUrl?: string | null,
        orderStatus?: string | null,
      }
    };
    // Validate tracking is present
    if (!trackingInfo?.trackingNumber) {
      throw new Error(`Prop trackingInfo.trackingNumber is required.`);
    }

    const { result } = await getOrderDetailWorkflow(req.scope).run({
      input: {
        order_id: req.params.id,
        fields: ["id", "status", "items.*"]
      }
    });

    await createOrderFulfillmentWorkflow(req.scope).run({
      input: {
        order_id: req.params.id,
        items: result.items?.map(i => ({ id: i.id, quantity: i.quantity })) ?? [],
        labels: [{
          tracking_number: `${trackingInfo.trackingNumber}`,
          tracking_url: `${trackingInfo.trackingUrl}`,
          label_url: ``
        }]
      }
    });

    await completeOrderWorkflow(req.scope).run({
      input: {
        orderIds: [req.params.id],
      }
    });

    res.status(200).json({ status: "ok", success: true });
  } catch (error) {
    res.status(400).json({ message: error.message, success: false });
  }
}
