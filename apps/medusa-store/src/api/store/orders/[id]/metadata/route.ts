/* eslint-disable func-style, import/prefer-default-export */
/* eslint-disable id-denylist */
import { MedusaRequest, MedusaResponse } from "@medusajs/framework";
import { Modules } from "@medusajs/framework/utils";

export async function PATCH(
  req: MedusaRequest,
  res: MedusaResponse
): Promise<void> {
  const { id } = req.params;
  const body = req.body as { metadata: Record<string, unknown> };
  const metadata = body.metadata;
  const orderService = req.scope.resolve(Modules.ORDER);
  try {
    const order = await orderService.retrieveOrder(id);
    const newMetadata = { ...order.metadata, ...metadata };
    await orderService.updateOrders([{ id: id, metadata: newMetadata }]);
    res.status(200).json({ status: "ok", success: true });
  } catch (error) {
    res.status(400).json({ message: error.message, success: false });
  }
}
