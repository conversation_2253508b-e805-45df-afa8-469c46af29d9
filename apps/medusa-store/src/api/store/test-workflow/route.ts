/* eslint-disable id-denylist, func-style, import/prefer-default-export */
import { MedusaRequest, MedusaResponse } from "@medusajs/framework/http";
import { orderFulfillmentWorkflow } from "../../../workflows/orders";

export async function GET(
  _req: MedusaRequest,
  res: MedusaResponse
): Promise<void> {
  const order = await orderFulfillmentWorkflow.run({ input: { id: "order_01JTP68MDDFRB4WZ3006TFVYFA" } });
  res.send(order);
}
