/* eslint-disable func-style, import/prefer-default-export */
/* eslint-disable id-denylist */
import { MedusaRequest, MedusaResponse } from "@medusajs/framework";
import { Modules } from "@medusajs/framework/utils";

export async function GET(
  req: MedusaRequest,
  res: MedusaResponse
): Promise<void> {
  try {
    const netsuiteId = req.query.netsuite_id;

    if (!netsuiteId || typeof netsuiteId !== "string") {
      res.status(400).json({ message: "Invalid or missing netsuite_id", success: false });
    }

    const productVariantService = req.scope.resolve(Modules.PRODUCT);

    const variants = await productVariantService.listProductVariants({
      metadata: {
        netsuite_id: netsuiteId,
      }
    });

    res.status(200).json({ status: "ok", success: true, variant: variants[0] || null });
  } catch (error) {
    res.status(400).json({ message: error.message, success: false });
  }
}
