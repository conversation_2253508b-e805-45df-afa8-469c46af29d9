/* eslint-disable func-style, import/prefer-default-export */
/* eslint-disable id-denylist */
import { MedusaRequest, MedusaResponse } from "@medusajs/framework";
import { Modules } from "@medusajs/framework/utils";

export async function PATCH(
  req: MedusaRequest,
  res: MedusaResponse
): Promise<void> {
  const { id } = req.params;
  const body = req.body as { metadata: Record<string, unknown> };
  const metadata = body.metadata;
  const paymentService = req.scope.resolve(Modules.PAYMENT);
  try {
    const session = await paymentService.retrievePaymentSession(id);
    const initialMetadata = session.data.metadata as Record<string, unknown>;
    const newMetadata = { ...initialMetadata, ...metadata };
    await paymentService.updatePaymentSession({ id, currency_code: session.currency_code, amount: session.amount, data: { metadata: newMetadata } });
    res.status(200).json({ status: "ok", success: true });
  } catch (error) {
    res.status(400).json({ message: error.message, success: false });
  }
}
