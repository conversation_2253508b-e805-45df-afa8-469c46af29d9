/* eslint-disable max-lines-per-function */
/* eslint-disable id-denylist, func-style, import/prefer-default-export */
import { MedusaRequest, MedusaResponse } from "@medusajs/framework";

import { ADDRESS_VALIDATOR_MODULE } from "../../../../modules/address-validator";
import AddressService from "../../../../modules/address-validator/service";

export async function POST(
  req: MedusaRequest,
  res: MedusaResponse
): Promise<void> {
  const addressService: AddressService = req.scope.resolve(ADDRESS_VALIDATOR_MODULE);
  const resp = await addressService.validate(req.body);

  res.status(200).send(resp);
}
