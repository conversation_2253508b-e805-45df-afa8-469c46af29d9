/* eslint-disable func-style, import/prefer-default-export */
/* eslint-disable id-denylist */
import { MedusaRequest, MedusaResponse } from "@medusajs/framework";
import { Modules } from "@medusajs/framework/utils";

export async function PATCH(
  req: MedusaRequest,
  res: MedusaResponse
): Promise<void> {
  const { scopeId } = req.params;
  const body = req.body as { metadata: Record<string, unknown> };

  try {
    res.status(200).json({ status: "ok", success: true });
  } catch (error) {
    res.status(400).json({ message: error.message, success: false });
  }
}
