import {
  MedusaNextFunction,
  MedusaRequest,
  MedusaResponse,
  defineMiddlewares,
} from "@medusajs/framework/http";
import { PrismaClient } from "@prisma/client";
import { ContainerRegistrationKeys } from "@medusajs/framework/utils";
import { QueryContext } from "@medusajs/framework/utils";
import { STORE_SCOPE_MODULE } from "../modules/store-scope";
const BrandMapping = {
  20: "Snap",
  18: "Nike",
  17: "ChampionTeam Wear",
  19: "Sanmar",
  21: "SSActivewear",
  22: "Strideline",
  592: "Puma",
  14: "Adidas",
  23: "Under Armour",
  591: "New Balance",
  558: "Boxercraft",
  559: "TheGame",
  16: "Bespoke",
  15: "Badger"
};
export default defineMiddlewares({
  routes: [
    {
      matcher: "/store/products",
      method: ["GET"],
      middlewares: [
        async (
          req: MedusaRequest,
          res: MedusaResponse,
          next: MedusaNextFunction
        ) => {
          const { storeCode } = req.query;
          delete req.query.storeCode;
          if (!storeCode) {
            console.log("No storeCode provided in the request.");
            return next();
          }

          try {
            const query = req.scope.resolve(ContainerRegistrationKeys.QUERY);
            const { data } = await query.graph({
              entity: "store_scope",
              fields: ["*", "brand.*"],
              filters: { domain: { $like: `${storeCode}%` } } // Specify the fields you want to retrieve
            });
            const store = data[0];
            if (!store) {
              console.log("Store not found for storeCode:", storeCode);
              return next();
            }
            const allowedBrands = new Set(store.brand.map(b => b.name));
            allowedBrands.add("Snap");
            const logoUrl = store.digital_logo;

            if (!logoUrl) {
              return next();
            }

            const cleanedUrl = logoUrl.replace(/.*\/([^/]+\.(svg|png))$/, "https://s3.us-west-1.amazonaws.com/snapraiselogos/PrinterLogos/$1").replace(/\.svg$/, ".png");

            const originalJson = res.json;
            res.json = (data) => {
              if (data.products) {
                data.products = data.products.map((product) => {
                  if (product.thumbnail) {
                    const url = new URL(product.thumbnail);
                    url.searchParams.set("overlay", cleanedUrl);
                    product.thumbnail = url.toString();
                    product.variants = product.variants.map(v => {
                      const thumbnail = new URL(v.metadata.thumbnail);
                      const small_image = new URL(v.metadata.small_image);
                      thumbnail.searchParams.set("overlay", cleanedUrl);
                      small_image.searchParams.set("overlay", cleanedUrl);
                      v.metadata.thumbnail = thumbnail.toString();
                      v.metadata.small_image = small_image.toString();
                      v.metadata.logo_url = cleanedUrl.replace(/PrinterLogos/, "MarcoLogos");
                      return v;
                    });
                  }
                  return product;
                }).filter(p => {
                  return allowedBrands.has(BrandMapping[p.metadata.brand]);
                });
                data.count = data.products.length;
              }
              return originalJson.call(res, data);
            };

            next();
          } catch (error) {
            console.error("Error looking up store:", error);
            next();
          }
        },
      ],
    },
  ],
});
