import {
  createStep,
  StepResponse
} from "@medusajs/framework/workflows-sdk";
import { MAGENTO_IMPORT_MODULE } from "../../../modules/magento-import";

export const importCategoriesStep = createStep(
  "import-categories-step",
  async (input: null, { container }) => {
    const magentoImportModuleService: MagentoClientService = container.resolve(
      MAGENTO_IMPORT_MODULE
    );

    const { data } = await magentoImportModuleService.retrieveCategories();

    return new StepResponse(data.items);
  }
);
