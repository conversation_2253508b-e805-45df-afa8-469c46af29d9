import {
  createStep,
  StepResponse
} from "@medusajs/framework/workflows-sdk";
import { MAGENTO_IMPORT_MODULE } from "../../../modules/magento-import";
import { MagentoProductTypes } from "../../../modules/magento-import/service";

export const retrieveAttributesStep = createStep(
  "import-attributes-step",
  async (input: null, { container }) => {
    const magentoImportModuleService: MagentoClientService = container.resolve(
      MAGENTO_IMPORT_MODULE
    );

    const data = await magentoImportModuleService.retrieveAttributes();

    return new StepResponse(data);
  }
);

export const retrieveConfigurableProductsStep = createStep(
  "import-configurable-products-step",
  async (input: null, { container }) => {
    const magentoImportModuleService: MagentoClientService = container.resolve(
      MAGENTO_IMPORT_MODULE
    );

    const data = await magentoImportModuleService.retrieveProducts(MagentoProductTypes.CONFIGURABLE);

    return new StepResponse(data);
  }
);

export const retrieveSimpleProductsStep = createStep(
  "import-simple-products-step",
  async (input: null, { container }) => {
    const magentoImportModuleService: MagentoClientService = container.resolve(
      MAGENTO_IMPORT_MODULE
    );

    const products = await magentoImportModuleService.retrieveProducts(MagentoProductTypes.SIMPLE);
    return new StepResponse(products, 1);
  }
);
