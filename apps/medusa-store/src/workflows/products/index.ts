import {
  createWorkflow,
  WorkflowResponse
} from "@medusajs/framework/workflows-sdk";
import { retrieveConfigurableProductsStep, retrieveSimpleProductsStep, retrieveAttributesStep } from "./steps";

export const retrieveConfigurableProductsWorkflow = createWorkflow(
  "import-configurable-products",
  (input: null, context) => {
    const configurableProducts = retrieveConfigurableProductsStep(null, context);
    return new WorkflowResponse(configurableProducts);
  }
);

export const retrieveSimpleProductsWorkflow = createWorkflow(
  "import-simple-products",
  (input: null, context) => {
    const simpleProducts = retrieveSimpleProductsStep(null, context);
    return new WorkflowResponse(simpleProducts);
  }
);

export const retrieveAttributesWorkflow = createWorkflow(
  "import-attributes",
  (input: null, context) => {
    const attributes = retrieveAttributesStep(null, context);
    return new WorkflowResponse(attributes);
  }
);
