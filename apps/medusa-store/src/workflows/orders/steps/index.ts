/* eslint-disable max-lines-per-function */
/* eslint-disable @typescript-eslint/no-explicit-any, import/prefer-default-export, id-denylist, import/group-exports, max-len */
import {
  createStep,
  StepResponse
} from "@medusajs/framework/workflows-sdk";
import {
  Modules
} from "@medusajs/framework/utils";
import { GRAPHQL_CLIENT_MODULE } from "../../../modules/graphql";
import { OrderProductInputMedusa, OrderInput, ShippingProvider, Vendor } from "../types";

export const retrieveOrderStep = createStep(
  "retrieve-order-step",
  async (input: { id: string }, { container }) => {
    const orderService = container.resolve(Modules.ORDER);
    const order = await orderService.retrieveOrder(input.id, { relations: ["items", "shipping_address", "billing_address", "shipping_methods"] });

    return new StepResponse(order);
  }
);
export const mapOrderToGraphStep = createStep(
  "map-order-to-graph-step",
  async (input: any) => {
    const { first_name, last_name, address_1, address_2, city, postal_code, province, email } = input.shipping_address;
    const { address_1: billing_address_1, address_2: billing_address_2, city: billing_city, postal_code: billing_postal_code, province: billing_province } = input.billing_address;
    const shipTo = `${first_name} ${last_name}`;
    const expandedProductlist = input.items.flatMap((item: OrderProductInputMedusa) => {
      return Array.from({ length: item.quantity || 1 }, () => ({
        receiverName: shipTo,
        netsuiteId: item.metadata?.netsuiteId || "",
        sku: item.variant_sku,
        amount: item.unit_price,
        logo: item.metadata?.logoUrl,
        quantity: 1,
      }));
    });
    const data: OrderInput = {
      orderId: input.id,
      confirmationId: input.id,
      vendor: Vendor.Store,
      carrier: ShippingProvider.Usps,
      shipToEmail: email,
      shipTo,
      street: address_1,
      street2: address_2,
      city: city,
      state: province,
      zipCode: postal_code,
      packingSlipId: input.metadata?.netsuiteId || "",
      packingSlipTitle: "",
      products: expandedProductlist,
      billingCity: billing_city,
      billingState: billing_province,
      billingStreet: billing_address_1,
      billingStreet2: billing_address_2,
      billingZipCode: billing_postal_code,
      source: "MEDUSA",
      shippingCost: input.shipping_methods[0].amount
    };

    return new StepResponse(data);
  }
);

export const sendOrderToGraphStep = createStep(
  "send-order-to-graph-step",
  async (order: OrderInput, { container }) => {
    const client = container.resolve(GRAPHQL_CLIENT_MODULE);
    const graphResponse = await client.createStoreOrder(order);

    return new StepResponse(graphResponse);
  }
);
