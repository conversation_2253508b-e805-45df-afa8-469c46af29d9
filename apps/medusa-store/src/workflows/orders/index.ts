import {
  createWorkflow,
  WorkflowResponse
} from "@medusajs/framework/workflows-sdk";
import { retrieveOrderStep, mapOrderToGraphStep, sendOrderToGraphStep } from "./steps";

export const orderFulfillmentWorkflow = createWorkflow(
  "order-fulfillment",
  (input: { id: string }, context) => {
    const orderResponse = retrieveOrderStep(input, context);
    const mapResponse = mapOrderToGraphStep(orderResponse);
    const graphResponse = sendOrderToGraphStep(mapResponse, context);
    return new WorkflowResponse(graphResponse);
  }
);
