import { SubscriberArgs, type SubscriberConfig } from "@medusajs/framework";
import { orderFulfillmentWorkflow } from '../workflows/orders'
export default async function orderPlacedHandler({
  event: { data },
  container,
}: SubscriberArgs<{ id: string }>) {
  const logger = container.resolve("logger");

  logger.info(`Got order placed event ${JSON.stringify(data)}`);
  await orderFulfillmentWorkflow.run({ input: { id: data.id } });
  logger.info(`Workflow for order ${data.id} created`);

}

export const config: SubscriberConfig = {
  event: `order.placed`,
};
