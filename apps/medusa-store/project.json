{"name": "medusa-store", "$schema": "../../node_modules/nx/schemas/project-schema.json", "projectType": "application", "tags": [], "targets": {"build": {"executor": "nx:run-commands", "options": {"command": "medusa build", "cwd": "apps/medusa-store"}}, "dev": {"executor": "nx:run-commands", "options": {"command": "medusa develop", "cwd": "apps/medusa-store"}}, "serve": {"executor": "nx:run-commands", "options": {"command": "medusa start", "cwd": ".medusa/server"}}, "db-migrate": {"executor": "nx:run-commands", "options": {"command": "npx medusa db:migrate", "cwd": "apps/medusa-store"}}}}