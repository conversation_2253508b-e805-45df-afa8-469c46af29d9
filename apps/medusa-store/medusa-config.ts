/* eslint-disable import/no-unused-modules */
/* eslint-disable import/no-commonjs */
/* eslint-disable functional/immutable-data */
import { loadEnv, defineConfig } from "@medusajs/framework/utils";

loadEnv(process.env.NODE_ENV || "development", process.cwd());
module.exports = defineConfig({
  projectConfig: {
    databaseUrl: process.env.MEDUSA_DATABASE_URL,
    redisUrl: process.env.REDIS_URL,
    http: {
      storeCors: process.env.STORE_CORS!,
      adminCors: process.env.ADMIN_CORS!,
      authCors: process.env.AUTH_CORS!,
      jwtSecret: process.env.JWT_SECRET || "supersecret",
      cookieSecret: process.env.COOKIE_SECRET || "supersecret",
    }
  },
  modules: [
    {
      resolve: "./src/modules/magento-import",
      options: {
        magento_url: process.env.MAGENTO_BASE_URL,
        consumer_key: process.env.MAGENTO_CONSUMER_KEY,
        consumer_secret: process.env.MAGENTO_CONSUMER_SECRET,
        access_token: process.env.MAGENTO_ACCESS_TOKEN,
        access_token_secret: process.env.MAGENTO_ACCESS_TOKEN_SECRET,
        inject: ["manager"]
      },
      definition: {
        isQueryable: false,
      }
    },
    {
      resolve: "./src/modules/graphql",
      options: {
        graphql_url: process.env.GRAPHQL_URL,
        graphql_api_key: process.env.GRAPHQL_API_KEY,
        inject: ["manager"]
      },
      definition: {
        isQueryable: false,
      }
    },
    {
      resolve: "./src/modules/address-validator",
      options: {
        google_validation_api_key: process.env.GOOGLE_VALIDATION_API_KEY,
        inject: ["manager"]
      }
    },
    {
      resolve: "./src/modules/brand"
    },
    {
      resolve: "./src/modules/store-scope"
    },
    {
      resolve: "@medusajs/medusa/payment",
      options: {
        providers: [
          {
            resolve: "@medusajs/medusa/payment-stripe",
            id: "stripe",
            options: {
              apiKey: process.env.STRIPE_API_KEY,
              capture: true,
              payment_description: "Snap!Store"
            }
          }
        ]
      }
    }
  ]
});
