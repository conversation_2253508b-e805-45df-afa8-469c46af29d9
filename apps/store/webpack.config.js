const { NxAppWebpackPlugin } = require('@nx/webpack/app-plugin');
const { join } = require('path');
const CopyWebpackPlugin = require('copy-webpack-plugin');

module.exports = {
  output: {
    path: join(__dirname, '../../dist/apps/store'),
  },
  entry: {
    main: './main.ts',
    worker: './src/temporal/worker.ts'
  },
  plugins: [
    new NxAppWebpackPlugin({
      target: 'node',
      compiler: 'tsc',
      main: './main.ts',
      tsConfig: './tsconfig.app.json',
      optimization: false,
      outputHashing: 'none',
    }),
    new CopyWebpackPlugin({
      patterns: [
        { from: './src/api/graphql/schema.graphql', to: 'schema.graphql' },
        { from: './src/temporal/workflows', to: 'workflows' }
      ],
    }),
  ],
};
