{"name": "store", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/store/src", "projectType": "application", "tags": [], "targets": {"copy-graphql": {"executor": "nx:run-commands", "options": {"command": "cp apps/store/src/api/graphql/schema.graphql dist/apps/store/src/api/graphql"}}, "build-store": {"executor": "nx:run-commands", "options": {"command": "npx tsc -p apps/store/tsconfig.app.json && npx resolve-tspaths -p apps/store/tsconfig.json"}, "outputs": ["{workspaceRoot}/dist/apps/store"]}, "prisma-generate": {"executor": "nx:run-commands", "options": {"command": "npx prisma generate --schema=apps/store/src/prisma/schema.prisma"}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"]}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "apps/store/jest.config.ts"}}, "serve": {"executor": "nx:run-commands", "defaultConfiguration": "development", "options": {"commands": [{"command": "nx run store:build"}]}, "configurations": {"development": {"commands": [{"command": "nx run store:build"}, {"command": "concurrently \"npm:worker:start\" \"npm:store:start\""}]}, "production": {"buildTarget": "store:build:production"}}}, "build": {"executor": "nx:run-commands", "defaultConfiguration": "development", "options": {"commands": [{"command": "nx run store:prisma-generate"}, {"command": "npx tsc -p apps/store/tsconfig.app.json && npx resolve-tspaths -p apps/store/tsconfig.app.json"}, {"command": "cp apps/store/src/api/graphql/schema.graphql dist/apps/store/src/api/graphql"}]}, "configurations": {"production": {"commands": [{"command": "nx run store:prisma-generate"}, {"command": "npx tsc -p apps/store/tsconfig.app.json && npx resolve-tspaths -p apps/store/tsconfig.app.json"}, {"command": "cp apps/store/src/api/graphql/schema.graphql dist/apps/store/src/api/graphql"}]}, "development": {"commands": [{"command": "nx run store:prisma-generate"}, {"command": "npx tsc -p apps/store/tsconfig.app.json && npx resolve-tspaths -p apps/store/tsconfig.app.json"}, {"command": "cp apps/store/src/api/graphql/schema.graphql dist/apps/store/src/api/graphql"}], "parallel": false}}, "outputs": ["{workspaceRoot}/dist/apps/store"]}, "start-both": {"executor": "nx:run-commands", "options": {"commands": [{"command": "npx concurrently \"npm:worker:start\" \"npm:store:start\""}], "cwd": "{workspaceRoot}", "parallel": false}}}}