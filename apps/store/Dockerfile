# This file is generated by Nx.
#
# Build the docker image with `npx nx docker-build store`.
# Tip: Modify "docker-build" options in project.json to change docker build args.
#
# Run the container with `docker run -p 3000:3000 -t store`.
FROM docker.io/node:lts-alpine

ENV HOST=0.0.0.0
ENV PORT=3000

WORKDIR /app

RUN addgroup --system store && \
          adduser --system -G store store

COPY dist/apps/store store/
RUN chown -R store:store .

# You can remove this install step if you build with `--bundle` option.
# The bundled output will include external dependencies.
RUN npm --prefix store --omit=dev -f install

CMD [ "node", "store" ]
