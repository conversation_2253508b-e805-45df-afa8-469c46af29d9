/* eslint-disable max-statements */
/* eslint-disable functional/no-return-void */
import express, { Express, Request, Response } from "express";
import { EasypostEventInput, Vendor } from "@store-monorepo/graphql/types";
import Graphql from "@store-monorepo/graphql";
import Repo from "@store-monorepo/repo";
import EasyPost from "../../lib/easypost";
const mapToEasyPostInput = (payload, order): EasypostEventInput => {
  const { id: eventId, result } = payload;
  const { status, tracking_code, shipment_id, id: trackerId, carrier, est_delivery_date, status_detail } = result;

  const event: EasypostEventInput = {
    eventId: eventId,
    tracking_number: tracking_code,
    trackerId: trackerId,
    shipmentId: shipment_id,
    referenceId: order.externalId,
    status: status,
    carrier: carrier,
    est_delivery_date: est_delivery_date,
    statusDetail: status_detail,
    orderId: order.id,
    vendor: order.vendor as Vendor,
  };
  return event;
};
export default function addEasypostWebhook(server: Express) {
  server.post(
    "/api/webhooks/easypost",
    express.json({ limit: "10mb" }),
    async (req: Request, resp: Response) => {
      try {
        const payload = req.body;
        const { object, description, result } = payload;
        const { shipment_id } = result;
        if (object !== "Event" || description !== "tracker.updated") {
          return resp.status(422).json({ error: "Invalid easypost webhook event" });
        }
        const shipment = await EasyPost.getShipment(shipment_id);
        const referenceId = shipment.reference;
        if (!referenceId) {
          return resp.status(422).json({
            error: "Missing required fields: tracking_number and status are required"
          });
        }

        const order = await Repo.Order.findFirst({
          externalId: referenceId
        });

        if (!order) {
          return resp.status(404).json({ error: "Order not found" });
        }

        const event = mapToEasyPostInput(payload, order);
        const { success, error } = await Graphql.easypostWebhook(event);

        return success
          ? resp.sendStatus(200)
          : resp.status(500).end(error);
      } catch (error) {
        console.error("Error processing Easypost webhook:", error);
        return resp.status(500).json({ error: "Internal server error Easypost webhook" });
      }
    });
}
