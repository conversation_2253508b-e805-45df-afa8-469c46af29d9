/* eslint-disable functional/no-return-void */
import express, { Express, Request, Response } from "express";
import { EasypostEventInput, Vendor } from "@store-monorepo/graphql/types";
import Graphql from "@store-monorepo/graphql";
import Repo from "@store-monorepo/repo";
import EasyPost from "../../lib/easypost";
type EasyPostPayload = {
  id: string;
  object: string;
  description: string;
  result: EasyPostPayloadResult;
}
type EasyPostPayloadResult = {
  status: string,
  tracking_code: string,
  shipment_id: string,
  id: string,
  carrier: string,
  est_delivery_date: string | null,
  status_detail: string,
  public_url: string | null
}
type Order = Awaited<ReturnType<typeof Repo.Order["findFirst"]>>;

const mapToEasyPostInput = (payload: EasyPostPayload, order: Order): EasypostEventInput => {
  const { id: eventId, result } = payload;
  const { public_url, status, tracking_code, shipment_id, id: trackerId, carrier, est_delivery_date, status_detail } = result;

  const event: EasypostEventInput = {
    eventId,
    trackingNumber: tracking_code,
    trackerId,
    shipmentId: shipment_id,
    referenceId: order.externalId,
    status,
    carrier,
    estDeliveryDate: est_delivery_date,
    statusDetail: status_detail,
    orderId: order.id,
    trackingUrl: public_url,
    vendor: order.vendor as Vendor,
  };
  return event;
};


const validateAndGetReferenceId = async (payload: EasyPostPayload): Promise<string> => {
  const { object, description, result } = payload;
  const { shipment_id } = result;

  if (object !== "Event" || description !== "tracker.updated") {
    throw new Error("Invalid easypost webhook event");
  }

  const shipment = await EasyPost.getShipment(shipment_id);
  const referenceId = shipment.reference;

  if (!referenceId) {
    throw new Error("Missing required fields: tracking_number and status are required");
  }

  return referenceId;
};

const processWebhook = async (payload: EasyPostPayload, order: Order): Promise<boolean> => {
  const event = mapToEasyPostInput(payload, order);
  const { success, error } = await Graphql.easypostWebhook(event);

  if (!success) {
    console.error("GraphQL webhook error:", error);
  }

  return success;
};

const handleEasypostWebhook = async (req: Request, resp: Response) => {
  try {
    const payload = req.body;
    const referenceId = await validateAndGetReferenceId(payload);

    const order = await Repo.Order.findFirst({ externalId: referenceId });
    if (!order) {
      return resp.status(404).json({ error: "Order not found" });
    }
    const success = await processWebhook(payload, order);

    return success ? resp.sendStatus(200) : resp.status(500).end("Webhook processing failed");
  } catch (error) {
    console.error("Error processing Easypost webhook:", error);
    return resp.status(500).json({ error: "Internal server error Easypost webhook" });
  }
};

export default function addEasypostWebhook(server: Express) {
  server.post(
    "/api/webhooks/easypost",
    express.json({ limit: "10mb" }),
    handleEasypostWebhook
  );
}
