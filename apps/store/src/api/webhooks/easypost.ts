import express, { Express, Request, Response} from "express";
import { EasypostEventInput, Vendor } from "@store-monorepo/graphql/types";
import Graphql from "@store-monorepo/graphql";
import Repo from "@store-monorepo/repo";

export default function addEasypostWebhook(server: Express) {
  server.post(
    "/api/webhooks/easypost",
    express.json({ limit: "10mb" }),
    async (req: Request, resp: Response) => {
      try {
        console.log("Full req.body:", JSON.stringify(req.body, null, 2));
        const payload = req.body;
        console.log("Incoming payload keys:", Object.keys(payload));
        console.log("Raw object value:", payload.object);

        if (payload?.object !== "Event" || payload?.description !== "tracker.updated") {
          return resp.status(422).json({ error: "Invalid easypost webhook event" });
        }

        const trackingNumber = payload.result.tracking_code;
        const status = payload.result.status;

        if (!trackingNumber || !status) {
          return resp.status(422).json({
            error: "Missing required fields: tracking_number and status are required"
          });
        }

        const order = await Repo.Order.findFirst({
          trackingNumber: trackingNumber,
        });

        if (!order) {
          return resp.status(404).json({ error: "Order not found" });
        }

        const event: EasypostEventInput = {
          tracking_number: trackingNumber,
          status: status,
          vendor: order.vendor as Vendor,
        };

        const { success, error } = await Graphql.easypostWebhook(event);

        return success
          ? resp.sendStatus(200)
          : resp.status(500).end(error);
      } catch (error) {
        console.error("Error processing Easypost webhook:", error);
        return resp.status(500).json({ error: "Internal server error Easypost webhook" });
      }
  });
}
