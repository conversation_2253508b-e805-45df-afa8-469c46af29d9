/* eslint-disable functional/no-return-void, max-statements */
import express, { Express, Request, Response } from "express";
import Graphql from "@store-monorepo/graphql";
import OrderNotifications from "@store-monorepo/order-notifications";
import Repo from "@store-monorepo/repo";
const apiKeyFromHeader = (req: Request): string | undefined => {
  const key = req.headers["x-store-api-key"];
  if (typeof key === "string") {
    return key;
  }
};
const addNetsuiteWebhook = (server: Express) => {
  server.post(
    "/api/webhooks/netsuite",
    express.json({ limit: "10mb" }),
    async (req: Request, resp: Response) => {
      const apiKey = apiKeyFromHeader(req);

      if (apiKey && apiKey === process.env.STORE_NETSUITE_API_KEY) {
        const event = req.body;
        if (event.eventType === "create") {
          const fields = event.record.fields;
          if (fields.custbody_parent_sales_order) {
            const netsuiteId = fields.custbody_parent_sales_order;
            const pastOrder = await Repo.Order.findFirst({ netsuiteId });
            const fundraiserId = req.body.record.fields.custbody_fundraiser_id;
            if (!pastOrder) {
              console.log(`No order found for parent salesorder ${netsuiteId} for fundraiser ${fundraiserId}, SO# ${event.record.id}`);
              resp.sendStatus(200);
              return;
            }
            const newOrder = OrderNotifications.newOrder(req.body, pastOrder?.products?.[0]?.logo);
            await Graphql.createOrder(newOrder);
          }
        } else {
          await Graphql.netsuiteWebhook(event);
        }
      }
      resp.sendStatus(200);
    });
};

export default addNetsuiteWebhook;
