# Testing Easypost

POST `v2/trackers`

body:

```json
{
  "tracker": {
    "tracking_code": "EZ1000000001",
    "carrier": "USPS"
  }
}
```

Response:

```json
{
  "id": "trk_ed031aa18c024c57bc46a1b6714041ee",
  "object": "Tracker",
  "mode": "test",
  "tracking_code": "EZ1000000001",
  "status": "pre_transit",
  "status_detail": "status_update",
  "created_at": "2025-06-16T22:20:04Z",
  "updated_at": "2025-06-16T22:20:04Z",
  "signed_by": null,
  "weight": null,
  "est_delivery_date": "2025-06-16T22:20:04Z",
  "shipment_id": null,
  "carrier": "USPS",
  "tracking_details": [
    {
      "object": "TrackingDetail",
      "message": "Pre-Shipment Info Sent to USPS",
      "description": null,
      "status": "pre_transit",
      "status_detail": "status_update",
      "datetime": "2025-05-16T22:20:04Z",
      "source": "USPS",
      "carrier_code": null,
      "tracking_location": {
        "object": "TrackingLocation",
        "city": null,
        "state": null,
        "country": null,
        "zip": null
      },
      "est_delivery_date": null
    },
    {
      "object": "TrackingDetail",
      "message": "Shipping Label Created",
      "description": null,
      "status": "pre_transit",
      "status_detail": "status_update",
      "datetime": "2025-05-17T10:57:04Z",
      "source": "USPS",
      "carrier_code": null,
      "tracking_location": {
        "object": "TrackingLocation",
        "city": "HOUSTON",
        "state": "TX",
        "country": null,
        "zip": "77063"
      },
      "est_delivery_date": null
    }
  ],
  "carrier_detail": {
    "object": "CarrierDetail",
    "service": "First-Class Package Service",
    "container_type": null,
    "est_delivery_date_local": null,
    "est_delivery_time_local": null,
    "origin_location": "HOUSTON TX, 77001",
    "origin_tracking_location": {
      "object": "TrackingLocation",
      "city": "HOUSTON",
      "state": "TX",
      "country": null,
      "zip": "77063"
    },
    "destination_location": "CHARLESTON SC, 29401",
    "destination_tracking_location": null,
    "guaranteed_delivery_date": null,
    "alternate_identifier": null,
    "initial_delivery_attempt": null
  },
  "finalized": true,
  "is_return": false,
  "public_url": "<https://track.easypost.com/djE6dHJrX2VkMDMxYWExOGMwMjRjNTdiYzQ2YTFiNjcxNDA0MWVl>",
  "fees": [
    {
      "object": "Fee",
      "type": "TrackerFee",
      "amount": "0.02000",
      "charged": false,
      "refunded": false
    }
  ]
}
```
