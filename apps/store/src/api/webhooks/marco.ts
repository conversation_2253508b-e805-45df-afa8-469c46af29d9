/* eslint-disable max-len, max-lines-per-function, capitalized-comments, functional/no-return-void, etc/no-commented-out-code, functional/immutable-data */
import express, { Express, NextFunction, Request, Response } from "express";

import Graphql from "@store-monorepo/graphql";
import { MarcoEventInput, MarcoShipment } from "@store-monorepo/graphql/types";

const MARCO_WEBHOOK_KEY = process.env.MARCO_WEBHOOK_KEY || "";

const keyFromQuery = (req: Request): string | undefined => {
  console.log("Marco webhook", JSON.stringify(req.query));
  return req.query.key as string | undefined;
};

const keyFromHeader = (req: Request): string | undefined => {
  const key = req.headers.authorization;
  if (typeof key === "string" && key.startsWith("Bearer ")) return key.substring(7);
  return undefined;
};

const addMarcoWebhook = (server: Express) => {
  server.post(
    "/api/webhooks/marco",
    express.json({ limit: "10mb" }),

    async (req: Request, resp: Response) => {
      const { address, created_at, id, purchase_order, received_at, shipments, shipped_at, status } = req.body;

      if (!id || !purchase_order) return resp.sendStatus(422);

      const event: MarcoEventInput = {
        address,
        created_at,
        id,
        purchase_order,
        received_at,
        shipments: shipments.map((s: MarcoShipment) => ({
          delivered: s.delivered,
          search_link: s.search_link,
          shipping_carrier: s.shipping_carrier,
          shipping_method: s.shipping_method,
          tracking_number: s.tracking_number
        })),
        shipped_at,
        status
      };

      if (status === "shipped" || status === "completed") {
        const { success, error } = await Graphql.marcoWebhook(event);

        return success
          ? resp.sendStatus(200)
          : resp.status(500).end(error);
      }

      return resp.sendStatus(200);
    });
};

export default addMarcoWebhook;
