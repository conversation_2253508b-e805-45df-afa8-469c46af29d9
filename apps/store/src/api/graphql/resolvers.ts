/* eslint-disable id-denylist */
/* eslint-disable functional/no-let */
/* eslint-disable max-lines, max-statements, import/max-dependencies, complexity, sonarjs/cognitive-complexity, functional/immutable-data */
import { WorkflowNotFoundError } from "@temporalio/client";
import Repo, { StoreType, prisma } from "@store-monorepo/repo";
import { log } from "@store-monorepo/logger";
import Slack from "@store-monorepo/slack-api";
import Sentry from "@store-monorepo/sentry";
import { STATUS, Prisma } from "@prisma/client";
import Graphql from "@store-monorepo/graphql";
import Temporal from "../../temporal";
import * as Workflows from "../../temporal/workflows";
import Signals from "../../temporal/workflows/signals";
import StoreActivities from "../../temporal/workflows/activities/store";
import Magento from "../../lib/magento";
import Medusa from "../../lib/medusa";
import Netsuite from "../../lib/netsuite";
import <PERSON> from "../../lib/marco";
import Order from "../../lib/order";
import Raise from "../../lib/raise";
import Validate from "../../lib/validate";
import Klaviyo from "../../lib/klaviyo";
import { inState, stateCode } from "../../lib/states";
import {
  EasypostWebhookResponse,
  MutationEasypostWebhookArgs,
  ImageInput,
  MagentoConsumerPointsLiabilityInput,
  MagentoRefundInput,
  MagentoTransactionWebhookResponse,
  MarcoWebhookResponse,
  MutationActivateStoreArgs,
  MutationCreateStoreArgs,
  MutationEscalateRaiseStoreBuildArgs,
  MutationMarcoWebhookArgs,
  MutationResendNetsuiteTrackingArgs,
  NetsuiteEventInput,
  NetsuiteWebhookResponse,
  OrderInput,
  OrderResult,
  OrderStatus,
  ProductInput,
  ProductResult,
  QueryStoreOrderArgs,
  QueryStoreSearchArgs,
  StoreOrderResult,
  StoreOrderStatus,
  StoreResult,
  StoreSearchResponse,
  StoreStatus,
  TrackingResult,
  Vendor
} from "./types";

const SLACK_ORDER_CREATION_NOTIFICATION_CHANNEL = process.env.SLACK_ORDER_CREATION_NOTIFICATION_CHANNEL || "";
const NODE_ENV = process.env.NODE_ENV || "production";

const orderWorkflowId = (params: OrderInput) => {
  const { netsuiteId, vendor, orderId, confirmationId, source } = params;
  const isMedusa = source === "MEDUSA" ? "MEDUSA-" : "";
  const suffix = vendor === Vendor.Store ? `STORE-${confirmationId?.includes("REORDER") ? confirmationId : orderId}` : netsuiteId;
  return `createOrder-${isMedusa}${suffix}`;
};

const nsWebhookWorkflowId = async (event: NetsuiteEventInput) => {
  const order = await Repo.Order.findBy({ netsuiteId: event.record.id });
  if (!order) return null;
  const source = order.source === "MEDUSA" ? "MEDUSA-" : "";
  if (order.vendor === Vendor.Store) {
    // SO_SnapV4<orderId> or SO_SnapV3<orderId>
    const orderId = order.confirmationId?.includes("REORDER") ? order.confirmationId : event.record.fields.externalid.split("SO_SnapV")[1].slice(1);
    return `createOrder-${source}STORE-${orderId}`;
  }
  const id = order.source === "MEDUSA" ? order.orderId : order.netsuiteId;
  return `createOrder-${source}${id}`;
};

const restartOrderWorkflow = async (_: null, args: { netsuiteId: string }) => {
  const order = await Repo.Order.findFirst({ netsuiteId: args.netsuiteId });
  const input = Order.toOrderInput(order);
  return await Graphql.createOrder(input, true);
};

// eslint-disable-next-line max-lines-per-function
const createStoreOrder = async (_: null, args: { order: OrderInput, restartWorkflow: boolean }): Promise<OrderResult> => {
  const params = args.order;
  const { products, vendor, netsuiteId, source } = params;
  const orderSource = source === "MEDUSA" ? "MEDUSA" : (source || null);

  const pl = Order.validateProductLogos(products);
  if (pl.valid === false) {
    await Slack.postMessage({
      channel: SLACK_ORDER_CREATION_NOTIFICATION_CHANNEL,
      text: `*Empty Product's Logo Error ${vendor}*`,
      attachments: [{
        color: NODE_ENV === "production" ? "danger" : "warning",
        fields: [
          { title: "Workflow ID", value: orderWorkflowId(params), short: true },
          { title: "NetSuite SO ID", value: netsuiteId, short: true },
          { title: "Products", value: Slack.codeBlock(pl.errors.join("\n")) },
          { title: "Environment", value: NODE_ENV, short: true },
          { title: "Source", value: orderSource },
        ]
      }]
    });
  }

  const pa = Order.validatePrintAttributes(products);
  if (pa.valid === false) {
    await Slack.postMessage({
      channel: SLACK_ORDER_CREATION_NOTIFICATION_CHANNEL,
      text: `*Custom Printing Order Error ${vendor}*`,
      attachments: [{
        color: NODE_ENV === "production" ? "danger" : "warning",
        fields: [
          { title: "Workflow ID", value: orderWorkflowId(params), short: true },
          { title: "NetSuite SO ID", value: netsuiteId, short: true },
          { title: "Input", value: Slack.codeBlock(JSON.stringify(params, null, 2)) },
          { title: "Errors", value: Slack.codeBlock(pa.errors.join("\n")) },
          { title: "Environment", value: NODE_ENV, short: true },
          { title: "Source", value: orderSource },
        ]
      }]
    });
  }
  const workflowId = orderWorkflowId(params);
  const temporalClient = await Temporal.client();

  if (args.restartWorkflow) {
    try {
      const handle = temporalClient.workflow.getHandle(workflowId);
      const description = await handle.describe();

      if (description.status.name === "RUNNING") {
        await temporalClient.workflow.start(Workflows.triggerOrderSend, {
          args: [params.netsuiteId],
          taskQueue: Temporal.TASK_QUEUE,
          workflowId: `${workflowId}-RESEND`
        });
        return { status: OrderStatus.Success, errors: [] };
      }
    } catch (error) {
      console.log(error);
      return { status: OrderStatus.Failure, errors: [error.message] };
    }
  }
  await temporalClient.workflow.start(Workflows.createOrder, {
    args: [params, args.restartWorkflow],
    taskQueue: Temporal.TASK_QUEUE,
    workflowId
  });
  return { status: OrderStatus.Success, errors: [] };
};

const createProduct = async (_: null, args: { product: ProductInput }): Promise<ProductResult> => {
  return Repo.Product.create(args.product);
};

const netsuiteWebhook = async (
  _: null,
  args: { netsuiteEvent: NetsuiteEventInput }
): Promise<NetsuiteWebhookResponse> => {
  const workflowId = await nsWebhookWorkflowId(args.netsuiteEvent);
  if (!workflowId) return;
  const client = await Temporal.client();
  try {
    const handle = client.workflow.getHandle(workflowId);

    if (!handle) {
      return { success: false, error: "Workflow not found" };
    }

    const description = await handle.describe();
    if (description.status.name !== "RUNNING") {
      return { success: false, error: "Workflow is not running" };
    }

    await handle.signal(Signals.order.updateOrder, args.netsuiteEvent);
    return { success: true, error: "" };
  } catch (_e) {
    if (!(_e instanceof WorkflowNotFoundError)) Sentry.captureException(_e);
    return { success: false, error: "Failed to signal order update" };
  }
};

const magentoTransactionWebhook = async (
  _: null,
  args: {
    magentoEvent: MagentoConsumerPointsLiabilityInput
  }): Promise<MagentoTransactionWebhookResponse> => {
  const client = await Temporal.client();
  const workflowId = `handle-points-transaction-${args.magentoEvent.transactionId}`;
  try {
    await client.workflow.start(Workflows.handlePointsTransaction, {
      taskQueue: Temporal.TASK_QUEUE,
      workflowId,
      args: [args.magentoEvent]
    });

    const createOrderWorkflowId = `createOrder-STORE-${args.magentoEvent.orderId}`;
    const handle = client.workflow.getHandle(createOrderWorkflowId);
    await handle.signal(Signals.order.createCustomerDeposit, args.magentoEvent);
    return { success: true, error: "" };
  } catch (_e) {
    Sentry.captureException(_e);
    return { success: false, error: "Failed to handle point transaction" };
  }
};

const magentoRefundWebhook = async (_: null, args: { magentoRefund: MagentoRefundInput }): Promise<MagentoTransactionWebhookResponse> => {
  const client = await Temporal.client();
  const workflowId = `handle-refund-transaction-${args.magentoRefund.orderId}`;
  try {
    await client.workflow.start(Workflows.handleRefundTransaction, {
      taskQueue: Temporal.TASK_QUEUE,
      workflowId,
      args: [args.magentoRefund]
    });
    return { success: true, error: "" };
  } catch (_e) {
    Sentry.captureException(_e);
    return { success: false, error: "Failed to handle refund transaction" };
  }
};

const uploadLogoPNG = async (_: null, args: { image: ImageInput }): Promise<OrderResult> => {
  const cl = await Temporal.client();
  await cl.workflow.start(Workflows.uploadLogoPNG, {
    taskQueue: Temporal.TASK_QUEUE,
    workflowId: `upload-logo-${args.image.url.split("/").slice(-2).
      join("/")}`,
    args: [args.image]
  });
  return { status: OrderStatus.Success };
};

const marcoWebhook = async (_: null, { status }: MutationMarcoWebhookArgs): Promise<MarcoWebhookResponse> => {
  log("graphql", "info", "marcoWebhook", status);
  const temporalClient = await Temporal.client();
  try {
    const update = Marco.getOrderUpdateInput(status);
    const dbOrder = await Repo.Order.updateWhere({ netsuiteId: status.purchase_order }, update);
    if (!dbOrder) {
      return { success: false };
    }
    if (update.trackingNumber && dbOrder.status !== STATUS.SHIPPED) {
      await temporalClient.workflow.start(Workflows.updateExternalTracking, {
        args: [dbOrder],
        taskQueue: Temporal.TASK_QUEUE,
        workflowId: `update-tracking-${dbOrder.netsuiteId}`
      });

      if (dbOrder.vendor === Vendor.Store && dbOrder.orderId) {
        await (dbOrder.source === "MEDUSA" ? Medusa.addTrackingNumbers(dbOrder) : Magento.addTrackingNumbers(dbOrder));
      }
    }

    return { success: true };
  } catch (_e) {
    Sentry.captureException(_e);
    return { success: false, error: (_e as Error).message };
  }
};

const easypostWebhook = async (_: null, { status: input }: MutationEasypostWebhookArgs): Promise<EasypostWebhookResponse> => {
  log("graphql", "info", "easypostWebhook", input);

  try {
    const temporalClient = await Temporal.client();
    const order = await Repo.Order.findFirst({
      trackingNumber: input.tracking_number
    });

    if (!order) {
      return { success: false, error: "Order not found" };
    }

    await temporalClient.workflow.start(Workflows.handleTrackingStatus, {
      args: [order, input.status],
      taskQueue: Temporal.TASK_QUEUE,
      workflowId: `order-tracking-status-${order.id}-${input.status}`
    });

    return { success: true };
  } catch (_e) {
    Sentry.captureException(_e);
    return { success: false, error: (_e as Error).message };
  }
};

const createStore = async (_: null, { store, overrideSkipLogic }: MutationCreateStoreArgs): Promise<StoreResult> => {
  const errors = [];

  if (!(await Validate.isExistingUrl(store.logoDigitalUrl))) errors.push("Logo Digital Url not found");
  if (!(await Validate.isExistingUrl(store.logoEmbroideryUrl))) errors.push("Logo Embroidery Url not found");
  if (!(await Validate.isExistingUrl(store.logoHatUrl))) errors.push("Logo Hat Url not found");
  if (!(await Validate.isExistingUrl(store.logoWebHeaderUrl))) errors.push("Logo WebHeader Url not found");

  if (!Validate.isComplexColor(store.logoPrimaryColor)) errors.push("Logo Primary Color needs to be valid RGB format");
  if (store.groupLeaderEmail && !Validate.isEmail(store.groupLeaderEmail)) errors.push("Group Leader Email needs to be a valid email format");
  if (store.accountManagerEmail && !Validate.isEmail(store.accountManagerEmail)) errors.push("Account Manager Email needs to be a valid email format");
  if (store.salesRepEmail && !Validate.isEmail(store.salesRepEmail)) errors.push("Sales Rep Email needs to be a valid email format");
  if (store.pointsPercentage < 0 || store.pointsPercentage > 15) errors.push("Points Percentage needs to be an integer between 0 and 15");
  if (!store.fundraiserId && !store.storeCode) errors.push("At least one of Fundraiser Id or Store Code is required");

  store.storeUrl = store.storeUrl.toLowerCase().replace(/^http:/, "https:");
  if (!store.storeUrl.startsWith("https:")) store.storeUrl = `https://${store.storeUrl}`;
  if (!Validate.isUrl(store.storeUrl)) errors.push("Store Url is invalid");

  store.logoPrimaryColor = store.logoPrimaryColor.
    split("|").
    map(color => color.toUpperCase() === "#FFFFFF" ? "#808080" : color).
    join("|");

  if (errors.length > 0) return { status: StoreStatus.Failure, errors };

  const client = await Temporal.client();
  await client.workflow.start(Workflows.createExternalStore, {
    taskQueue: Temporal.TASK_QUEUE,
    workflowId: `create-external-store-${Date.now()}`,
    args: [store, overrideSkipLogic]
  });

  return { status: StoreStatus.Success, errors: [] };
};

// eslint-disable-next-line max-lines-per-function
const escalateRaiseStoreBuild = async (_: null, { fundraiserId }: MutationEscalateRaiseStoreBuildArgs): Promise<StoreResult> => {
  const errors: string[] = [];
  let store: StoreType;
  let message: string;

  const existingStore = await Repo.Store.findStoreByFundraiserId(fundraiserId);

  if (!existingStore) {
    return {
      status: StoreStatus.Failure,
      errors: [`Store with fundraiser id ${fundraiserId} not found`]
    };
  }

  await StoreActivities.setStoreSchool(existingStore);

  if (existingStore.builtAt && existingStore.storeUrl) {
    return {
      status: StoreStatus.Failure,
      errors: [`Store already built at ${existingStore.builtAt.toISOString()} as ${existingStore.storeUrl}`],
      storeUrl: existingStore.storeUrl
    };
  }

  const previousStore = await Repo.Store.originalStore(existingStore);
  if (previousStore) {
    store = await Repo.Store.updateBuiltStore(existingStore, previousStore);
    message = `There was a store already for ${store.fundraiserId} & ${store.teamId}. ${store.storeUrl} has now been sent to ${store.fundraiserId} successfully.`;
  } else {
    const [verified, , logoUrls] = await Repo.Store.verifyLogos(existingStore);
    if (!verified) {
      Object.keys(logoUrls).
        filter(k => k.endsWith("VerifiedAt") && !logoUrls[k]).
        forEach(v => {
          const key = v.replace("VerifiedAt", "Url");
          return errors.push(`Logo validation failed for ${logoUrls[key]}`);
        });
    }

    if (errors.length > 0) return { status: StoreStatus.Failure, errors };

    existingStore.storeUrl = existingStore.storeUrl ?? `https://${existingStore.fundraiserEntityId}.snap.store`;

    const { magentoStoreId, magentoStoreCode, magentoManagerEmail, error, warning } = await Magento.createStore(existingStore);
    if (error) {
      return { status: StoreStatus.Failure, errors: warning ? [error, warning] : [error] };
    }

    store = await Repo.Store.updateBuiltStore({ ...existingStore, magentoStoreId, magentoStoreCode, magentoManagerEmail });
    message = `${store.storeUrl} has now been sent to ${store.fundraiserId} successfully.`;

    await StoreActivities.sendStoreBuildReadyEmail(store);
  }

  await Raise.updateStoreUrl(store);

  const { attributes: { email }, id } = await Klaviyo.createOrUpdateProfile(store);
  await Klaviyo.createSubscription(email, id);

  return { status: StoreStatus.Success, errors: [], storeUrl: store.storeUrl, message };
};

const storeOrder = async (_: null, { netsuiteId }: QueryStoreOrderArgs): Promise<StoreOrderResult> => {
  const order = await Repo.Order.findBy({ netsuiteId });
  if (order) {
    return {
      ...order,
      status: order.status as StoreOrderStatus
    };
  }
};

const resendNetsuiteTracking = async (_: null, { netsuiteId }: MutationResendNetsuiteTrackingArgs): Promise<TrackingResult> => {
  const order = await Repo.Order.findBy({ netsuiteId });
  if (!order) {
    return { status: OrderStatus.Failure };
  }
  try {
    await Netsuite.updateNetsuiteTracking(order);
    return { status: OrderStatus.Success };
  } catch (_e) {
    return { status: OrderStatus.Failure };
  }
};

// eslint-disable-next-line max-lines-per-function
const storeSearch = async (_: null, { where = {}, pagination = {} }: QueryStoreSearchArgs): Promise<StoreSearchResponse> => {
  const { name, state } = where;
  const { limit = 24, offset = 0 } = pagination;

  const stateCodes = inState(state);

  const query = {
    ...(name ? {
      OR: [
        { name: { contains: name, mode: Prisma.QueryMode.insensitive } },
        { organizationName: { contains: name, mode: Prisma.QueryMode.insensitive } },
        { organizationLegalName: { contains: name, mode: Prisma.QueryMode.insensitive } },
        { schoolName: { contains: name, mode: Prisma.QueryMode.insensitive } },
      ]
    } : {}),
    ...(stateCodes ? { state: { in: stateCodes } } : {}),
    AND: [
      { storeUrl: { not: null } },
      { storeUrl: { not: "" } },
    ],
    builtAt: { not: null },
    deactivatedAt: null,
  };

  const rawQuery = [
    "stores.store_url IS NOT NULL",
    "stores.store_url != ''",
    "stores.built_at IS NOT NULL",
    "stores.deactivated_at IS NULL"
  ];

  if (name) {
    const searchBy = ["name", "organization_name", "organization_name", "school_name"].
      map(field => `stores.${field} ILIKE '%${name}%'`).
      join(" OR ");
    rawQuery.push(`(${searchBy})`);
  }

  if (stateCodes) rawQuery.push(`stores.state IN (${stateCodes.map(sc => `'${sc}'`).join(",")})`);

  const counter = await prisma.$queryRaw<[{ total: bigint }]>(Prisma.raw(`SELECT COUNT(DISTINCT stores.store_url) as total FROM stores WHERE (${rawQuery.join(" AND ")})`));
  const total = Number(counter[0]?.total) ?? 0;

  if (total === 0) return {
    limit, offset, stores: [], total
  };

  const stores = await Repo.Store.findMany({
    where: query,
    distinct: ["storeUrl"],
    orderBy: { name: "asc" },
    take: limit,
    skip: offset,
  });

  return {
    limit, offset, total,
    stores: stores.map(store => ({ ...store, state: stateCode(store.state) }))
  };
};

const activateStore = async (_: null, { active, storeUrl, magentoStoreId, magentoStoreCode }: MutationActivateStoreArgs): Promise<StoreResult> => {
  try {
    const searchBy = [
      ["storeUrl", storeUrl],
      ["magentoStoreCode", magentoStoreCode],
      ["magentoStoreId", magentoStoreId]
    ].find(([name, value]) => !!value);

    if (!searchBy) return { status: StoreStatus.Failure, errors: ["Provide at least one argument to search the store"] };

    const deactivatedAt = active ? null : new Date();
    const [name, value] = searchBy;

    await Repo.Store.updateMany({
      data: { deactivatedAt },
      where: { [name]: value }
    });

    return { status: StoreStatus.Success };
  } catch (_e) {
    Sentry.captureException(_e);
    return { status: StoreStatus.Failure, errors: [_e.message] };
  }
};

const storeByCode = async (_: null, { storeCode }: { storeCode: string }) => {
  try {
    return await Repo.Store.findStoreByStoreCode(storeCode);
  } catch (error) {
    console.error("Error finding store by code:", error);
    return null;
  }
};

const resolvers = {
  Mutation: {
    escalateRaiseStoreBuild,
    magentoTransactionWebhook,
    createProduct,
    createStoreOrder,
    netsuiteWebhook,
    uploadLogoPNG,
    marcoWebhook,
    easypostWebhook,
    createStore,
    magentoRefundWebhook,
    resendNetsuiteTracking,
    activateStore,
    restartOrderWorkflow
  },
  Query: {
    storeOrder,
    storeSearch,
    storeByCode
  }
};

export default resolvers;
