import { GraphQLResolveInfo, GraphQLScalarType, GraphQLScalarTypeConfig } from 'graphql';
export type Maybe<T> = T | null;
export type InputMaybe<T> = Maybe<T>;
export type Exact<T extends { [key: string]: unknown }> = { [K in keyof T]: T[K] };
export type MakeOptional<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]?: Maybe<T[SubKey]> };
export type MakeMaybe<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]: Maybe<T[SubKey]> };
export type MakeEmpty<T extends { [key: string]: unknown }, K extends keyof T> = { [_ in K]?: never };
export type Incremental<T> = T | { [P in keyof T]?: P extends ' $fragmentName' | '__typename' ? T[P] : never };
export type RequireFields<T, K extends keyof T> = Omit<T, K> & { [P in K]-?: NonNullable<T[P]> };
/** All built-in and custom scalars, mapped to their actual values */
export type Scalars = {
  ID: { input: string; output: string; }
  String: { input: string; output: string; }
  Boolean: { input: boolean; output: boolean; }
  Int: { input: number; output: number; }
  Float: { input: number; output: number; }
  DateTime: { input: any; output: any; }
  JSON: { input: any; output: any; }
};

export enum Color {
  Black = 'BLACK',
  Blue = 'BLUE',
  Brown = 'BROWN',
  Gray = 'GRAY',
  Green = 'GREEN',
  LightBlue = 'LIGHT_BLUE',
  Orange = 'ORANGE',
  Pink = 'PINK',
  Purple = 'PURPLE',
  Red = 'RED',
  White = 'WHITE',
  Yellow = 'YELLOW'
}

export type EasypostEventInput = {
  status: Scalars['String']['input'];
  status_detail: Scalars['String']['input'];
  tracking_number: Scalars['String']['input'];
  vendor: Vendor;
};

export type EasypostWebhookResponse = {
  __typename?: 'EasypostWebhookResponse';
  error?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type ImageInput = {
  url: Scalars['String']['input'];
};

export enum LogoPosition {
  FrontCenter = 'FRONT_CENTER',
  FrontLeftArm = 'FRONT_LEFT_ARM',
  FrontLeftChest = 'FRONT_LEFT_CHEST',
  FrontLeftLeg = 'FRONT_LEFT_LEG',
  FrontRightArm = 'FRONT_RIGHT_ARM',
  FrontRightChest = 'FRONT_RIGHT_CHEST',
  FrontRightLeg = 'FRONT_RIGHT_LEG'
}

export enum MacroServiceRequestWorkflowState {
  Approved = 'approved',
  Canceled = 'canceled',
  Closed = 'closed',
  Completed = 'completed',
  Open = 'open',
  Rejected = 'rejected'
}

export type MagentoConsumerPointsLiabilityInput = {
  comment?: InputMaybe<Scalars['String']['input']>;
  createdAt?: InputMaybe<Scalars['DateTime']['input']>;
  customerBalanceId?: InputMaybe<Scalars['Int']['input']>;
  customerId: Scalars['Int']['input'];
  customerLiability?: InputMaybe<Scalars['Float']['input']>;
  entityId?: InputMaybe<Scalars['Int']['input']>;
  eventCode?: InputMaybe<Scalars['String']['input']>;
  eventData?: InputMaybe<Scalars['JSON']['input']>;
  expirationDate?: InputMaybe<Scalars['DateTime']['input']>;
  expirationPeriod?: InputMaybe<Scalars['Int']['input']>;
  isExpired?: InputMaybe<Scalars['Boolean']['input']>;
  isNeedSendNotification?: InputMaybe<Scalars['Boolean']['input']>;
  isNotificationSent?: InputMaybe<Scalars['Boolean']['input']>;
  netsuiteExternalId?: InputMaybe<Scalars['String']['input']>;
  netsuiteInternalId?: InputMaybe<Scalars['Int']['input']>;
  orderId?: InputMaybe<Scalars['Int']['input']>;
  organizationId?: InputMaybe<Scalars['Int']['input']>;
  parentNetsuiteExternalId?: InputMaybe<Scalars['String']['input']>;
  parentNetsuiteInternalId?: InputMaybe<Scalars['Int']['input']>;
  parentTransactionId?: InputMaybe<Scalars['Int']['input']>;
  parentTransactionType?: InputMaybe<Scalars['String']['input']>;
  paymentEntityId?: InputMaybe<Scalars['Int']['input']>;
  paymentMethod?: InputMaybe<PaymentMethod>;
  pointsBalance?: InputMaybe<Scalars['Float']['input']>;
  pointsDelta: Scalars['Float']['input'];
  pointsToUse?: InputMaybe<Scalars['Float']['input']>;
  receiverId?: InputMaybe<Scalars['Int']['input']>;
  ruleId?: InputMaybe<Scalars['Int']['input']>;
  senderId?: InputMaybe<Scalars['Int']['input']>;
  storeId?: InputMaybe<Scalars['Int']['input']>;
  transactionId: Scalars['Int']['input'];
  transactionParentGroup?: InputMaybe<Scalars['String']['input']>;
  transactionType: Scalars['String']['input'];
  websiteId?: InputMaybe<Scalars['Int']['input']>;
};

export type MagentoRefundInput = {
  amount: Scalars['Float']['input'];
  netsuiteOrderId: Scalars['Int']['input'];
  orderId: Scalars['Int']['input'];
  status: Scalars['String']['input'];
};

export type MagentoTransactionWebhookResponse = {
  __typename?: 'MagentoTransactionWebhookResponse';
  error?: Maybe<Scalars['String']['output']>;
  success?: Maybe<Scalars['Boolean']['output']>;
};

export type MarcoEventInput = {
  address?: InputMaybe<MarcoShippingAddress>;
  created_at?: InputMaybe<Scalars['String']['input']>;
  id: Scalars['Int']['input'];
  purchase_order: Scalars['String']['input'];
  received_at?: InputMaybe<Scalars['String']['input']>;
  shipments?: InputMaybe<Array<InputMaybe<MarcoShipment>>>;
  shipped_at?: InputMaybe<Scalars['String']['input']>;
  status?: InputMaybe<MarcoOrderStatus>;
};

export enum MarcoItemWorkflowState {
  Canceled = 'canceled',
  Completed = 'completed',
  IntakeHold = 'intake_hold',
  Open = 'open',
  ProductionHold = 'production_hold',
  Shipped = 'shipped'
}

export type MarcoOrderItem = {
  client_item_num?: InputMaybe<Scalars['String']['input']>;
  customer_sku?: InputMaybe<Scalars['String']['input']>;
  description?: InputMaybe<Scalars['String']['input']>;
  id: Scalars['Int']['input'];
  quantity: Scalars['Int']['input'];
  sku_id?: InputMaybe<Scalars['Int']['input']>;
  workflow_state?: InputMaybe<MarcoItemWorkflowState>;
};

export enum MarcoOrderStatus {
  Canceled = 'canceled',
  Completed = 'completed',
  IntakeHold = 'intake_hold',
  Open = 'open',
  ProductionHold = 'production_hold',
  Received = 'received',
  Shipped = 'shipped'
}

export type MarcoOrderStatusNote = {
  created_at: Scalars['String']['input'];
  note: Scalars['String']['input'];
};

export type MarcoServiceRequest = {
  created_at?: InputMaybe<Scalars['String']['input']>;
  items_count?: InputMaybe<Scalars['Int']['input']>;
  reason?: InputMaybe<Scalars['String']['input']>;
  workflow_state?: InputMaybe<MacroServiceRequestWorkflowState>;
};

export type MarcoShipment = {
  delivered?: InputMaybe<Scalars['Boolean']['input']>;
  search_link?: InputMaybe<Scalars['String']['input']>;
  shipping_carrier?: InputMaybe<ShippingProvider>;
  shipping_method?: InputMaybe<Scalars['String']['input']>;
  tracking_number?: InputMaybe<Scalars['String']['input']>;
};

export type MarcoShippingAddress = {
  ship_to_address?: InputMaybe<Scalars['String']['input']>;
  ship_to_address_2?: InputMaybe<Scalars['String']['input']>;
  ship_to_city?: InputMaybe<Scalars['String']['input']>;
  ship_to_company_name?: InputMaybe<Scalars['String']['input']>;
  ship_to_country?: InputMaybe<Scalars['String']['input']>;
  ship_to_email?: InputMaybe<Scalars['String']['input']>;
  ship_to_first_name?: InputMaybe<Scalars['String']['input']>;
  ship_to_last_name?: InputMaybe<Scalars['String']['input']>;
  ship_to_state?: InputMaybe<Scalars['String']['input']>;
  ship_to_telephone?: InputMaybe<Scalars['String']['input']>;
  ship_to_zip_code?: InputMaybe<Scalars['String']['input']>;
};

export type MarcoWebhookResponse = {
  __typename?: 'MarcoWebhookResponse';
  error?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type Mutation = {
  __typename?: 'Mutation';
  activateStore: StoreResult;
  createProduct?: Maybe<ProductResult>;
  createStore: StoreResult;
  createStoreOrder?: Maybe<OrderResult>;
  easypostWebhook: EasypostWebhookResponse;
  escalateRaiseStoreBuild: StoreResult;
  magentoRefundWebhook?: Maybe<MagentoTransactionWebhookResponse>;
  magentoTransactionWebhook?: Maybe<MagentoTransactionWebhookResponse>;
  marcoWebhook: MarcoWebhookResponse;
  netsuiteWebhook?: Maybe<NetsuiteWebhookResponse>;
  resendNetsuiteTracking: TrackingResult;
  restartOrderWorkflow: OrderResult;
  uploadLogoPNG?: Maybe<OrderResult>;
};


export type MutationActivateStoreArgs = {
  active: Scalars['Boolean']['input'];
  magentoStoreCode?: InputMaybe<Scalars['String']['input']>;
  magentoStoreId?: InputMaybe<Scalars['Int']['input']>;
  storeUrl?: InputMaybe<Scalars['String']['input']>;
};


export type MutationCreateProductArgs = {
  product?: InputMaybe<ProductInput>;
};


export type MutationCreateStoreArgs = {
  overrideSkipLogic?: InputMaybe<Scalars['Boolean']['input']>;
  store: StoreInput;
};


export type MutationCreateStoreOrderArgs = {
  order?: InputMaybe<OrderInput>;
  restartWorkflow?: InputMaybe<Scalars['Boolean']['input']>;
};


export type MutationEasypostWebhookArgs = {
  status: EasypostEventInput;
};


export type MutationEscalateRaiseStoreBuildArgs = {
  fundraiserId: Scalars['Int']['input'];
};


export type MutationMagentoRefundWebhookArgs = {
  magentoRefund?: InputMaybe<MagentoRefundInput>;
};


export type MutationMagentoTransactionWebhookArgs = {
  magentoEvent?: InputMaybe<MagentoConsumerPointsLiabilityInput>;
};


export type MutationMarcoWebhookArgs = {
  status: MarcoEventInput;
};


export type MutationNetsuiteWebhookArgs = {
  netsuiteEvent?: InputMaybe<NetsuiteEventInput>;
};


export type MutationResendNetsuiteTrackingArgs = {
  netsuiteId: Scalars['String']['input'];
};


export type MutationRestartOrderWorkflowArgs = {
  netsuiteId: Scalars['String']['input'];
};


export type MutationUploadLogoPngArgs = {
  image?: InputMaybe<ImageInput>;
};

export type NetsuiteEventInput = {
  eventType?: InputMaybe<Scalars['String']['input']>;
  record?: InputMaybe<Scalars['JSON']['input']>;
};

export type NetsuiteWebhookResponse = {
  __typename?: 'NetsuiteWebhookResponse';
  error?: Maybe<Scalars['String']['output']>;
  success?: Maybe<Scalars['Boolean']['output']>;
};

export type OrderInput = {
  baseSubtotal?: InputMaybe<Scalars['Float']['input']>;
  billingCity?: InputMaybe<Scalars['String']['input']>;
  billingState?: InputMaybe<Scalars['String']['input']>;
  billingStreet?: InputMaybe<Scalars['String']['input']>;
  billingStreet2?: InputMaybe<Scalars['String']['input']>;
  billingZipCode?: InputMaybe<Scalars['String']['input']>;
  carrier: ShippingProvider;
  city: Scalars['String']['input'];
  confirmationId?: InputMaybe<Scalars['String']['input']>;
  designEps?: InputMaybe<Scalars['String']['input']>;
  discountAmount?: InputMaybe<Scalars['Float']['input']>;
  fundraiserId?: InputMaybe<Scalars['String']['input']>;
  giftCardAmount?: InputMaybe<Scalars['Float']['input']>;
  line2?: InputMaybe<Scalars['String']['input']>;
  netsuiteId?: InputMaybe<Scalars['String']['input']>;
  orderId?: InputMaybe<Scalars['String']['input']>;
  packingSlipId: Scalars['String']['input'];
  packingSlipTitle: Scalars['String']['input'];
  paymentMethod?: InputMaybe<Scalars['String']['input']>;
  pointAmount?: InputMaybe<Scalars['Float']['input']>;
  priority?: InputMaybe<Priority>;
  products: Array<OrderProductInput>;
  scheduleAt?: InputMaybe<Scalars['String']['input']>;
  shipTo: Scalars['String']['input'];
  shipToEmail?: InputMaybe<Scalars['String']['input']>;
  shipToPhone?: InputMaybe<Scalars['String']['input']>;
  shippingCost?: InputMaybe<Scalars['Float']['input']>;
  source?: InputMaybe<Scalars['String']['input']>;
  state: Scalars['String']['input'];
  street: Scalars['String']['input'];
  street2?: InputMaybe<Scalars['String']['input']>;
  taxAmount?: InputMaybe<Scalars['Float']['input']>;
  transactionId?: InputMaybe<Scalars['String']['input']>;
  update?: InputMaybe<Scalars['Boolean']['input']>;
  vendor: Vendor;
  zipCode: Scalars['String']['input'];
};

export type OrderProductInput = {
  amount?: InputMaybe<Scalars['Float']['input']>;
  color?: InputMaybe<Scalars['String']['input']>;
  discountAmount?: InputMaybe<Scalars['Float']['input']>;
  logo?: InputMaybe<Scalars['String']['input']>;
  magentoItemId?: InputMaybe<Scalars['String']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
  netsuiteId: Scalars['String']['input'];
  printAttributes?: InputMaybe<PrintAttributesInput>;
  quantity?: InputMaybe<Scalars['Int']['input']>;
  receiverName: Scalars['String']['input'];
  sku?: InputMaybe<Scalars['String']['input']>;
  taxAmount?: InputMaybe<Scalars['Float']['input']>;
  thumbnailUrl?: InputMaybe<Scalars['String']['input']>;
};

export type OrderResult = {
  __typename?: 'OrderResult';
  errors?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
  netsuiteId?: Maybe<Scalars['String']['output']>;
  status?: Maybe<OrderStatus>;
};

export enum OrderStatus {
  Failure = 'FAILURE',
  Success = 'SUCCESS'
}

export enum PaymentMethod {
  Paypal = 'PAYPAL',
  Stripe = 'STRIPE'
}

export type PrintAttributesInput = {
  name?: InputMaybe<PrintValueInput>;
  number?: InputMaybe<PrintValueInput>;
};

export type PrintValueInput = {
  color?: InputMaybe<Color>;
  font?: InputMaybe<Scalars['String']['input']>;
  value?: InputMaybe<Scalars['String']['input']>;
};

export enum Priority {
  Normal = 'NORMAL',
  Rush = 'RUSH'
}

export type ProductInput = {
  color?: InputMaybe<Scalars['String']['input']>;
  logoPosition: LogoPosition;
  magentoSku?: InputMaybe<Scalars['String']['input']>;
  name: Scalars['String']['input'];
  netsuiteId?: InputMaybe<Scalars['String']['input']>;
  size: ProductSize;
  sku: Scalars['String']['input'];
};

export type ProductResult = {
  __typename?: 'ProductResult';
  errors?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
  id?: Maybe<Scalars['String']['output']>;
};

export enum ProductSize {
  L = 'L',
  M = 'M',
  NoSize = 'NO_SIZE',
  Osfa = 'OSFA',
  S = 'S',
  Xl = 'XL',
  Xs = 'XS',
  Xxl = 'XXL',
  Xxs = 'XXS',
  Xxxl = 'XXXL',
  Xxxxl = 'XXXXL',
  Yl = 'YL',
  Ym = 'YM',
  Ys = 'YS',
  Yxl = 'YXL',
  Yxs = 'YXS',
  Yxxl = 'YXXL',
  Yxxxl = 'YXXXL'
}

export enum ProductStatus {
  Failure = 'FAILURE',
  Success = 'SUCCESS'
}

export type Query = {
  __typename?: 'Query';
  storeOrder?: Maybe<StoreOrderResult>;
  storeSearch: StoreSearchResponse;
};


export type QueryStoreOrderArgs = {
  netsuiteId?: InputMaybe<Scalars['String']['input']>;
};


export type QueryStoreSearchArgs = {
  pagination?: InputMaybe<StoreSearchPaginationInput>;
  where?: InputMaybe<StoreSearchWhereInput>;
};

export enum ShippingProvider {
  Dhl = 'DHL',
  FedEx = 'FedEx',
  Osm = 'OSM',
  Ups = 'UPS',
  Usps = 'USPS'
}

export type StoreInput = {
  accountManager?: InputMaybe<Scalars['String']['input']>;
  accountManagerEmail?: InputMaybe<Scalars['String']['input']>;
  accountManagerUDID?: InputMaybe<Scalars['String']['input']>;
  activityType: Scalars['String']['input'];
  city: Scalars['String']['input'];
  fundraiserId?: InputMaybe<Scalars['Int']['input']>;
  groupLeader?: InputMaybe<Scalars['String']['input']>;
  groupLeaderEmail?: InputMaybe<Scalars['String']['input']>;
  groupLeaderUDID?: InputMaybe<Scalars['String']['input']>;
  groupName: Scalars['String']['input'];
  logoDigitalUrl: Scalars['String']['input'];
  logoEmbroideryUrl: Scalars['String']['input'];
  logoHatUrl: Scalars['String']['input'];
  /**  Pair of primary colors in format  #RRGGBB|#RRGGBB  */
  logoPrimaryColor: Scalars['String']['input'];
  logoWebHeaderUrl: Scalars['String']['input'];
  name: Scalars['String']['input'];
  organizationId?: InputMaybe<Scalars['Int']['input']>;
  organizationLegalName: Scalars['String']['input'];
  partnerId?: InputMaybe<Scalars['Int']['input']>;
  pointsPercentage?: Scalars['Int']['input'];
  salesRep?: InputMaybe<Scalars['String']['input']>;
  salesRepEmail?: InputMaybe<Scalars['String']['input']>;
  salesRepUDID?: InputMaybe<Scalars['String']['input']>;
  schoolId?: InputMaybe<Scalars['String']['input']>;
  schoolName?: InputMaybe<Scalars['String']['input']>;
  state: Scalars['String']['input'];
  storeCode?: InputMaybe<Scalars['String']['input']>;
  storeUrl: Scalars['String']['input'];
  zip: Scalars['String']['input'];
};

export type StoreOrderResult = {
  __typename?: 'StoreOrderResult';
  createdAt: Scalars['DateTime']['output'];
  id: Scalars['String']['output'];
  netsuiteId?: Maybe<Scalars['String']['output']>;
  shippingReceivedAt?: Maybe<Scalars['DateTime']['output']>;
  status?: Maybe<StoreOrderStatus>;
  trackingNumber?: Maybe<Scalars['String']['output']>;
  trackingUrl?: Maybe<Scalars['String']['output']>;
  updatedAt: Scalars['DateTime']['output'];
};

export enum StoreOrderStatus {
  Closed = 'CLOSED',
  Created = 'CREATED',
  Delivered = 'DELIVERED',
  Design = 'DESIGN',
  Hold = 'HOLD',
  InTransit = 'IN_TRANSIT',
  MarcoRejected = 'MARCO_REJECTED',
  Rejected = 'REJECTED',
  Shipped = 'SHIPPED'
}

export type StoreResult = {
  __typename?: 'StoreResult';
  errors?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
  message?: Maybe<Scalars['String']['output']>;
  status: StoreStatus;
  storeUrl?: Maybe<Scalars['String']['output']>;
};

export type StoreSearchPaginationInput = {
  limit?: InputMaybe<Scalars['Int']['input']>;
  offset?: InputMaybe<Scalars['Int']['input']>;
};

export type StoreSearchResponse = {
  __typename?: 'StoreSearchResponse';
  limit: Scalars['Int']['output'];
  offset: Scalars['Int']['output'];
  stores: Array<StoreType>;
  total: Scalars['Int']['output'];
};

export type StoreSearchWhereInput = {
  name?: InputMaybe<Scalars['String']['input']>;
  state?: InputMaybe<Scalars['String']['input']>;
};

export enum StoreStatus {
  Failure = 'FAILURE',
  Success = 'SUCCESS'
}

export type StoreType = {
  __typename?: 'StoreType';
  city?: Maybe<Scalars['String']['output']>;
  fundraiserId?: Maybe<Scalars['Int']['output']>;
  id: Scalars['String']['output'];
  name: Scalars['String']['output'];
  organizationId?: Maybe<Scalars['Int']['output']>;
  organizationLegalName?: Maybe<Scalars['String']['output']>;
  organizationName?: Maybe<Scalars['String']['output']>;
  schoolId?: Maybe<Scalars['String']['output']>;
  schoolName?: Maybe<Scalars['String']['output']>;
  state?: Maybe<Scalars['String']['output']>;
  storeCode?: Maybe<Scalars['String']['output']>;
  storeUrl: Scalars['String']['output'];
  teamId?: Maybe<Scalars['String']['output']>;
  zip?: Maybe<Scalars['String']['output']>;
};

export type TrackingResult = {
  __typename?: 'TrackingResult';
  status?: Maybe<OrderStatus>;
};

export enum Vendor {
  Marco = 'MARCO',
  Raise = 'RAISE',
  RaiseMarco = 'RAISE_MARCO',
  Store = 'STORE'
}



export type ResolverTypeWrapper<T> = Promise<T> | T;


export type ResolverWithResolve<TResult, TParent, TContext, TArgs> = {
  resolve: ResolverFn<TResult, TParent, TContext, TArgs>;
};
export type Resolver<TResult, TParent = {}, TContext = {}, TArgs = {}> = ResolverFn<TResult, TParent, TContext, TArgs> | ResolverWithResolve<TResult, TParent, TContext, TArgs>;

export type ResolverFn<TResult, TParent, TContext, TArgs> = (
  parent: TParent,
  args: TArgs,
  context: TContext,
  info: GraphQLResolveInfo
) => Promise<TResult> | TResult;

export type SubscriptionSubscribeFn<TResult, TParent, TContext, TArgs> = (
  parent: TParent,
  args: TArgs,
  context: TContext,
  info: GraphQLResolveInfo
) => AsyncIterable<TResult> | Promise<AsyncIterable<TResult>>;

export type SubscriptionResolveFn<TResult, TParent, TContext, TArgs> = (
  parent: TParent,
  args: TArgs,
  context: TContext,
  info: GraphQLResolveInfo
) => TResult | Promise<TResult>;

export interface SubscriptionSubscriberObject<TResult, TKey extends string, TParent, TContext, TArgs> {
  subscribe: SubscriptionSubscribeFn<{ [key in TKey]: TResult }, TParent, TContext, TArgs>;
  resolve?: SubscriptionResolveFn<TResult, { [key in TKey]: TResult }, TContext, TArgs>;
}

export interface SubscriptionResolverObject<TResult, TParent, TContext, TArgs> {
  subscribe: SubscriptionSubscribeFn<any, TParent, TContext, TArgs>;
  resolve: SubscriptionResolveFn<TResult, any, TContext, TArgs>;
}

export type SubscriptionObject<TResult, TKey extends string, TParent, TContext, TArgs> =
  | SubscriptionSubscriberObject<TResult, TKey, TParent, TContext, TArgs>
  | SubscriptionResolverObject<TResult, TParent, TContext, TArgs>;

export type SubscriptionResolver<TResult, TKey extends string, TParent = {}, TContext = {}, TArgs = {}> =
  | ((...args: any[]) => SubscriptionObject<TResult, TKey, TParent, TContext, TArgs>)
  | SubscriptionObject<TResult, TKey, TParent, TContext, TArgs>;

export type TypeResolveFn<TTypes, TParent = {}, TContext = {}> = (
  parent: TParent,
  context: TContext,
  info: GraphQLResolveInfo
) => Maybe<TTypes> | Promise<Maybe<TTypes>>;

export type IsTypeOfResolverFn<T = {}, TContext = {}> = (obj: T, context: TContext, info: GraphQLResolveInfo) => boolean | Promise<boolean>;

export type NextResolverFn<T> = () => Promise<T>;

export type DirectiveResolverFn<TResult = {}, TParent = {}, TContext = {}, TArgs = {}> = (
  next: NextResolverFn<TResult>,
  parent: TParent,
  args: TArgs,
  context: TContext,
  info: GraphQLResolveInfo
) => TResult | Promise<TResult>;



/** Mapping between all available schema types and the resolvers types */
export type ResolversTypes = {
  Boolean: ResolverTypeWrapper<Scalars['Boolean']['output']>;
  Color: Color;
  DateTime: ResolverTypeWrapper<Scalars['DateTime']['output']>;
  EasypostEventInput: EasypostEventInput;
  EasypostWebhookResponse: ResolverTypeWrapper<EasypostWebhookResponse>;
  Float: ResolverTypeWrapper<Scalars['Float']['output']>;
  ImageInput: ImageInput;
  Int: ResolverTypeWrapper<Scalars['Int']['output']>;
  JSON: ResolverTypeWrapper<Scalars['JSON']['output']>;
  LogoPosition: LogoPosition;
  MacroServiceRequestWorkflowState: MacroServiceRequestWorkflowState;
  MagentoConsumerPointsLiabilityInput: MagentoConsumerPointsLiabilityInput;
  MagentoRefundInput: MagentoRefundInput;
  MagentoTransactionWebhookResponse: ResolverTypeWrapper<MagentoTransactionWebhookResponse>;
  MarcoEventInput: MarcoEventInput;
  MarcoItemWorkflowState: MarcoItemWorkflowState;
  MarcoOrderItem: MarcoOrderItem;
  MarcoOrderStatus: MarcoOrderStatus;
  MarcoOrderStatusNote: MarcoOrderStatusNote;
  MarcoServiceRequest: MarcoServiceRequest;
  MarcoShipment: MarcoShipment;
  MarcoShippingAddress: MarcoShippingAddress;
  MarcoWebhookResponse: ResolverTypeWrapper<MarcoWebhookResponse>;
  Mutation: ResolverTypeWrapper<{}>;
  NetsuiteEventInput: NetsuiteEventInput;
  NetsuiteWebhookResponse: ResolverTypeWrapper<NetsuiteWebhookResponse>;
  OrderInput: OrderInput;
  OrderProductInput: OrderProductInput;
  OrderResult: ResolverTypeWrapper<OrderResult>;
  OrderStatus: OrderStatus;
  PaymentMethod: PaymentMethod;
  PrintAttributesInput: PrintAttributesInput;
  PrintValueInput: PrintValueInput;
  Priority: Priority;
  ProductInput: ProductInput;
  ProductResult: ResolverTypeWrapper<ProductResult>;
  ProductSize: ProductSize;
  ProductStatus: ProductStatus;
  Query: ResolverTypeWrapper<{}>;
  ShippingProvider: ShippingProvider;
  StoreInput: StoreInput;
  StoreOrderResult: ResolverTypeWrapper<StoreOrderResult>;
  StoreOrderStatus: StoreOrderStatus;
  StoreResult: ResolverTypeWrapper<StoreResult>;
  StoreSearchPaginationInput: StoreSearchPaginationInput;
  StoreSearchResponse: ResolverTypeWrapper<StoreSearchResponse>;
  StoreSearchWhereInput: StoreSearchWhereInput;
  StoreStatus: StoreStatus;
  StoreType: ResolverTypeWrapper<StoreType>;
  String: ResolverTypeWrapper<Scalars['String']['output']>;
  TrackingResult: ResolverTypeWrapper<TrackingResult>;
  Vendor: Vendor;
};

/** Mapping between all available schema types and the resolvers parents */
export type ResolversParentTypes = {
  Boolean: Scalars['Boolean']['output'];
  DateTime: Scalars['DateTime']['output'];
  EasypostEventInput: EasypostEventInput;
  EasypostWebhookResponse: EasypostWebhookResponse;
  Float: Scalars['Float']['output'];
  ImageInput: ImageInput;
  Int: Scalars['Int']['output'];
  JSON: Scalars['JSON']['output'];
  MagentoConsumerPointsLiabilityInput: MagentoConsumerPointsLiabilityInput;
  MagentoRefundInput: MagentoRefundInput;
  MagentoTransactionWebhookResponse: MagentoTransactionWebhookResponse;
  MarcoEventInput: MarcoEventInput;
  MarcoOrderItem: MarcoOrderItem;
  MarcoOrderStatusNote: MarcoOrderStatusNote;
  MarcoServiceRequest: MarcoServiceRequest;
  MarcoShipment: MarcoShipment;
  MarcoShippingAddress: MarcoShippingAddress;
  MarcoWebhookResponse: MarcoWebhookResponse;
  Mutation: {};
  NetsuiteEventInput: NetsuiteEventInput;
  NetsuiteWebhookResponse: NetsuiteWebhookResponse;
  OrderInput: OrderInput;
  OrderProductInput: OrderProductInput;
  OrderResult: OrderResult;
  PrintAttributesInput: PrintAttributesInput;
  PrintValueInput: PrintValueInput;
  ProductInput: ProductInput;
  ProductResult: ProductResult;
  Query: {};
  StoreInput: StoreInput;
  StoreOrderResult: StoreOrderResult;
  StoreResult: StoreResult;
  StoreSearchPaginationInput: StoreSearchPaginationInput;
  StoreSearchResponse: StoreSearchResponse;
  StoreSearchWhereInput: StoreSearchWhereInput;
  StoreType: StoreType;
  String: Scalars['String']['output'];
  TrackingResult: TrackingResult;
};

export interface DateTimeScalarConfig extends GraphQLScalarTypeConfig<ResolversTypes['DateTime'], any> {
  name: 'DateTime';
}

export type EasypostWebhookResponseResolvers<ContextType = any, ParentType extends ResolversParentTypes['EasypostWebhookResponse'] = ResolversParentTypes['EasypostWebhookResponse']> = {
  error?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  success?: Resolver<ResolversTypes['Boolean'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export interface JsonScalarConfig extends GraphQLScalarTypeConfig<ResolversTypes['JSON'], any> {
  name: 'JSON';
}

export type MagentoTransactionWebhookResponseResolvers<ContextType = any, ParentType extends ResolversParentTypes['MagentoTransactionWebhookResponse'] = ResolversParentTypes['MagentoTransactionWebhookResponse']> = {
  error?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  success?: Resolver<Maybe<ResolversTypes['Boolean']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type MarcoWebhookResponseResolvers<ContextType = any, ParentType extends ResolversParentTypes['MarcoWebhookResponse'] = ResolversParentTypes['MarcoWebhookResponse']> = {
  error?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  success?: Resolver<ResolversTypes['Boolean'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type MutationResolvers<ContextType = any, ParentType extends ResolversParentTypes['Mutation'] = ResolversParentTypes['Mutation']> = {
  activateStore?: Resolver<ResolversTypes['StoreResult'], ParentType, ContextType, RequireFields<MutationActivateStoreArgs, 'active'>>;
  createProduct?: Resolver<Maybe<ResolversTypes['ProductResult']>, ParentType, ContextType, Partial<MutationCreateProductArgs>>;
  createStore?: Resolver<ResolversTypes['StoreResult'], ParentType, ContextType, RequireFields<MutationCreateStoreArgs, 'overrideSkipLogic' | 'store'>>;
  createStoreOrder?: Resolver<Maybe<ResolversTypes['OrderResult']>, ParentType, ContextType, RequireFields<MutationCreateStoreOrderArgs, 'restartWorkflow'>>;
  easypostWebhook?: Resolver<ResolversTypes['EasypostWebhookResponse'], ParentType, ContextType, RequireFields<MutationEasypostWebhookArgs, 'status'>>;
  escalateRaiseStoreBuild?: Resolver<ResolversTypes['StoreResult'], ParentType, ContextType, RequireFields<MutationEscalateRaiseStoreBuildArgs, 'fundraiserId'>>;
  magentoRefundWebhook?: Resolver<Maybe<ResolversTypes['MagentoTransactionWebhookResponse']>, ParentType, ContextType, Partial<MutationMagentoRefundWebhookArgs>>;
  magentoTransactionWebhook?: Resolver<Maybe<ResolversTypes['MagentoTransactionWebhookResponse']>, ParentType, ContextType, Partial<MutationMagentoTransactionWebhookArgs>>;
  marcoWebhook?: Resolver<ResolversTypes['MarcoWebhookResponse'], ParentType, ContextType, RequireFields<MutationMarcoWebhookArgs, 'status'>>;
  netsuiteWebhook?: Resolver<Maybe<ResolversTypes['NetsuiteWebhookResponse']>, ParentType, ContextType, Partial<MutationNetsuiteWebhookArgs>>;
  resendNetsuiteTracking?: Resolver<ResolversTypes['TrackingResult'], ParentType, ContextType, RequireFields<MutationResendNetsuiteTrackingArgs, 'netsuiteId'>>;
  restartOrderWorkflow?: Resolver<ResolversTypes['OrderResult'], ParentType, ContextType, RequireFields<MutationRestartOrderWorkflowArgs, 'netsuiteId'>>;
  uploadLogoPNG?: Resolver<Maybe<ResolversTypes['OrderResult']>, ParentType, ContextType, Partial<MutationUploadLogoPngArgs>>;
};

export type NetsuiteWebhookResponseResolvers<ContextType = any, ParentType extends ResolversParentTypes['NetsuiteWebhookResponse'] = ResolversParentTypes['NetsuiteWebhookResponse']> = {
  error?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  success?: Resolver<Maybe<ResolversTypes['Boolean']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type OrderResultResolvers<ContextType = any, ParentType extends ResolversParentTypes['OrderResult'] = ResolversParentTypes['OrderResult']> = {
  errors?: Resolver<Maybe<Array<Maybe<ResolversTypes['String']>>>, ParentType, ContextType>;
  netsuiteId?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  status?: Resolver<Maybe<ResolversTypes['OrderStatus']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type ProductResultResolvers<ContextType = any, ParentType extends ResolversParentTypes['ProductResult'] = ResolversParentTypes['ProductResult']> = {
  errors?: Resolver<Maybe<Array<Maybe<ResolversTypes['String']>>>, ParentType, ContextType>;
  id?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type QueryResolvers<ContextType = any, ParentType extends ResolversParentTypes['Query'] = ResolversParentTypes['Query']> = {
  storeOrder?: Resolver<Maybe<ResolversTypes['StoreOrderResult']>, ParentType, ContextType, Partial<QueryStoreOrderArgs>>;
  storeSearch?: Resolver<ResolversTypes['StoreSearchResponse'], ParentType, ContextType, Partial<QueryStoreSearchArgs>>;
};

export type StoreOrderResultResolvers<ContextType = any, ParentType extends ResolversParentTypes['StoreOrderResult'] = ResolversParentTypes['StoreOrderResult']> = {
  createdAt?: Resolver<ResolversTypes['DateTime'], ParentType, ContextType>;
  id?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  netsuiteId?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  shippingReceivedAt?: Resolver<Maybe<ResolversTypes['DateTime']>, ParentType, ContextType>;
  status?: Resolver<Maybe<ResolversTypes['StoreOrderStatus']>, ParentType, ContextType>;
  trackingNumber?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  trackingUrl?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  updatedAt?: Resolver<ResolversTypes['DateTime'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type StoreResultResolvers<ContextType = any, ParentType extends ResolversParentTypes['StoreResult'] = ResolversParentTypes['StoreResult']> = {
  errors?: Resolver<Maybe<Array<Maybe<ResolversTypes['String']>>>, ParentType, ContextType>;
  message?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  status?: Resolver<ResolversTypes['StoreStatus'], ParentType, ContextType>;
  storeUrl?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type StoreSearchResponseResolvers<ContextType = any, ParentType extends ResolversParentTypes['StoreSearchResponse'] = ResolversParentTypes['StoreSearchResponse']> = {
  limit?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  offset?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  stores?: Resolver<Array<ResolversTypes['StoreType']>, ParentType, ContextType>;
  total?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type StoreTypeResolvers<ContextType = any, ParentType extends ResolversParentTypes['StoreType'] = ResolversParentTypes['StoreType']> = {
  city?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  fundraiserId?: Resolver<Maybe<ResolversTypes['Int']>, ParentType, ContextType>;
  id?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  name?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  organizationId?: Resolver<Maybe<ResolversTypes['Int']>, ParentType, ContextType>;
  organizationLegalName?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  organizationName?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  schoolId?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  schoolName?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  state?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  storeCode?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  storeUrl?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  teamId?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  zip?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type TrackingResultResolvers<ContextType = any, ParentType extends ResolversParentTypes['TrackingResult'] = ResolversParentTypes['TrackingResult']> = {
  status?: Resolver<Maybe<ResolversTypes['OrderStatus']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type Resolvers<ContextType = any> = {
  DateTime?: GraphQLScalarType;
  EasypostWebhookResponse?: EasypostWebhookResponseResolvers<ContextType>;
  JSON?: GraphQLScalarType;
  MagentoTransactionWebhookResponse?: MagentoTransactionWebhookResponseResolvers<ContextType>;
  MarcoWebhookResponse?: MarcoWebhookResponseResolvers<ContextType>;
  Mutation?: MutationResolvers<ContextType>;
  NetsuiteWebhookResponse?: NetsuiteWebhookResponseResolvers<ContextType>;
  OrderResult?: OrderResultResolvers<ContextType>;
  ProductResult?: ProductResultResolvers<ContextType>;
  Query?: QueryResolvers<ContextType>;
  StoreOrderResult?: StoreOrderResultResolvers<ContextType>;
  StoreResult?: StoreResultResolvers<ContextType>;
  StoreSearchResponse?: StoreSearchResponseResolvers<ContextType>;
  StoreType?: StoreTypeResolvers<ContextType>;
  TrackingResult?: TrackingResultResolvers<ContextType>;
};

