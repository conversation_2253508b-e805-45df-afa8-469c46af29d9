# schema.graphql
scalar JSON
scalar DateTime

type Query {
  storeOrder(netsuiteId: String): StoreOrderResult
  storeSearch(pagination: StoreSearchPaginationInput, where: StoreSearchWhereInput): StoreSearchResponse!
  storeByCode(storeCode: String!): StoreType
}

type Mutation {
  activateStore(active: Boolean!, storeUrl: String, magentoStoreId: Int, magentoStoreCode: String): StoreResult!
  magentoRefundWebhook(magentoRefund: MagentoRefundInput):  MagentoTransactionWebhookResponse
  magentoTransactionWebhook(magentoEvent: MagentoConsumerPointsLiabilityInput):  MagentoTransactionWebhookResponse
  createProduct(product: ProductInput): ProductResult
  createStoreOrder(order: OrderInput, restartWorkflow: Boolean = false): OrderResult
  netsuiteWebhook(netsuiteEvent: NetsuiteEventInput): NetsuiteWebhookResponse
  uploadLogoPNG(image: ImageInput): OrderResult
  marcoWebhook(status: MarcoEventInput!): MarcoWebhookResponse!
  createStore(store: StoreInput!, overrideSkipLogic: Boolean = false): StoreResult!
  escalateRaiseStoreBuild(fundraiserId: Int!): StoreResult!
  resendNetsuiteTracking(netsuiteId: String! ): TrackingResult!
  restartOrderWorkflow(netsuiteId: String! ): OrderResult!
  easypostWebhook(status: EasypostEventInput!): EasypostWebhookResponse!
}

type TrackingResult {
  status: OrderStatus
}

enum Vendor {
  MARCO
  STORE
  RAISE_MARCO
  RAISE
}

enum ShippingProvider {
  DHL
  UPS
  FedEx
  USPS
  OSM
}

input MagentoRefundInput {
  status: String!
  amount: Float!
  netsuiteOrderId: Int!
  orderId: Int!
}

input OrderInput {
  paymentMethod: String
  designEps: String
  transactionId: String
  orderId: String
  scheduleAt: String
  update: Boolean
  vendor: Vendor!
  carrier: ShippingProvider!
  shipTo: String!
  shipToEmail: String
  shipToPhone: String
  confirmationId: String
  line2: String
  street: String!
  street2: String
  city: String!
  state: String!
  zipCode: String!
  billingStreet: String
  billingStreet2: String
  billingCity: String
  billingState: String
  billingZipCode: String
  baseSubtotal: Float,
  packingSlipId: String!
  packingSlipTitle: String!
  netsuiteId: String
  products: [OrderProductInput!]!
  fundraiserId: String
  shippingCost: Float
  taxAmount: Float
  discountAmount: Float
  pointAmount: Float
  giftCardAmount: Float
  priority: Priority
  source: String
}

input OrderProductInput {
  receiverName: String!
  netsuiteId: String!
  name: String
  sku: String
  logo: String
  color: String
  thumbnailUrl: String
  magentoItemId: String
  printAttributes: PrintAttributesInput
  amount: Float
  quantity: Int
  discountAmount: Float
  taxAmount: Float
}

input ProductInput {
  name: String!
  size: ProductSize!
  sku: String!
  netsuiteId: String
  logoPosition: LogoPosition!
  magentoSku: String,
  color: String
}

enum OrderStatus {
  SUCCESS
  FAILURE
}

enum ProductStatus {
  SUCCESS
  FAILURE
}

enum StoreStatus {
  SUCCESS
  FAILURE
}

type OrderResult {
  status: OrderStatus
  netsuiteId: String
  errors: [String]
}

type ProductResult {
  id: String
  errors: [String]
}

type StoreResult {
  status: StoreStatus!
  errors: [String]
  storeUrl: String
  message: String
}

input PrintAttributesInput {
  name: PrintValueInput
  number: PrintValueInput
}

input PrintValueInput {
  value: String
  font: String
  color: Color
}

input NetsuiteEventInput {
  eventType: String
  record: JSON
}

input MagentoConsumerPointsLiabilityInput {
  transactionId: Int!
  customerId: Int!
  pointsDelta: Float!
  customerLiability: Float
  netsuiteInternalId: Int
  netsuiteExternalId: String
  transactionType: String!
  senderId: Int
  receiverId: Int
  transactionParentGroup: String
  isExpired: Boolean
  pointsToUse: Float
  parentTransactionId: Int
  expirationDate: DateTime
  orderId: Int
  customerBalanceId: Int
  websiteId: Int
  storeId: Int
  pointsBalance: Float
  eventCode: String
  eventData: JSON
  ruleId: Int
  entityId: Int
  createdAt: DateTime
  isNotificationSent: Boolean
  expirationPeriod: Int
  comment: String
  isNeedSendNotification: Boolean
  organizationId: Int
  paymentMethod: PaymentMethod
  paymentEntityId: Int
  parentNetsuiteInternalId: Int
  parentNetsuiteExternalId: String
  parentTransactionType: String
}

enum PaymentMethod {
  STRIPE
  PAYPAL
}

enum Priority {
  NORMAL
  RUSH
}

type NetsuiteWebhookResponse {
  success: Boolean
  error: String
}

type MagentoTransactionWebhookResponse {
  success: Boolean
  error: String
}

input ImageInput {
  url: String!
}

enum LogoPosition {
  FRONT_CENTER,
  FRONT_LEFT_ARM
  FRONT_LEFT_CHEST,
  FRONT_LEFT_LEG,
  FRONT_RIGHT_ARM,
  FRONT_RIGHT_CHEST,
  FRONT_RIGHT_LEG
}

enum ProductSize {
  XS
  XXS
  S
  M
  L
  XL
  XXL
  XXXL
  XXXXL
  YXS
  YS
  YM
  YL
  YXL
  YXXL
  YXXXL
  OSFA
  NO_SIZE
}

enum Color {
  RED
  GREEN
  BLUE
  YELLOW
  ORANGE
  PURPLE
  WHITE
  BLACK
  BROWN
  GRAY
  PINK
  LIGHT_BLUE
}

enum MarcoOrderStatus {
  canceled
  completed
  intake_hold
  open
  production_hold
  received
  shipped
}

enum MarcoItemWorkflowState  {
  canceled
  completed
  intake_hold
  open
  production_hold
  shipped
}

enum MacroServiceRequestWorkflowState {
  approved
  canceled
  closed
  completed
  open
  rejected
}

input MarcoOrderItem {
  id: Int!
  customer_sku: String
  sku_id: Int
  description: String
  workflow_state: MarcoItemWorkflowState
  quantity: Int!
  client_item_num: String
}

input MarcoShipment {
  shipping_carrier: ShippingProvider
  shipping_method: String
  tracking_number: String
  search_link: String
  delivered: Boolean
}

input MarcoOrderStatusNote {
  note: String!
  created_at: String!
}

input MarcoServiceRequest {
  reason: String
  workflow_state: MacroServiceRequestWorkflowState
  items_count: Int
  created_at: String
}

input MarcoShippingAddress {
  ship_to_first_name: String,
  ship_to_last_name: String,
  ship_to_company_name: String,
  ship_to_address: String,
  ship_to_address_2: String,
  ship_to_city:  String,
  ship_to_state:  String,
  ship_to_zip_code:  String,
  ship_to_country:  String,
  ship_to_email: String,
  ship_to_telephone:  String,
}

input MarcoEventInput {
  id: Int!,
  purchase_order: String!,
  status: MarcoOrderStatus,
  received_at: String
  created_at: String
  shipped_at: String,
  shipments: [MarcoShipment]
  address: MarcoShippingAddress
}

type MarcoWebhookResponse {
  success: Boolean!
  error: String
}

input StoreInput {
  accountManager: String
  accountManagerEmail: String
  accountManagerUDID: String
  activityType: String!
  city: String!
  fundraiserId: Int
  groupLeader: String
  groupLeaderEmail: String
  groupLeaderUDID: String
  groupName: String!
  logoDigitalUrl: String!
  logoEmbroideryUrl: String!
  logoHatUrl: String!
  """ Pair of primary colors in format  #RRGGBB|#RRGGBB """
  logoPrimaryColor: String!
  logoWebHeaderUrl: String!
  name: String!
  organizationId: Int
  organizationLegalName: String!
  partnerId: Int
  pointsPercentage: Int! = 10
  salesRep: String
  salesRepEmail: String
  salesRepUDID: String
  state: String!
  storeCode: String
  storeUrl: String!
  zip: String!
  schoolName: String
  schoolId: String
}

enum StoreOrderStatus {
  DESIGN
  HOLD
  SHIPPED
  DELIVERED
  CREATED
  REJECTED
  MARCO_REJECTED
  CLOSED
}

type StoreOrderResult {
  id: String!
  netsuiteId: String
  status: StoreOrderStatus
  trackingNumber: String
  trackingUrl: String
  createdAt: DateTime!
  updatedAt: DateTime!
  shippingReceivedAt: DateTime
}

input StoreSearchPaginationInput {
  limit: Int = 24
  offset: Int = 0
}

input StoreSearchWhereInput {
  name: String
  state: String
}

type StoreType {
  city: String
  fundraiserId: Int
  id: String!
  name: String!
  organizationId: Int
  organizationLegalName: String
  organizationName: String
  schoolId: String
  schoolName: String
  state: String
  storeCode: String
  storeUrl: String!
  teamId: String
  zip: String
}

type StoreSearchResponse {
  stores: [StoreType!]!
  total: Int!
  limit: Int!
  offset: Int!
}

input EasypostEventInput {
  eventId: String!
  trackingNumber: String!
  trackerId: String!
  shipmentId: String!
  referenceId: String!
  status: String!
  carrier: String!
  estDeliveryDate: DateTime
  statusDetail: String
  orderId: String!
  trackingUrl: String
  vendor: Vendor!
}

type EasypostWebhookResponse {
  success: Boolean!
  error: String
}
