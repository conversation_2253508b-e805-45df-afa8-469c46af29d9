/* eslint-disable id-denylist, import/max-dependencies, functional/no-return-void */
import http from "http";
import { readFileSync } from "fs";
import * as path from "node:path";
import { buildSubgraphSchema } from "@apollo/subgraph";
import { GraphQLResolverMap } from "apollo-graphql";
import { ApolloServer, ApolloServerPlugin } from "@apollo/server";
import { expressMiddleware } from "@apollo/server/express4";
import { ApolloServerPluginDrainHttpServer } from "@apollo/server/plugin/drainHttpServer";
import express, { Express } from "express";
import { applyMiddleware } from "graphql-middleware";
import { gql } from "graphql-tag";
import createNewRelicPlugin from "@newrelic/apollo-server-plugin";

import { ApolloServerPlugin as SentryPlugin } from "@store-monorepo/sentry";

import shield from "./shield";
import resolvers from "./resolvers";

const typeDefs = gql(
  readFileSync(path.join(path.resolve(__dirname), "schema.graphql"), {
    encoding: "utf-8"
  })
);

export default async (
  server: Express
) => {
  const middleware = [shield];
  const httpServer = http.createServer(server);
  const apolloServer = new ApolloServer({
    schema: applyMiddleware(buildSubgraphSchema({
      typeDefs,
      resolvers: resolvers as GraphQLResolverMap<unknown>
    }), ...middleware),

    plugins: [
      ApolloServerPluginDrainHttpServer({ httpServer }),
      SentryPlugin(),
      createNewRelicPlugin<ApolloServerPlugin>({})
    ]
  });

  await apolloServer.start();
  server.post(
    "/api/graphql",
    express.json({ type: "application/json", limit: "10mb" }),
    expressMiddleware(apolloServer, {
      context: async ({ req, res }) => {
        const user = req.headers.user as string | null;
        return {
          req,
          res,
          user: user ? JSON.parse(user) : null,
        };
      },
    })
  );

  await new Promise<void>((resolve) => {
    console.log(`server started at :${3000}/graphql`);
    httpServer.listen({ port: 3000 }, resolve);
  });
  return server;
};
