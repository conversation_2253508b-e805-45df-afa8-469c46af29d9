import express from "express";
import { rule, shield } from "graphql-shield";

export type ApolloContext = {
  req: express.Request;
}

const requestIsFromBackendSnapService = ({
  req: { headers },
}: ApolloContext): boolean => !!headers["x-api-key"];

const isBackendSnapService = rule()((_, __, context: ApolloContext): boolean => requestIsFromBackendSnapService(context)
);

export default shield(
  {
    Query: {
      "*": isBackendSnapService,
    },
    Mutation: {
      "*": isBackendSnapService
    }
  },
  {
    debug: true,
  }
);
