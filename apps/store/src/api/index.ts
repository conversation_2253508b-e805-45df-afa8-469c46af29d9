import "newrelic";
import { Express } from "express";

import makeGraphqlAPI from "./graphql";
import addHealthChecks from "./health-check";
import addWebhooks from "./webhooks";
import addPDFfReports from "./pdf";
import addPngBackLogos from "./png";

const configure = async (server: Express) => {
  await makeGraphqlAPI(server);
  addHealthChecks(server);
  addWebhooks(server);
  addPDFfReports(server);
  addPngBackLogos(server);
};

export default {
  configure
};
