/* eslint-disable max-lines-per-function, functional/no-this-expressions */
import { Express, Request, Response } from "express";
import Handlebars from "handlebars";

import <PERSON>o from "@store-monorepo/repo";
import ImageScraper from "@store-monorepo/logo-uploader";
import Sentry from "@store-monorepo/sentry";

import Custom<PERSON>ogo from "./custom-logo";

const customLogoTmpl = Handlebars.compile(CustomLogo);

Handlebars.registerHelper("normalizeName", function(name) {
  return name ? name.substring(0, 13) : "";
});

Handlebars.registerHelper("normalizeNumber", function(number) {
  return number ? number.substring(0, 2) : "";
});

Handlebars.registerHelper("normalizeColor", function(color) {
  return color ? color.toLowerCase().replace("_", "") : "";
});

Handlebars.registerHelper("isNameLong", function(name, options) {
  if (name?.length >= 9) {
    return options.fn(this);
  }
  return options.inverse(this);
});

Handlebars.registerHelper("normalizeNameFontSize", function(name) {
  return [
    "",
    "75px", "75px", "75px", "75px", "75px", "75px", "75px", "75px",
    "60px", "60px", "55px", "50px", "45px"
  ][Math.min(name?.length || 0, 13)];
});

export default function(server: Express): Express {
  server.get("/api/custom-logo/:id\\_d.png",
    async (req: Request, resp: Response) => {
      const { id } = req.params;
      try {
        const [product] = await Repo.OrdersOnProducts.findBy({ id });
        if (!product) return resp.sendStatus(404);

        resp.type("image/png");
        const png = await ImageScraper.createBackprintPNG(customLogoTmpl({
          ...(product.printAttributes as object || {}),
          width: 7000,
          height: 7000
        }));
        resp.end(png);
      } catch (error) {
        Sentry.captureException(error);
        resp.sendStatus(500);
      }
    });

  server.get("/api/custom-logo/sandbox",
    async (req: Request, resp: Response) => {
      const { name, number, font, color = "BLACK", output = "svg", size = 600 } = req.query;
      const printAttributes = {
        width: size,
        height: size,
        ...(name ? { name: { value: name, color, font } } : {}),
        ...(number ? { number: { value: number, color, font } } : {})
      };

      try {
        switch (output) {
        case "png":
          resp.type("image/png");
          resp.end(await ImageScraper.openSVGAndDownloadPNG(customLogoTmpl(printAttributes)));
          break;

        case "svg":
        default:
          resp.type("image/svg+xml");
          resp.end(customLogoTmpl(printAttributes));
          break;
        }
      } catch (error) {
        console.error(error);
        resp.sendStatus(503);
      }
    });

  return server;
}
