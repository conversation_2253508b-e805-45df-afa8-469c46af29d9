/* eslint-disable max-lines-per-function */
import * as React from "react";
import { Document, Page, Polyline, Rect, renderToBuffer, Svg, Text, View } from "@react-pdf/renderer";

import type { PublicFundraiserData } from "@store-monorepo/graphql/types";

import Header from "../common/header";
import Footer from "../common/footer";

import type { Order } from "../../../types";
import Format from "../format";

import styles from "../styles";

interface PackingSlipProps {
  order: Order;
  fundraiser?: PublicFundraiserData;
}

const Br = () => "\n";

export default function PackingSlip({ order, fundraiser }: PackingSlipProps) {
  const items = [...order.products].sort((a, b) => (a.receiverName.toLowerCase() > b.receiverName.toLowerCase() ? 1 : -1));

  return (
    <Document>
      <Page size="A4" style={styles.page}>
        <Header/>

        <View style={styles.section}>
          <Text style={styles.title}>
            Congrats on your campaign! We’re so excited to support your group and
            help you reach your goals.
          </Text>
          <View style={styles.tableHeader}>
            <Text style={[styles.col, { width: "50%" }]}>Campaign</Text>
            <Text style={[styles.col, { width: "20%" }]}>Campaign ID</Text>
            <Text style={[styles.col, { width: "15%" }]}>Order Placed</Text>
            <Text style={[styles.col, { width: "15%" }]}>Order</Text>
          </View>
          <View style={styles.tableRow}>
            <Text style={[styles.col, { width: "50%" }]}>{fundraiser?.name}</Text>
            <Text style={[styles.col, { width: "20%" }]}>{order.fundraiserId}</Text>
            <Text style={[styles.col, { width: "15%" }]}>{Format.toDate(order.createdAt)}</Text>
            <Text style={[styles.col, { width: "15%" }]}>{order.netsuiteId}</Text>
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.title}>Additional Items</Text>
          <View style={styles.tableHeader} fixed>
            <Svg width="12" height="12" viewBox="0 0 24 24" style={styles.checkbox}>
              <Polyline points="4 13 9 18 20 7" stroke="#000000" stroke-width="1" />
            </Svg>
            <Text style={[styles.col, { width: "25%" }]}>Recipient (A-Z)</Text>
            <Text style={[styles.col, { width: "30%" }]}>Item / SKU</Text>
            <Text style={[styles.col, { width: "10%" }]}>Size</Text>
            <Text style={[styles.col, { width: "10%" }]}>Qty</Text>
          </View>
          {items.map((row) => (
            <View key={row.id} style={styles.tableRow}>
              <Svg
                width="14"
                height="14"
                viewBox="0 0 24 24"
                style={styles.checkbox}
              >
                <Rect
                  width="18"
                  height="18"
                  x="0"
                  y="0"
                  stroke="#aaaaaa"
                  stroke-width="1"
                />
              </Svg>

              <Text style={[styles.col, { width: "25%" }]}>{row.receiverName}</Text>
              <Text style={[styles.col, { width: "30%" }]}>
                {row.product.name}<Br />
                <Text style={{ fontSize: 6 }}>{row.product.sku}</Text>
              </Text>
              <Text style={[styles.col, { width: "10%" }]}>{row.product.size}</Text>
              <Text style={[styles.col, { width: "10%" }]}>1</Text>
            </View>
          ))}
        </View>

        <Footer refundDays={60}/>
      </Page>
    </Document>
  );
}

export const render = (order: Order, fundraiser?: PublicFundraiserData) => renderToBuffer(<PackingSlip order={order} fundraiser={fundraiser} />);
