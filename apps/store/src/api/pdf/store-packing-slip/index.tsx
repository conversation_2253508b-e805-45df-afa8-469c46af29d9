/* eslint-disable max-lines-per-function, no-warning-comments, no-inline-comments */
import * as React from "react";
import { Document, Page, renderToBuffer, Text, View } from "@react-pdf/renderer";

import Header from "../common/header";
import Footer from "../common/footer";

import type { Order } from "../../../types";
import Format from "../format";

import styles from "../styles";

interface PackingSlipProps {
  order: Order;
}

const Br = () => "\n";

export default function StorePackingSlip({ order }: PackingSlipProps) {
  const items = [...order.products].sort((a, b) => (a.product.name > b.product.name ? 1 : -1));
  const orderTotal = order.baseSubtotal + order.shippingCost + order.taxAmount - order.discountAmount;

  return (
    <Document>
      <Page size="A4" style={styles.page}>
        <Header/>

        <View style={styles.section}>
          <View style={styles.tableHeader}>
            <Text style={[styles.col, { width: "50%" }]}>Ship To</Text>
            <Text style={[styles.col, { width: "25%" }]}>Order Placed</Text>
            <Text style={[styles.col, { width: "25%" }]}>Order #</Text>
          </View>
          <View style={styles.tableRow}>
            <Text style={[styles.col, { width: "50%" }]}>
              {order.shipTo}<Br />
              {order.street}<Br />
              {order.city} {order.state} {order.zipCode}
            </Text>
            <Text style={[styles.col, { width: "25%" }]}>{Format.toDate(order.createdAt)}</Text>
            <Text style={[styles.col, { width: "25%" }]}>{order.netsuiteId}</Text>
          </View>
        </View>

        <View style={styles.section}>
          <View style={styles.tableHeader} fixed>
            <Text style={[styles.col, { width: "45%" }]}>Item / SKU</Text>
            <Text style={[styles.col, { width: "20%" }]}>Color</Text>
            <Text style={[styles.col, { width: "10%" }]}>Size</Text>
            <Text style={[styles.col, { width: "10%" }]}>Qty</Text>
            <Text style={[styles.col, { width: "15%" }]}>Price</Text>
          </View>
          {items.map((row, index) => (
            <View key={index} style={styles.tableRow}>
              <Text style={[styles.col, { width: "45%" }]}>
                {row.product.name}<Br/>
                <Text style={{ fontSize: 6 }}>{row.product.sku}</Text>
              </Text>
              <Text style={[styles.col, { width: "20%" }]}>{row.product.color}</Text>
              <Text style={[styles.col, { width: "10%" }]}>{row.product.size}</Text>
              <Text style={[styles.col, { width: "10%" }]}>1</Text>
              <Text style={[styles.col, { width: "15%", textAlign: "right" }]}>{Format.toCurrency(row.amount)}</Text>
            </View>
          ))}
          <View style={styles.tableRow}>
            <Text style={[styles.col, styles.strong, { width: "85%", textAlign: "right" }]}>Subtotal</Text>
            <Text style={[styles.col, styles.strong, { width: "15%", textAlign: "right" }]}>{Format.toCurrency(order.baseSubtotal)}</Text>
          </View>
          <View style={styles.tableRow}>
            <Text style={[styles.col, styles.strong, { width: "85%", textAlign: "right" }]}>Tax</Text>
            <Text style={[styles.col, styles.strong, { width: "15%", textAlign: "right" }]}>{Format.toCurrency(order.taxAmount)}</Text>
          </View>
          <View style={styles.tableRow}>
            <Text style={[styles.col, styles.strong, { width: "85%", textAlign: "right" }]}>Shipping</Text>
            <Text style={[styles.col, styles.strong, { width: "15%", textAlign: "right" }]}>{Format.toCurrency(order.shippingCost)}</Text>
          </View>
          {order.discountAmount > 0 && <View style={styles.tableRow}>
            <Text style={[styles.col, styles.strong, { width: "85%", textAlign: "right" }]}>Discount</Text>
            <Text style={[styles.col, styles.strong, { width: "15%", textAlign: "right" }]}>{Format.toCurrency(-order.discountAmount)}</Text>
          </View>}
          <View style={styles.tableRow}>
            <Text style={[styles.col, styles.strong, { width: "85%", textAlign: "right" }]}>Order Total</Text>
            <Text style={[styles.col, styles.strong, { width: "15%", textAlign: "right" }]}>{Format.toCurrency(orderTotal)}</Text>
          </View>
        </View>

        <Footer refundDays={30}/>
      </Page>
    </Document>
  );
}

export const render = (order: Order) => renderToBuffer(<StorePackingSlip order={order} />);
