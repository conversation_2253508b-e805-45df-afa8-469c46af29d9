/* eslint-disable max-statements, max-lines-per-function, functional/no-return-void, functional/immutable-data, sonarjs/no-duplicate-string */
import { Request, Response, Express, RequestHandler } from "express";

import Repo, { VENDOR } from "@store-monorepo/repo";
import Graphql from "@store-monorepo/graphql";
import { log } from "@store-monorepo/logger";

import type { Order } from "../../types";
import { render as renderPackingSlip } from "./packing-slip";
import { render as renderStorePackingSlip } from "./store-packing-slip";

const PACKING_SLIP_EXPIRATION_TIME = 1000 * 60 * 60 * 24 * 90;

const fetchOrder: RequestHandler = async (req, resp, next) => {
  const netsuiteId = req.params.id;
  const order = await Repo.Order.findBy({ netsuiteId }, true) as Order;

  if (!order) {
    log("express", "info", "packing-slip", {
      netsuiteId,
      error: "Order not found."
    });

    return resp.status(404).end("Order not found.");
  }

  resp.locals.order = order;

  return next();
};

const checkOrderExpiration: RequestHandler = async (req, resp, next) => {
  const netsuiteId = req.params.id;
  const order: Order = resp.locals.order;

  if ((Date.now() - order.createdAt.getTime()) > PACKING_SLIP_EXPIRATION_TIME) {
    log("express", "info", "packing-slip", {
      netsuiteId,
      orderId: order.id,
      fundraiserId: order.fundraiserId,
      error: "Packing slips PDF is publicly visible for 90 days only."
    });

    return resp.status(410).end("Packing slips PDF is publicly visible for 90 days only.");
  }

  return next();
};

const defaultPackingSlip = async (req: Request, resp: Response) => {
  const netsuiteId = req.params.id;
  const order: Order = resp.locals.order;

  const fundraiser = await Graphql.publicFundraiserData(parseInt(order.fundraiserId));

  if (!fundraiser) {
    log("express", "info", "packing-slip", {
      netsuiteId,
      orderId: order.id,
      fundraiserId: order.fundraiserId,
      error: "Fundraiser not found"
    });

    return resp.status(404).end("Fundraiser not found.");
  }

  resp.type("application/pdf");
  resp.end(await renderPackingSlip(order, fundraiser), "binary");
};

const storePackingSlip = async (req: Request, resp: Response) => {
  const order: Order = resp.locals.order;

  resp.type("application/pdf");
  resp.end(await renderStorePackingSlip(order), "binary");
};

export default function(server: Express): Express {
  server.get(
    "/api/packing-slip/:id.pdf",
    [fetchOrder, checkOrderExpiration],
    async (req: Request, resp: Response) => {
      const netsuiteId = req.params.id;
      const order: Order = resp.locals.order;

      log("express", "info", "packing-slip", {
        netsuiteId,
        orderId: order.id,
        fundraiserId: order.fundraiserId,
      });

      if (order.vendor === VENDOR.STORE) {
        return storePackingSlip(req, resp);
      }

      return defaultPackingSlip(req, resp);
    }
  );

  server.get(
    "/api/packing-slip/:id",
    (req: Request, resp: Response) => {
      const netsuiteId = req.params.id;
      return resp.redirect(301, `/api/packing-slip/${netsuiteId}.pdf`);
    }
  );

  return server;
}
