import { StyleSheet } from "@react-pdf/renderer";

enum JustifyContent {
  SpaceBetween = "space-between"
}

const styles = StyleSheet.create({
  page: {
    fontFamily: "Helvetica",
    fontWeight: "normal",
    fontSize: 10,
    padding: "30 40",
    lineHeight: 1.25,
    flexDirection: "column"
  },

  header: {
    width: "100%",
    display: "flex",
    justifyContent: JustifyContent.SpaceBetween,
    marginBottom: 25,
    flexDirection: "row"
  },

  section: {
    marginBottom: 20
  },

  title: {
    color: "#1f76cd",
    fontFamily: "Courier",
    fontSize: 16,
    lineHeight: 1.15,
    marginBottom: 20
  },

  aside: {
    display: "flex",
    flexDirection: "column",
    textAlign: "right",
    alignItems: "flex-end"
  },

  logo: {
    width: 120,
    height: 50,
    objectFit: "contain",
    paddingRight: 1
  },

  text: {
    fontSize: 9,
    marginBottom: 10
  },

  strong: {
    fontFamily: "Helvetica-Bold",
    fontWeight: "bold"
  },

  link: {
    textDecoration: "none",
    color: "rgb(0 0 0);",
    textAlign: "right"
  },

  tableHeader: {
    display: "flex",
    flexDirection: "row",
    backgroundColor: "rgb(229 231 235)",
    justifyContent: JustifyContent.SpaceBetween,
    alignItems: "center",
    textAlign: "left",
    padding: "4 2 2 2",
    height: 24,
    fontSize: 8
  },

  tableRow: {
    display: "flex",
    flexDirection: "row",
    justifyContent: JustifyContent.SpaceBetween,
    alignItems: "center",
    textAlign: "left",
    padding: "4 2 2 2",
    fontSize: 8
  },

  col: {
    padding: "0 2",
    overflow: "hidden"
  },

  checkbox: {
    margin: "0 10"
  },

  rect: {
    margin: "0 10"
  }
});

export default styles;
