import * as React from "react";
import { Link, Text, View } from "@react-pdf/renderer";

import styles from "../styles";

interface FooterProps {
  refundDays: number
}

export default function Footer({ refundDays }: FooterProps) {
  return (
    <View style={styles.section}>
      <Text style={styles.title}>Refund and Exchange Policy</Text>
      <Text style={styles.text}>
        Thank you for your purchase! We hope you enjoy your item. All items are
        made to order, and for that reason items that have been decorated with a
        logo, name, or number are not eligible for a return or exchange unless
        it is determined that your order is not 100% free from defects in
        materials and decoration.
        <Text style={styles.strong}>
          In the event items are missing or not 100% defect-free, a refund or
          replacement must be requested within {refundDays} days of the delivery date.
        </Text>
      </Text>
      <Text style={styles.text}>
        Please email{" "}
        <Link src="mailto:<EMAIL>" style={styles.link}>
          <EMAIL>
        </Link>{" "}
        with details of your purchase and a picture of the product you received
        to help resolve your issue.
      </Text>
    </View>
  );
}
