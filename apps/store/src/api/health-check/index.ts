/* eslint-disable functional/no-return-void */
import { Request, Response, Express } from "express";

const checkEnvVars = (): boolean => {
  const missingVars = [
    "AWS_ACCESS_KEY_ID",
    "AWS_SECRET_ACCESS_KEY",
    "D<PERSON><PERSON>ASE_URL",
    "GRAPHQL_API_KEY",
    "GRAPHQL_URL",
    "MAGENTO_API_TOKEN",
    "MAGENTO_BASE_URL",
    "MARCO_ACCOUNT_ID",
    "MARCO_ACCOUNT_ZIP",
    "MARCO_API_KEY",
    "MARCO_API_URL",
    "NETSUITE_ACCOUNT_ID",
    "NETSUITE_APPLICATION_ID",
    "NETSUITE_CLIENT_KEY",
    "NETSUITE_CLIENT_SECRET",
    "NETSUITE_CUSTOMER_ID",
    "NETSUITE_TOKEN_KEY",
    "NETSUITE_TOKEN_SECRET",
    "S3_BASE_URL",
    "S3_BUCKET",
    "SENTRY_DSN",
    "TEMPORAL_NAMESPACE",
    "TEMPORAL_URL",
    "ZENDESK_SUBDOMAIN",
    "ZENDESK_TOKEN",
    "ZENDESK_USERNAME"
  ].map(name => [name, process.env[name]]).filter(([name, value]: [string, string]) => !value);

  if (missingVars.length > 0) {
    console.error(`Missing environment variables: ${missingVars.map(([name, value]: [string, string]) => name).join(", ")}`);
    return false;
  }

  return true;
};

const readinessCheck = async (_req: Request, resp: Response) => {
  return checkEnvVars() ? resp.status(200).send("ok") : resp.status(500).end();
};

const livenessCheck = async (_req: Request, resp: Response) => {
  return checkEnvVars() ? resp.status(200).send("ok") : resp.status(500).end();
};

export default function(
  server: Express
): void {
  server.use("/api/health-check/readiness", readinessCheck);
  server.use("/api/health-check/liveness", livenessCheck);
}
