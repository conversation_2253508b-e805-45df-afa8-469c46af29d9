/* eslint-disable functional/immutable-data */
import { RequestHand<PERSON> } from "express";

import Graphql from "@store-monorepo/graphql";

import Helpers from "../../lib/helpers";

const authenticate = (): RequestHandler => {
  return async (req, resp, next) => {
    const token = Helpers.fetchToken(req);
    if (!token) return resp.redirect(Helpers.redirectTo(req));
    resp.locals.me = await Graphql.me(token);
    return next();
  };
};

export default authenticate;
