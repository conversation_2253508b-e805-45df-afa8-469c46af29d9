import { RequestHandler } from "express";

const hasPermissions = (...permissions: string[]): RequestHandler => {
  return (req, resp, next) => {
    const me = resp.locals.me;

    if (!me) return resp.status(401).end("Not Authorized");

    if (
      permissions.length > 0 &&
      !permissions.every((permission) => me.permissions.includes(permission))
    )
      return resp.status(401).end("Has no permissions");

    next();
  };
};

export default hasPermissions;
