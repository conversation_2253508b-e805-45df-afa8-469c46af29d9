{"data": [{"attributes": {"anonymous_id": null, "created": "2023-01-15T10:30:00+00:00", "email": "<EMAIL>", "external_id": null, "first_name": "<PERSON>", "image": null, "last_event_date": "2024-03-20T15:45:30+00:00", "last_name": "<PERSON><PERSON>", "locale": null, "location": {"address1": "123 Main St", "address2": null, "city": "Anytown", "country": "US", "ip": "***********", "latitude": null, "longitude": null, "region": "California", "timezone": "America/Los_Angeles", "zip": "12345"}, "organization": null, "phone_number": "**********", "properties": {"fund_end_date": "2023-12-31", "$consent": ["email"], "$consent_timestamp": "2023-06-15T09:00:00.000Z", "$source": -9, "Activity Type": "basketball", "City": "Anytown", "Logo": "https://example.com/logos/123456_logo.png", "MagentoAccountCreated": "2023-03-01 14:30:00", "MagentoCustomerGroup": "General", "MagentoStore": "ANYTOWN", "MagentoWebsiteID": "1", "Organization": "Anytown High School", "ProgramName": "Anytown Basketball 2023", "State/Region": "CA", "StoreUrl": "https://anytown.example.store", "Zip Code": "12345", "fund_start_date": "2023-01-01", "fundraiser_id": "123456", "received 'store is live' email": "n/a", "store_create_date": "2023-01-01 09:00:00"}, "subscriptions": {"email": {"marketing": {"consent": "SUBSCRIBED", "custom_method_detail": null, "double_optin": false, "list_suppressions": [], "method": "API", "method_detail": "1234", "suppressions": [], "timestamp": "2023-06-15T09:00:00.000000+00:00"}}, "sms": {"marketing": {"consent": "NEVER_SUBSCRIBED", "method": null, "method_detail": null, "timestamp": null}}}, "title": null, "updated": "2024-03-20T15:46:00+00:00"}, "id": "01ABCD1234EFGH5678IJKL9012", "links": {"self": "https://a.klaviyo.com/api/profiles/01ABCD1234EFGH5678IJKL9012/"}, "relationships": {"lists": {"links": {"related": "https://a.klaviyo.com/api/profiles/01ABCD1234EFGH5678IJKL9012/lists/", "self": "https://a.klaviyo.com/api/profiles/01ABCD1234EFGH5678IJKL9012/relationships/lists/"}}, "segments": {"links": {"related": "https://a.klaviyo.com/api/profiles/01ABCD1234EFGH5678IJKL9012/segments/", "self": "https://a.klaviyo.com/api/profiles/01ABCD1234EFGH5678IJKL9012/relationships/segments/"}}}, "type": "profile"}], "links": {"next": null, "prev": null, "self": "https://a.klaviyo.com/api/profiles/?filter=equals(email,\"<EMAIL>\")"}}