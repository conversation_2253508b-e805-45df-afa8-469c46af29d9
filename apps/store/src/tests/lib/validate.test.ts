/* eslint-disable functional/no-return-void, import/no-unused-modules, check-file/folder-match-with-fex, max-lines-per-function, functional/immutable-data, functional/no-let, max-len, import/no-relative-parent-imports, import/no-internal-modules, id-denylist */
import nock from "nock";
import fetch from "cross-fetch";

import Validate from "../../lib/validate";

global.fetch = fetch;

describe("Validate", () => {
  beforeAll(() => {
    nock("https://snapraiselogos.s3.amazonaws.com").get("/PROD-SVG/203350_d.svg").
      reply(200);

    nock("https://snapraiselogos.s3.amazonaws.com").get("/PROD-SVG/203350_d.bmp").
      reply(404);
  });

  it("isEmail", () => {
    expect(Validate.isEmail("<EMAIL>")).toBe(true);
    expect(Validate.isEmail("<EMAIL>")).toBe(true);
    expect(Validate.isEmail("<EMAIL>")).toBe(true);

    expect(Validate.isEmail("email@snap.c")).toBe(false);
    expect(Validate.isEmail("email@snapcom")).toBe(false);
  });

  it("isColor", () => {
    expect(Validate.isColor("#000000")).toBe(true);
    expect(Validate.isColor("#DEDBEF")).toBe(true);
    expect(Validate.isColor("#aaaccc")).toBe(true);

    expect(Validate.isColor("#aaaggg")).toBe(false);
    expect(Validate.isColor("#eee")).toBe(false);
    expect(Validate.isColor("FFFFFF")).toBe(false);
  });

  it("isComplexColor", () => {
    expect(Validate.isComplexColor("#006AB6|#005595")).toBe(true);

    expect(Validate.isComplexColor("#006AB6")).toBe(false);
    expect(Validate.isComplexColor("#006AB6|#005595|#000000")).toBe(false);
  });

  it("isExistingUrl", async () => {
    expect(await Validate.isExistingUrl("https://snapraiselogos.s3.amazonaws.com/PROD-SVG/203350_d.svg")).toBe(true);

    expect(await Validate.isExistingUrl("https://snapraiselogos.s3.amazonaws.com/PROD-SVG/203350_d.bmp")).toBe(false);
  });

  it("isUrl", async () => {
    expect(Validate.isUrl("https://123456.snap.store/")).toBe(true);
    expect(Validate.isUrl("http://user:password@[3ffe:2a00:100:7031::1]:8080")).toBe(true);

    expect(Validate.isUrl("")).toBe(false);
    expect(Validate.isUrl("Just another message")).toBe(false);
  });
});
