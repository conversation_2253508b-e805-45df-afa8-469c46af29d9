/* eslint-disable max-len, max-lines, functional/no-return-void, functional/immutable-data, max-lines-per-function, check-file/folder-match-with-fex, import/no-unused-modules, @typescript-eslint/no-explicit-any, check-file/folder-match-with-fex */
import nock from "nock";
import fetch from "cross-fetch";

import { Store } from "@prisma/client";

import Magento from "../../lib/magento";

global.fetch = fetch;

const featureOn = jest.fn();
jest.mock("@store-monorepo/splitio-client", () => {
  return {
    on: jest.fn().mockImplementation(() => featureOn())
  };
});

const store: Store = {
  accountManager: null,
  accountManagerEmail: null,
  accountManagerUDID: null,
  activityType: "other",
  builtAt: null,
  city: "Smyrna",
  createdAt: new Date("2025-03-19T18:48:12.901Z"),
  deactivatedAt: null,
  emailLogo: null,
  endDate: new Date("2025-04-25T00:00:00.000Z"),
  fanStore: true,
  fundraiserEntityId: 222222,
  fundraiserId: 111111,
  fundraiserLogo: "*.jpg",
  fundraiserLogoId: 2177647,
  fundraiserPrimaryColor: "#0504AA",
  fundraiserSecondaryColor: "#fff",
  groupId: 141250,
  groupLeader: "Sa...os",
  groupLeaderEmail: "<EMAIL>",
  groupLeaderUDID: "udu_cm8g9...d4pr",
  groupName: "Imp...om ",
  hasParticipants: true,
  id: "store_64b...64",
  incentiveStore: true,
  logo: "Custom Logo",
  logoDigitalUrl: null,
  logoDigitalVerifiedAt: null,
  logoEmbroideryUrl: null,
  logoEmbroideryVerifiedAt: null,
  logoHatUrl: null,
  logoHatVerifiedAt: null,
  logoNotes: "",
  logoPrimaryColor: "#6DCFF6|#3B7BB0",
  logoScript: "",
  logoSecondaryColor: "#FFFFFF|#E4E8FF",
  logoType: "TEAM",
  logoUpdatedAt: new Date("2025-03-19T18:49:20.000Z"),
  logoWebHeaderUrl: null,
  logoWebHeaderVerifiedAt: null,
  magentoManagerEmail: null,
  magentoStoreCode: null,
  magentoStoreId: null,
  name: "Test 2025",
  organizationId: 11111,
  organizationLegalName: "Ca...ol",
  organizationName: "Cam...ool",
  partnerId: null,
  pointsPercentage: 10,
  previousLogo: false,
  raiseCreatedAt: new Date("2025-03-19T18:48:07.000Z"),
  raiseUpdatedAt: new Date("2025-03-26T15:35:17.000Z"),
  salesRep: "LaDa...d",
  salesRepEmail: "<EMAIL>",
  salesRepUDID: "udu_clxf...i1",
  slug: "impact-prom-donation-drive-2025",
  startDate: new Date("2025-03-28T00:00:00.000Z"),
  state: "georgia",
  status: "deleted",
  storeCode: null,
  storeUrl: "https://222222.store.url",
  teamId: "team_27d1e...a8c9",
  teamSize: 60,
  updatedAt: new Date("2025-03-26T15:35:18.086Z"),
  zip: "30080",
};

describe("Magento", () => {
  describe("toDomain", () => {
    it("should parse url correctly", () => {
      expect(Magento.toDomain("111222.snap.store")).toEqual("111222.snap.store");
      expect(Magento.toDomain("https://333444.snap.store")).toEqual("333444.snap.store");
      expect(Magento.toDomain("https://555666.snap.store/")).toEqual("555666.snap.store");
    });
  });

  describe("createStore", () => {
    const customersReply = jest.fn();
    const createStoreScopeReply = jest.fn();

    const customersReplyError = "Could not create customer";
    const createStoreScopeReplyError = "Could not create store";

    const scopeId = "13906";

    beforeEach(() => {
      featureOn.mockResolvedValue(true);

      nock(process.env.MAGENTO_WORKER_URL).
        post("/rest/default/V1/integration/admin/token").
        reply(200, "\"AuthToken\"");

      nock(process.env.MAGENTO_WORKER_URL).
        post("/rest/default/V1/customers").
        reply(200, jest.fn().mockImplementation((uri, body) => customersReply(uri, body)));

      nock(process.env.MAGENTO_WORKER_URL).
        post("/rest/default/V1/snapraise-storescopeapi/createstorescope").
        reply(200,jest.fn().mockImplementation((uri, body) => createStoreScopeReply(uri, body)));
    });

    it("should create store scope and manager", async () => {
      createStoreScopeReply.mockImplementation((uri, body: {store_data?: {name: string}}) => ({
        code: body?.store_data?.name,
        scope_id: scopeId
      }));

      customersReply.mockImplementation((uri, body: {customer?: {email: string}}) => ({
        email: body?.customer?.email,
      }));

      const { magentoStoreId, magentoStoreCode, magentoManagerEmail, error, warning } = await Magento.createStore(store);

      expect(magentoStoreId).toEqual(scopeId);
      expect(magentoStoreCode).toEqual(`SNP${store.fundraiserEntityId}`);
      expect(magentoManagerEmail).toEqual(`${magentoStoreCode}@snap.store`);
      expect(error).toBeFalsy();
      expect(warning).toBeFalsy();
    });

    it("should create store scope but fail on create manager", async () => {
      createStoreScopeReply.mockImplementation((uri, body: {store_data?: {name: string}}) => ({
        code: body?.store_data?.name,
        scope_id: scopeId
      }));

      customersReply.mockImplementation(() => ({
        message: customersReplyError,
      }));

      const { magentoStoreId, magentoStoreCode, magentoManagerEmail, error, warning } = await Magento.createStore(store);

      expect(magentoStoreId).toEqual(scopeId);
      expect(magentoStoreCode).toEqual(`SNP${store.fundraiserEntityId}`);
      expect(magentoManagerEmail).toBeUndefined();
      expect(error).toBeFalsy();
      expect(warning).toEqual(customersReplyError);
    });

    it("should not create either store or manager", async () => {
      createStoreScopeReply.mockImplementation(() => ({
        message: createStoreScopeReplyError
      }));

      customersReply.mockImplementation(() => ({
        message: customersReplyError,
      }));

      const { magentoStoreId, magentoStoreCode, magentoManagerEmail, error, warning } = await Magento.createStore(store);

      expect(magentoStoreId).toBeUndefined();
      expect(magentoStoreCode).toBeUndefined();
      expect(magentoManagerEmail).toBeUndefined();
      expect(error).toEqual(createStoreScopeReplyError);
      expect(warning).toEqual(customersReplyError);
    });
  });
});
