/* eslint-disable max-lines, functional/no-return-void, functional/immutable-data, max-lines-per-function,
check-file/folder-match-with-fex, import/no-unused-modules, @typescript-eslint/no-explicit-any*/
import Magento from "../../lib/magento";
const { addTrackingNumbers } = Magento;

describe("addTrackingNumbers", () => {
  const mockOrder = {
    netsuiteId: "12345",
    carrier: "UPS",
    trackingNumber: "1Z999AA10123456784",
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should add tracking numbers successfully", async () => {
    const mockAuthToken = "token";
    const mockResponse = { success: true };
    console.log = jest.fn();
    global.fetch = jest.fn((url) => {
      if (url === `${process.env.MAGENTO_BASE_URL}/rest/default/V1/shipment/update`) {
        return Promise.resolve({
          json: () => Promise.resolve(mockResponse),
        });
      }
      return Promise.resolve({
        json: () => Promise.resolve(mockAuthToken),
      });
    }) as jest.Mock;

    await addTrackingNumbers(mockOrder as any);

    expect(global.fetch).toHaveBeenNthCalledWith(1,
      `${process.env.MAGENTO_WORKER_URL}/rest/default/V1/integration/admin/token`,
      expect.objectContaining({
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          username: process.env.MAGENTO_USERNAME,
          password: process.env.MAGENTO_PASSWORD,
        }),
      })
    );

    expect(global.fetch).toHaveBeenNthCalledWith(2,
      `${process.env.MAGENTO_BASE_URL}/rest/default/V1/shipment/update`,
      expect.objectContaining({
        method: "PUT",
        headers: {
          Authorization: `Bearer ${mockAuthToken}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          netsuiteOrderId: mockOrder.netsuiteId,
          carrierName: mockOrder.carrier.toUpperCase(),
          trackingNumber: mockOrder.trackingNumber,
        }),
      })
    );
    expect(console.log).toHaveBeenCalledWith("Tracking information added successfully");
  });

  it("should throw an error if adding tracking numbers fails", async () => {
    const mockAuthToken = "token";
    const mockResponse = { success: false };

    global.fetch = jest.fn((url) => {
      if (url === `${process.env.MAGENTO_BASE_URL}/rest/default/V1/shipment/update`) {
        return Promise.resolve({
          json: () => Promise.resolve(mockResponse),
        });
      }
      return Promise.resolve({
        json: () => Promise.resolve(mockAuthToken),
      });
    }) as jest.Mock;

    console.error = jest.fn();

    await expect(addTrackingNumbers(mockOrder as any)).rejects.toThrow("Failed to add tracking information");

    expect(global.fetch).toHaveBeenNthCalledWith(1,
      `${process.env.MAGENTO_WORKER_URL}/rest/default/V1/integration/admin/token`,
      expect.objectContaining({
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          username: process.env.MAGENTO_USERNAME,
          password: process.env.MAGENTO_PASSWORD,
        }),
      })
    );

    expect(global.fetch).toHaveBeenNthCalledWith(2,
      `${process.env.MAGENTO_BASE_URL}/rest/default/V1/shipment/update`,
      expect.objectContaining({
        method: "PUT",
        headers: {
          Authorization: `Bearer ${mockAuthToken}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          netsuiteOrderId: mockOrder.netsuiteId,
          carrierName: mockOrder.carrier.toUpperCase(),
          trackingNumber: mockOrder.trackingNumber,
        }),
      })
    );

    expect(console.error).toHaveBeenCalledWith("Failed to add tracking information");
  });
});
