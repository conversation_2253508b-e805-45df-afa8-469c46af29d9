/* eslint-disable max-len, max-lines-per-function, functional/no-return-void, import/no-unused-modules, newline-per-chained-call, functional/immutable-data, check-file/folder-match-with-fex */
import nock from "nock";
import fetch from "cross-fetch";

import { OrdersOnProducts } from "@prisma/client";

import Order from "../../lib/order";
import type { Invalid } from "../../lib/order";

import { PrintAttributesInput, Color } from "../../api/graphql/types";

global.fetch = fetch;

describe("missingLogoUrls", () => {
  const host = "http://snapraiselogos.s3.us-west-1.amazonaws.com.test";
  const logos: Array<[string, number]> = [
    ["/MyFolder/UuwUwpbCgK.png", 200],
    ["/MyFolder/UuwUwpbCgK.png", 200],
    ["/MyFolder/SvvaVPhpnJ.png", 404],
    ["/MyFolder/SvvaVPhpnJ.png", 404],
    ["/MyFolder/YGXONfTggc.png", 200],
    ["/MyFolder/YGXONfTggc.png", 200]
  ];

  beforeAll(() => {
    logos.forEach(([logo, status]) => {
      nock(host).get(logo).reply(status);
    });
  });

  it("should return missing logos", async () => {
    const products: Array<OrdersOnProducts> = logos.map(([logo]) => ({
      id: "12345678",
      backLogo: "test",
      orderId: "0987654321",
      productId: "",
      printAttributes: null,
      createdAt: new Date(),
      updatedAt: new Date(),
      logo: host + logo,
      netsuiteId: "977f0cd2-5e82-4332-9705-2a0c61f019ff",
      receiverName: "Jack Larkin",
      amount: 1
    }));

    const expectedMissingLogoUrls: Array<string> = [...new Set(logos.filter(([logo, status]) => status === 404).map(([logo]) => host + logo))];
    const computedMissingLogoUrls = await Order.missingLogoUrls(products);

    expect(computedMissingLogoUrls.length).toEqual(expectedMissingLogoUrls.length);
    expect(computedMissingLogoUrls).toEqual(expect.arrayContaining(expectedMissingLogoUrls));
  });

  it("should return empty array", async () => {
    const products: Array<OrdersOnProducts> = Array(3).fill({
      logo: "",
      netsuiteId: "977f0cd2-5e82-4332-9705-2a0c61f019ff",
      receiverName: "Jack Larkin"
    });

    const computedMissingLogoUrls = await Order.missingLogoUrls(products);

    expect(computedMissingLogoUrls.length).toEqual(0);
  });
});

describe("validatePrintAttributes", () => {
  const products = (printAttributes: PrintAttributesInput) => ([{
    id: "12345678",
    backLogo: "test",
    orderId: "0987654321",
    productId: "",
    printAttributes: printAttributes,
    createdAt: new Date(),
    updatedAt: new Date(),
    logo: "",
    netsuiteId: "977f0cd2-5e82-4332-9705-2a0c62f019ff",
    receiverName: "Jack Barkin",
    amount: 1
  }]);

  it("should return true", async () => {
    expect(Order.validatePrintAttributes([])).toEqual({ valid: true });
    expect(Order.validatePrintAttributes(products(null))).toEqual({ valid: true });

    expect(Order.validatePrintAttributes(products({ name: { value: "Lorem" } }))).toEqual({ valid: true });
    expect(Order.validatePrintAttributes(products({ name: { value: "Lorem", color: "WHITE" as Color } }))).toEqual({ valid: true });

    expect(Order.validatePrintAttributes(products({ number: { value: "99" } }))).toEqual({ valid: true });
    expect(Order.validatePrintAttributes(products({ number: { value: "99", color: "BLACK" as Color } }))).toEqual({ valid: true });
  });

  it("should return false", async () => {
    const v1 = Order.validatePrintAttributes(products({ name: { value: "Loremipsum0987" } }));
    expect(v1.valid).toEqual(false);
    expect((v1 as Invalid).errors).toEqual(expect.arrayContaining(["Name is too long."]));

    const v2 = Order.validatePrintAttributes(products({ number: { value: "111" } }));
    expect(v2.valid).toEqual(false);
    expect((v2 as Invalid).errors).toEqual(expect.arrayContaining(["Number is too long."]));

    const v3 = Order.validatePrintAttributes(products({ name: { value: "Lorem", color: "RED" as Color } }));
    expect(v3.valid).toEqual(false);
    expect((v3 as Invalid).errors).toEqual(expect.arrayContaining(["Color is not supported."]));
  });
});
