/* eslint-disable functional/no-return-void, import/no-unused-modules, check-file/folder-match-with-fex, max-lines-per-function, functional/immutable-data, functional/no-let, max-len, import/no-relative-parent-imports, import/no-internal-modules, id-denylist, sonarjs/no-duplicate-string */
import Klaviyo from "../../lib/klaviyo";
import KlaviyoCreateProfileResponse from "../mocks/klaviyo-create-profile-response.json";
import KlaviyoCreateSubscriptionResponse from "../mocks/klaviyo-create-subscription-response.json";
import KlaviyoFindProfileResponse from "../mocks/klaviyo-find-profile-response.json";
import KlaviyoCreateOrUpdateProfileResponse from "../mocks/klaviyo-create-or-update-profile-response.json";

describe("Klaviyo", () => {
  let originalFetch: typeof global.fetch;
  beforeAll(() => { originalFetch = global.fetch; });
  afterEach(() => { global.fetch = originalFetch; });

  describe("findProfile", () => {
    it("returns a Klaviyo profile", async () => {
      jest.spyOn(global, "fetch").mockResolvedValue({ ok: true, status: 200, json: async () => KlaviyoFindProfileResponse } as Response);
      const search = await Klaviyo.findProfile("<EMAIL>");
      expect(search).toEqual(KlaviyoFindProfileResponse.data[0]);
    });

    it("does not return a Klaviyo profile for a missing email", async () => {
      jest.spyOn(global, "fetch").mockResolvedValue({ ok: true, status: 200, json: async () => ({ data: [], links: {} }) } as Response);
      const search = await Klaviyo.findProfile("<EMAIL>");
      expect(search).toEqual(null);
    });
  });

  describe("createProfile", () => {
    const store = {
      groupLeaderEmail: "<EMAIL>",
      groupLeader: "Alice Smith",
      name: "Example Store",
      fundraiserId: 67890,
      fundraiserEntityId: 67890,
      createdAt: new Date("2023-02-01"),
      storeUrl: "https://examplestore.com",
      logoDigitalUrl: "https://examplestore.com/logo.png",
      startDate: new Date("2024-02-01"),
      endDate: new Date("2024-11-30"),
      state: "NY",
      zip: "67890",
      groupName: "Example Program",
      activityType: "Example Activity",
      city: "Example City",
      organizationName: "Example Organization"
    };

    it("creates and returns a Klaviyo profile", async () => {
      jest.spyOn(global, "fetch").mockResolvedValue({ ok: true, status: 200, json: async () => KlaviyoCreateProfileResponse } as Response);
      const profile = await Klaviyo.createProfile(store);
      expect(profile).toEqual(KlaviyoCreateProfileResponse.data);
    });

    it("specifies whether the profile already exists", async () => {
      jest.spyOn(global, "fetch").mockResolvedValue({ ok: false, status: 409, json: async () => ({ errors: [{ status: "409", title: "Conflict" }] }) } as Response);
      const profile = await Klaviyo.createProfile(store);
      expect(profile).toEqual({ exists: true });
    });
  });

  describe("createOrUpdateProfile", () => {
    const store = {
      groupLeaderEmail: "<EMAIL>",
      groupLeader: "Alice Smith",
      name: "Example Store",
      fundraiserId: 67890,
      fundraiserEntityId: 67890,
      createdAt: new Date("2023-02-01"),
      storeUrl: "https://examplestore.com",
      logoDigitalUrl: "https://examplestore.com/logo.png",
      startDate: new Date("2024-02-01"),
      endDate: new Date("2024-11-30"),
      state: "NY",
      zip: "67890",
      groupName: "Example Program",
      activityType: "Example Activity",
      city: "Example City",
      organizationName: "Example Organization"
    };

    it("upserts and returns a Klaviyo profile", async () => {
      jest.spyOn(global, "fetch").mockResolvedValue({ ok: true, status: 200, json: async () => KlaviyoCreateOrUpdateProfileResponse } as Response);
      const profile = await Klaviyo.createOrUpdateProfile(store);
      expect(profile).toEqual(KlaviyoCreateOrUpdateProfileResponse.data);
    });
  });

  describe("createSubscription", () => {
    it("creates a subscription and returns an empty object", async () => {
      jest.spyOn(global, "fetch").mockResolvedValue({ ok: true, status: 202, json: async () => ("Invalid JSON") } as Response);
      const result = await Klaviyo.createSubscription("<EMAIL>", "01J9A4F7B802W47Y2RPPRH614W");
      expect(result).toEqual({ accepted: true });
    });
  });

  describe("findUserSubscription", () => {
    it("finds a user subscription if it exists", async () => {
      jest.spyOn(global, "fetch").mockResolvedValue({ ok: true, status: 200, json: async () => KlaviyoCreateSubscriptionResponse } as Response);
      const result = await Klaviyo.findUserSubscription("<EMAIL>");
      expect(result).toEqual(KlaviyoCreateSubscriptionResponse.data[0]);
    });

    it("returns null if a user subscription does not exist", async () => {
      const notFoundResponse = { data: [], links: {} };
      jest.spyOn(global, "fetch").mockResolvedValue({ ok: true, status: 200, json: async () => notFoundResponse } as Response);
      const result = await Klaviyo.findUserSubscription("<EMAIL>");
      expect(result).toEqual(null);
    });
  });
});
