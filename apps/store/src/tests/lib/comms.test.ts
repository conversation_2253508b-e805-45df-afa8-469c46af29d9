/* eslint-disable functional/no-return-void, import/no-unused-modules, check-file/folder-match-with-fex, max-lines-per-function, functional/immutable-data, functional/no-let, max-len, import/no-relative-parent-imports, import/no-internal-modules, id-denylist */
import Comms from "../../lib/comms";
import CommsSendEmailResponse from "../mocks/comms-send-email-response.json";

describe("Comms", () => {
  let originalFetch: typeof global.fetch;
  beforeAll(() => { originalFetch = global.fetch; });
  afterEach(() => { global.fetch = originalFetch; });

  describe("sendEmail", () => {
    it("send an email", async () => {
      jest.spyOn(global, "fetch").mockResolvedValue({ ok: true, status: 200, json: async () => CommsSendEmailResponse } as Response);
      const send = await Comms.sendEmail("store_build_ready", [{ to: "joseph.ravenwolf<PERSON>@snapraise.com" }], { storeUrl: "https://demo.snap.store" });
      expect(send).toEqual(CommsSendEmailResponse.data.commsSend);
    });
  });
});
