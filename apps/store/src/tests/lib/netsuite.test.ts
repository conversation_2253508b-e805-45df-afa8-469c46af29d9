/* eslint-disable max-lines-per-function */
/* eslint-disable max-lines */
/* eslint-disable functional/no-return-void, sonarjs/no-duplicate-string, import/no-unused-modules, check-file/folder-match-with-fex */
import Prisma from "@prisma/client";
import Netsuite, { NetsuiteOrder } from "@store-monorepo/netsuite";
import netsuite from "../../lib/netsuite";
import { OrderInput, ShippingProvider, Vendor } from "../../api/graphql/types";

jest.mock("@store-monorepo/netsuite", () => ({
  SalesOrder: {
    create: jest.fn().mockResolvedValue("12345"),
    createItemFulfillment: jest.fn()
  },
  DEPARTMENT_ID: 1,
  SUBSIDIARY_ID: 2,
  CLASS_ID: 3,
  ORDER_TYPE: 4,
  WORKFLOW_STAGE: {
    NEW_ORDER: 5,
  },
  CUSTOM_FORM: {
    ORDER: 126,
    DEPOSIT: 134
  },
  PAYMENT_METHOD: {
    CREDIT_CARD: "credit_card",
    PAYPAL: "paypal",
  },
  DISCOUNT_ITEM_ID: 6,
  TAX_ITEM_ID: 7,
  SHIPPING_ITEM_ID: 8,
}));

describe("netsuite", () => {
  describe("orderUrl", () => {
    it("should return the correct URL", () => {
      const netsuiteId = 67890;
      const expectedUrl = "https://test_app_id.app.netsuite.com/app/accounting/transactions/salesord.nl?id=67890&whence=";
      expect(netsuite.orderUrl(String(netsuiteId))).toBe(expectedUrl);
    });
  });

  describe("updateNetsuiteTracking", () => {
    it("should successfully update tracking information", async () => {
      const mockOrder = { id: "123", trackingNumber: "1Z999AA1234567890" } as Prisma.Order;

      await netsuite.updateNetsuiteTracking(mockOrder);

      expect(Netsuite.SalesOrder.createItemFulfillment).toHaveBeenCalledWith(mockOrder);
    });

    it("should throw an error if createItemFulfillment fails", async () => {
      const mockOrder = { id: "123", trackingNumber: "1Z999AA1234567890" } as Prisma.Order;
      (Netsuite.SalesOrder.createItemFulfillment as jest.Mock).mockRejectedValue(new Error("Failed to update tracking"));

      await expect(netsuite.updateNetsuiteTracking(mockOrder)).rejects.toThrow("Failed to update tracking");
    });
  });
});
