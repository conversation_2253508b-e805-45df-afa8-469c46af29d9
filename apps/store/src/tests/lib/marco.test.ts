/* eslint-disable functional/no-return-void, import/no-unused-modules */
import <PERSON> from "../../lib/marco";
import { MarcoEventInput, MarcoOrderStatus, ShippingProvider } from "../../api/graphql/types";

const EVENT: MarcoEventInput = {
  id: 8116900,
  purchase_order: "",
  status: MarcoOrderStatus.Shipped,
  shipments: [
    {
      shipping_carrier: ShippingProvider.FedEx,
      tracking_number: "**********"
    }
  ]
};

describe("<PERSON>", () => {
  describe("getOrderUpdateInput", () => {
    it("should return carrier and tracking number", () => {
      const update = Marco.getOrderUpdateInput(EVENT);

      expect(update).toEqual({
        carrier: ShippingProvider.FedEx,
        trackingNumber: "**********",
      });
    });
  });
});
