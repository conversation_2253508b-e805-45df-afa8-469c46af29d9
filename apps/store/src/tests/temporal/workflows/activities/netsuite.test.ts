/* eslint-disable max-lines-per-function */
/* eslint-disable max-lines */
/* eslint-disable functional/no-return-void, sonarjs/no-duplicate-string, import/no-unused-modules, check-file/folder-match-with-fex */
import Netsuite, { NetsuiteOrder, NetsuiteCustomerDeposit } from "@store-monorepo/netsuite";
import { OrderInput, ShippingProvider, Vendor } from "../../../../api/graphql/types";
import netsuite from "../../../../temporal/workflows/activities/netsuite";

jest.mock("@store-monorepo/netsuite", () => ({
  SalesOrder: {
    create: jest.fn().mockResolvedValue("12345"),
    createItemFulfillment: jest.fn(),
    getClosedSalesOrders: jest.fn()
  },
  CustomerDeposit: {
    create: jest.fn().mockResolvedValue("deposit123")
  },
  DEPARTMENT_ID: 1,
  SU<PERSON>IDIARY_ID: 2,
  CLASS_ID: 3,
  ORDER_TYPE: 4,
  WORKFLOW_STAGE: {
    NEW_ORDER: 5,
  },
  CUSTOM_FORM: {
    ORDER: 126,
    DEPOSIT: 134
  },
  CUSTOMER_ID: 9,
  PAYMENT_METHOD: {
    PAYPAL: 9,
    STRIPE: 10
  },
  DISCOUNT_ITEM_ID: 6,
  TAX_ITEM_ID: 7,
  SHIPPING_ITEM_ID: 8,
  USD_CURRENCY_ID: 10,
  ACCOUNTS: {
    STRIPE: 11,
    CREDIT_CARD: 12,
    PAYPAL: 13
  }
}));

describe("netsuite", () => {
  describe("orderUrl", () => {
    it("should return the correct URL", () => {
      const netsuiteId = 67890;
      const expectedUrl = "https://test_app_id.app.netsuite.com/app/accounting/transactions/salesord.nl?id=67890&whence=";
      expect(netsuite.orderUrl(String(netsuiteId))).toBe(expectedUrl);
    });
  });

  describe("createNetsuiteOrder", () => {
    it("should call Netsuite.SalesOrder.create with the correct order", async () => {
      const mockOrder = {
        entity: { id: 12345 },
        department: { id: 1 },
        subsidiary: { id: 2 },
        class: { id: 3 },
        custbody_order_type: 4,
        email: "<EMAIL>",
        custbody_primary_contact_name: "John Doe",
        custbody_primary_contact_email: "<EMAIL>",
        custbody_primary_contact_phone: "**********",
        item: { items: [] },
        shippingaddress: {
          addressee: "John Doe",
          addr1: "123 Main St",
          addr2: "",
          city: "Anytown",
          state: "CA",
          zip: "12345",
          country: "US",
        },
        externalId: "SO_SnapV41234",
        billingaddress: {
          addressee: "John Doe",
          addr1: "456 Billing St",
          addr2: "Suite 7",
          city: "Billtown",
          state: "NY",
          zip: "67890",
          country: "US",
        },
        custbody_shipment_workflow_stage: 5,
      };

      const netsuiteId = await netsuite.createNetsuiteOrder(mockOrder);
      expect(Netsuite.SalesOrder.create).toHaveBeenCalledWith(mockOrder);
      expect(netsuiteId).toBe("12345");
    });

    it("should throw an error if Netsuite.SalesOrder.create fails", async () => {
      (Netsuite.SalesOrder.create as jest.Mock).mockRejectedValue(new Error("Failed to create Netsuite order"));
      await expect(netsuite.createNetsuiteOrder({ externalId: "SO_SnapV41234" } as NetsuiteOrder)).rejects.toThrow("Failed to create Netsuite order");
    });
  });

  describe("prepareNetsuiteOrder", () => {
    it("should prepare a NetsuiteOrder from an OrderInput", async () => {
      const mockOrderInput: OrderInput = {
        shipTo: "John Doe",
        shipToEmail: "<EMAIL>",
        shipToPhone: "**********",
        street: "123 Main St",
        street2: "Apt 4",
        city: "Anytown",
        state: "CA",
        zipCode: "12345",
        billingStreet: "456 Billing St",
        billingStreet2: "Suite 7",
        billingCity: "Billtown",
        billingState: "NY",
        billingZipCode: "67890",
        packingSlipId: "PS001",
        packingSlipTitle: "Packing Slip",
        carrier: ShippingProvider.Ups,
        vendor: Vendor.Marco,
        orderId: "12345",
        paymentMethod: "STRIPE",
        confirmationId: "CONF123",
        transactionId: "TXN123",
        products: [
          { netsuiteId: "1001", name: "Product 1", logo: "logo1", receiverName: "receiver1", amount: 10, sku: "SKU123" },
          { netsuiteId: "1002", name: "Product 2", logo: "logo2", receiverName: "receiver2", amount: 15, sku: "SKU456" },
        ],
      };

      const result = await netsuite.prepareNetsuiteOrder(mockOrderInput);

      expect(result).toEqual({
        billaddresslist: null,
        customform: {
          id: 126
        },
        entity: { id: parseInt(process.env.NETSUITE_CUSTOMER_ID as string) },
        department: { id: Netsuite.DEPARTMENT_ID },
        subsidiary: { id: Netsuite.SUBSIDIARY_ID },
        class: { id: Netsuite.CLASS_ID },
        custbody_order_type: Netsuite.ORDER_TYPE,
        custbody_fundraiser_id: undefined,
        email: "<EMAIL>",
        custbody_primary_contact_name: "John Doe",
        custbody_primary_contact_email: "<EMAIL>",
        custbody_primary_contact_phone: "**********",
        custbody_shipment_workflow_stage: Netsuite.WORKFLOW_STAGE.NEW_ORDER,
        custbodycustbodypayment_method: 10,
        custbodydesign_details_store: undefined,
        custbodyorder_confirmation: "CONF123",
        custbodytxn_id: "TXN123",
        externalId: "SO_SnapV412345",
        item: {
          items: [
            { item: { id: 1001 }, rate: 10, quantity: 1, custcol_sku: "SKU123", custcolcust_col_logourl: "logo1", custcol_recipient_name: "John Doe", custcol_recipient_email: "<EMAIL>" },
            { item: { id: 1002 }, rate: 15, quantity: 1, custcol_sku: "SKU456", custcolcust_col_logourl: "logo2", custcol_recipient_name: "John Doe", custcol_recipient_email: "<EMAIL>" }
          ],
        },
        shippingaddress: {
          addressee: "John Doe",
          addr1: "123 Main St",
          addr2: "Apt 4",
          city: "Anytown",
          state: "CA",
          zip: "12345",
          country: "US",
        },
        billingaddress: {
          addressee: "John Doe",
          addr1: "456 Billing St",
          addr2: "Suite 7",
          city: "Billtown",
          state: "NY",
          zip: "67890",
          country: "US",
        },
      });
    });
  });

  describe("customerDepositParams", () => {
    it("should prepare customer deposit parameters correctly", async () => {
      const mockOrder: OrderInput = {
        orderId: "12345",
        paymentMethod: "STRIPE",
        transactionId: "TXN123",
        baseSubtotal: 1.00,
        products: [
          { netsuiteId: "1001", amount: 10, quantity: 2 },
          { netsuiteId: "1002", amount: 15, quantity: 1 },
        ],
      } as OrderInput;

      const mockNsOrder: NetsuiteOrder = {
        item: {
          items: [
            { item: { id: 1001 }, rate: 10, quantity: 1 },
            { item: { id: 1002 }, rate: 15, quantity: 1 },
          ],
        },
      } as NetsuiteOrder;

      const result = await netsuite.customerDepositParams(mockNsOrder, mockOrder, mockOrder.baseSubtotal);
      expect(result).toEqual({
        customer: { id: 9 },
        currency: { id: 10 },
        salesOrder: { externalId: "SO_SnapV412345" },
        externalId: "CD_SnapV412345",
        account: { id: 11 },
        class: { id: 3 },
        payment: 1.00,
        customForm: { id: 134 },
        custbodycustbodypayment_method: 10,
        department: { id: 1 },
        custbodytxn_id: "TXN123",
      });
    });
  });

  describe("createCustomerDeposit", () => {
    it("should create a customer deposit successfully", async () => {
      const mockDepositParams: NetsuiteCustomerDeposit = {
        customer: { id: 9 },
        currency: { id: 10 },
        salesOrder: { externalId: "SO_SnapV412345" },
        externalId: "CD_SnapV412345",
        account: { id: 11 },
        class: { id: 3 },
        payment: 100,
        customForm: { id: 134 },
        custbodycustbodypayment_method: 10,
        department: { id: 1 },
        custbodytxn_id: "TXN123",
      };

      await netsuite.createCustomerDeposit(mockDepositParams);
      expect(Netsuite.CustomerDeposit.create).toHaveBeenCalledWith(mockDepositParams);
    });

    it("should throw an error if creating customer deposit fails", async () => {
      (Netsuite.CustomerDeposit.create as jest.Mock).mockRejectedValue(new Error("Failed to create customer deposit"));
      await expect(netsuite.createCustomerDeposit({} as NetsuiteCustomerDeposit)).rejects.toThrow("Failed to create customer deposit");
    });
  });
  describe("getClosedSalesOrders", () => {
    it("retrieves up to n salesorder transactions", async () => {
      (Netsuite.SalesOrder.getClosedSalesOrders as jest.Mock).mockReturnValue({ body: { items: [] } });
      const result = await netsuite.getClosedSalesOrders(3);

      expect(result).toEqual([]);
    });
  });
});
