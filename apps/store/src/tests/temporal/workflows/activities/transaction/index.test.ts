/* eslint-disable sonarjs/no-duplicate-string */
/* eslint-disable
check-file/folder-match-with-fex, import/no-unused-modules,
max-lines-per-function, functional/no-return-void */
import { MagentoConsumerPointsLiability, MagentoRefundPointsLiability } from "@store-monorepo/magento-api";
import MagentoToNetsuite from "../../../../../temporal/workflows/activities/transaction/dto";
import pointsTransactionActivities from "../../../../../temporal/workflows/activities/transaction";

jest.mock("../../../../../temporal/workflows/activities/transaction/dto");

describe("Points Transaction Activities", () => {
  const createMockParams = (transactionType: string, comment: string|undefined =undefined): MagentoConsumerPointsLiability => ({
    transactionId: 123,
    transactionType,
    customerId: 456,
    pointsDelta: 100,
    netsuiteExternalId: "Snap123",
    netsuiteInternalId: 123,
    comment,
    orderId: 789
  });

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe("processPointsByTransactionType", () => {
    afterEach(() => {
      (MagentoToNetsuite.recordEarnedPoints as jest.Mock).mockReset();
    });
    it("should handle earned points", async () => {
      const mockParams = createMockParams("Earned");
      (MagentoToNetsuite.shouldHandleEarnedPoints as jest.Mock).mockReturnValue(true);
      (MagentoToNetsuite.recordEarnedPoints as jest.Mock).mockResolvedValue({ netsuiteInternalId: mockParams.netsuiteInternalId, netsuiteExternalId: mockParams.netsuiteExternalId });

      await pointsTransactionActivities.processPointsByTransactionType(mockParams);
      expect(MagentoToNetsuite.shouldHandleEarnedPoints).toHaveBeenCalledWith(mockParams);
      expect(MagentoToNetsuite.recordEarnedPoints).toHaveBeenCalledWith(mockParams);
    });

    it("should handle bought points", async () => {
      const mockParams = createMockParams("Dashboard_Bought", "buy-points");
      (MagentoToNetsuite.shouldHandlePointsBoughtCD as jest.Mock).mockReturnValue(true);
      (MagentoToNetsuite.recordBoughtPoints as jest.Mock).mockResolvedValue({ netsuiteInternalId: mockParams.netsuiteInternalId, netsuiteExternalId: mockParams.netsuiteExternalId });

      await pointsTransactionActivities.processPointsByTransactionType(mockParams);
      expect(MagentoToNetsuite.recordBoughtPoints).toHaveBeenCalled();
    });

    it("should handle redeem points", async () => {
      const mockParams = createMockParams("Redeem");
      (MagentoToNetsuite.shouldHandleRedeemedOrExpired as jest.Mock).mockReturnValue(true);

      await pointsTransactionActivities.processPointsByTransactionType(mockParams);

      expect(MagentoToNetsuite.recordRedeemedOrExpiredPoints).toHaveBeenCalledWith(mockParams);
    });

    it("should handle dashboard bought points", async () => {
      const mockParams = createMockParams("Dashboard_Bought", "buy-points");
      (MagentoToNetsuite.shouldHandlePointsBoughtCD as jest.Mock).mockReturnValue(true);
      (MagentoToNetsuite.recordBoughtPoints as jest.Mock).mockResolvedValue({ netsuiteInternalId: mockParams.netsuiteInternalId, netsuiteExternalId: mockParams.netsuiteExternalId });
      await pointsTransactionActivities.processPointsByTransactionType(mockParams);
      expect(MagentoToNetsuite.recordBoughtPoints as jest.Mock).toHaveBeenCalled();
    });

    it("should handle order failure restore", async () => {
      const mockParams = createMockParams("Earned");
      (MagentoToNetsuite.shouldHandleOrderFailure as jest.Mock).mockReturnValue(true);

      await pointsTransactionActivities.processPointsByTransactionType(mockParams);

      expect(MagentoToNetsuite.recordOrderFailureRestore).toHaveBeenCalledWith(mockParams);
    });
  });

  describe("processPointsTransactionTransfer", () => {
    it("should handle transfer points", async () => {
      const mockParams = createMockParams("Transfer");
      (MagentoToNetsuite.shouldHandleTransfer as jest.Mock).mockReturnValue(true);
      (MagentoToNetsuite.recordTransferPoints as jest.Mock).mockResolvedValue({
        netsuiteInternalId: "mock-internal-id",
        netsuiteExternalId: "mock-external-id",
      });

      await pointsTransactionActivities.processPointsTransactionTransfer(mockParams);

      expect(MagentoToNetsuite.recordTransferPoints).toHaveBeenCalledWith(mockParams);
      expect(MagentoToNetsuite.updateMagentoPointsTransaction).toHaveBeenCalledWith({
        transactionId: mockParams.transactionId,
        netsuiteInternalId: "mock-internal-id",
        netsuiteExternalId: "mock-external-id",
        transactionType: mockParams.transactionType,
      });
    });

    it("should not handle transfer if shouldHandleTransfer returns false", async () => {
      const mockParams = createMockParams("Transfer");
      (MagentoToNetsuite.shouldHandleTransfer as jest.Mock).mockReturnValue(false);

      await pointsTransactionActivities.processPointsTransactionTransfer(mockParams);

      expect(MagentoToNetsuite.recordTransferPoints).not.toHaveBeenCalled();
      expect(MagentoToNetsuite.updateMagentoPointsTransaction).not.toHaveBeenCalled();
    });
  });

  describe("processRefundPointsTransaction", () => {
    const createMockRefundParams = (): MagentoRefundPointsLiability => ({
      status: "billed",
      amount: 100,
      orderId: 789,
      netsuiteOrderId: 123,
    });

    it("should handle billed refund", async () => {
      const mockParams = createMockRefundParams();
      (MagentoToNetsuite.hasOrderShipped as jest.Mock).mockResolvedValue(true);
      (MagentoToNetsuite.hasOrderShipmentTracking as jest.Mock).mockResolvedValue(true);

      await pointsTransactionActivities.processRefundPointsTransaction(mockParams);

      expect(MagentoToNetsuite.recordBilledRefundPoints).toHaveBeenCalledWith(mockParams);
    });
  });
});
