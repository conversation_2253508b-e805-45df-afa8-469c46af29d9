/* eslint-disable max-lines, max-lines-per-function,
check-file/folder-match-with-fex, @typescript-eslint/no-unused-vars, import/no-unused-modules, functional/no-return-void */
import Netsuite from "@store-monorepo/netsuite";
import { MagentoConsumerPointsLiability } from "@store-monorepo/magento-api";
import serializer from "../../../../../temporal/workflows/activities/transaction/serializer";

jest.mock("@store-monorepo/netsuite", () => ({
  ACCOUNTS: {
    POINTS_LIABILITY: 123,
    POINTS_DEFERRED: 456,
    REFUND_LIABILITY: 789,
    REFUND_DEFERRED: 101
  },
  CUSTOMER_ID: 789,
  USD_CURRENCY_ID: 101,
  CLASS_ID: 202,
  DEPARTMENT_ID: 303,
  PAYMENT_METHOD: {
    STRIPE: 10
  },
  SUBSIDIARY_ID: 404,
  CUSTOM_FORM: {
    ORDER: 126,
    DEPOSIT: 134
  }
}));

describe("Points Transaction Serializers", () => {
  describe("generateOrderFailureRestoreSerializer", () => {
    it("should generate correct NetsuiteJournalEntry for order failure restore", () => {
      const transactionId = 123;
      const points = 100;

      const result = serializer.generateOrderFailureRestore(transactionId, points);

      expect(result).toEqual({
        subsidiary: { id: Netsuite.SUBSIDIARY_ID },
        externalId: `JE-Points_Restore-${transactionId}`,
        line: {
          items: [
            {
              account: { id: Netsuite.ACCOUNTS.POINTS_LIABILITY },
              debit: points,
              memo: "$100 transferred back to points deferred account due to order failure restore"
            },
            {
              account: { id: Netsuite.ACCOUNTS.POINTS_DEFERRED },
              credit: points,
              memo: "$100 transferred back to points deferred account due to order failure restore"
            }
          ]
        },
        memo: "When points are restored"
      });
    });
  });
  describe("generatePointsEarnedSerializer", () => {
    it("should generate correct NetsuiteCustomerDeposit for points earned", () => {
      const params: MagentoConsumerPointsLiability = {
        transactionId: 123,
        paymentMethod: "STRIPE",
        orderId: 12345,
        pointsDelta: 50,
        pointsBalance: 100,
        organizationId: 123,
        paymentEntityId: 456,
        customerId: 1,
        transactionType: "earned",
      };

      const result = serializer.generatePointsEarned(params);

      expect(result).toEqual({
        customer: { id: Netsuite.CUSTOMER_ID },
        currency: { id: Netsuite.USD_CURRENCY_ID },
        customForm: { id: Netsuite.CUSTOM_FORM.DEPOSIT },
        salesOrder: { externalId: `SO_SnapV4${params.orderId}` },
        paymentOption: { id: Netsuite.PAYMENT_METHOD.STRIPE },
        externalId: `Store Points - Customer Deposit: ${params.orderId}`,
        account: { id: Netsuite.ACCOUNTS.STRIPE },
        class: { id: Netsuite.CLASS_ID },
        payment: params.pointsDelta,
        custbodycustbodypayment_method: Netsuite.PAYMENT_METHOD.STRIPE,
        custbodypayment_method: 10,
        department: { id: Netsuite.DEPARTMENT_ID },
        custbodyorg_id: params.organizationId,
        custbodytxn_id: `${params.paymentEntityId}`,
        custbodyactive_points: params.pointsDelta,
        custbodytotal_points: params.pointsBalance,
      });
    });
  });
  describe("generatePointsBoughtSerializer", () => {
    it("should generate correct NetsuiteCustomerDeposit for points bought", () => {
      const params: MagentoConsumerPointsLiability = {
        customerId: 1,
        transactionId: 456,
        pointsDelta: 100,
        pointsBalance: 100,
        transactionType: "earned"
      };

      const result = serializer.generatePointsBought(params);

      expect(result).toEqual({
        customer: { id: Netsuite.CUSTOMER_ID },
        currency: { id: Netsuite.USD_CURRENCY_ID },
        customForm: { id: Netsuite.CUSTOM_FORM.DEPOSIT },
        externalId: `${params.customerId}_${params.transactionId}`,
        account: { id: Netsuite.ACCOUNTS.STRIPE },
        class: { id: Netsuite.CLASS_ID },
        payment: params.pointsDelta,
        custbodyactive_points: params.pointsDelta,
        custbodytotal_points: params.pointsDelta,
      });
    });
  });
  describe("generatePointsBoughtJournalEntrySerializer", () => {
    it("should generate correct NetsuiteJournalEntry for points bought", () => {
      const params: MagentoConsumerPointsLiability = {
        transactionId: 789,
        pointsDelta: 75,
        customerId: 1,
        transactionType: "earned"
      };

      const result = serializer.generatePointsBoughtJournalEntry(params);

      expect(result).toEqual({
        externalId: `JE-Points_Migration-${params.transactionId}`,
        subsidiary: { id: Netsuite.SUBSIDIARY_ID },
        line: {
          items: [
            {
              account: { id: Netsuite.ACCOUNTS.STRIPE },
              debit: params.pointsDelta,
              memo: `${params.pointsDelta} points being transferred to Points Deferred Account`
            },
            {
              account: { id: Netsuite.ACCOUNTS.POINTS_DEFERRED },
              credit: params.pointsDelta,
              memo: `${params.pointsDelta} points being transferred to Points Deferred Account`
            }
          ]
        },
        memo: "Amount is transferred from Stripe to Points Deferred Account"
      });
    });
  });
  describe("generatePointsTransferSerializer", () => {
    it("should generate correct NetsuiteCustomerDeposit for points transfer", () => {
      const params: MagentoConsumerPointsLiability = {
        customerId: 1,
        transactionId: 101,
        pointsDelta: 50,
        organizationId: 456,
        transactionType: "earned"
      };

      const result = serializer.generatePointsTransfer(params);

      expect(result).toEqual({
        customer: { id: Netsuite.CUSTOMER_ID },
        currency: { id: Netsuite.USD_CURRENCY_ID },
        customForm: { id: Netsuite.CUSTOM_FORM.DEPOSIT },
        externalId: `${params.customerId}_${params.transactionId}`,
        account: { id: Netsuite.ACCOUNTS.STRIPE },
        class: { id: Netsuite.CLASS_ID },
        department: { id: Netsuite.DEPARTMENT_ID },
        custbodyorg_id: params.organizationId,
        payment: params.pointsDelta,
      });
    });
  });
  describe("generateRedeemedOrExpiredPointsSerializer", () => {
    it("should generate correct NetsuiteJournalEntry for redeemed points", () => {
      const params: MagentoConsumerPointsLiability = {
        transactionId: 101,
        pointsDelta: 30,
        comment: "order_placed_spend",
        customerId: 1,
        transactionType: "earned"
      };

      const result = serializer.generateRedeemedOrExpiredPoints(params);

      expect(result).toEqual({
        subsidiary: { id: Netsuite.SUBSIDIARY_ID },
        externalId: `JE-Points_used-${params.transactionId}`,
        line: {
          items: [
            {
              account: { id: Netsuite.ACCOUNTS.POINTS_DEFERRED },
              debit: params.pointsDelta,
              memo: `${params.pointsDelta}$ transferred to store products deferred account. Points were used for this purchase`
            },
            {
              account: { id: Netsuite.ACCOUNTS.POINTS_LIABILITY },
              credit: params.pointsDelta,
              memo: `${params.pointsDelta}$ credited to Product Income account from points store as points were used for this purchase`
            }
          ]
        },
        memo: "When points are used and order is not shipped yet"
      });
    });

    it("should generate correct NetsuiteJournalEntry for expired points", () => {
      const params: MagentoConsumerPointsLiability = {
        transactionId: 102,
        pointsDelta: 20,
        comment: "points_expired",
        customerId: 1,
        transactionType: "earned"
      };

      const result = serializer.generateRedeemedOrExpiredPoints(params);

      expect(result).toEqual({
        subsidiary: { id: Netsuite.SUBSIDIARY_ID },
        externalId: `Point_Expiry${params.transactionId}`,
        line: {
          items: [
            {
              account: { id: Netsuite.ACCOUNTS.POINTS_DEFERRED },
              debit: params.pointsDelta,
              memo: `${params.pointsDelta} points expired`
            },
            {
              account: { id: Netsuite.ACCOUNTS.POINTS_LIABILITY },
              credit: params.pointsDelta,
              memo: `${params.pointsDelta} points expired and credited to cash account`
            }
          ]
        },
        memo: `${params.pointsDelta} Points are expired`
      });
    });
  });

  describe("generateBilledRefundPointsSerializer", () => {
    it("should generate correct NetsuiteJournalEntry for billed refund points", () => {
      const params = {
        orderId: 12345,
        amount: 50,
        status: "billed",
        transactionId: 123,
        netsuiteOrderId: 123,
        netsuiteExternalId: "mock-external-id",
        eventCode: "mock-event-code",
        comment: "mock-comment",
      };

      const result = serializer.generateBilledRefundPoints(params);

      expect(result).toEqual({
        subsidiary: { id: Netsuite.SUBSIDIARY_ID },
        externalId: `JE-Refund_SnapV4${params.orderId}`,
        line: {
          items: [
            {
              account: { id: Netsuite.ACCOUNTS.POINTS_LIABILITY },
              debit: params.amount,
              memo: `${params.amount} debited from cash account due to refund against order: ${params.orderId}`
            },
            {
              account: { id: Netsuite.ACCOUNTS.REFUND_DEFERRED },
              credit: params.amount,
              memo: `${params.amount} credited to refunds of product account against order: ${params.orderId}`
            }
          ]
        },
        memo: "Refund has occurred"
      });
    });
  });
});
