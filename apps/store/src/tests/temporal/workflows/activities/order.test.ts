/* eslint-disable max-len, max-lines, functional/immutable-data, import/no-unused-modules, max-lines-per-function, check-file/folder-match-with-fex, functional/no-return-void */
import { Order as PrismaOrder, OrdersOnProducts, Product, STATE, STATUS } from "@prisma/client";
import Repo from "@store-monorepo/repo";
import MarcoAP<PERSON> from "@store-monorepo/marco-api";
import Activities from "../../../../temporal/workflows/activities/order";
import { ShippingProvider, Vendor } from "../../../../api/graphql/types";

type OrderWithProducts = PrismaOrder & { products: (OrdersOnProducts & { product: Product })[] };

jest.mock("@store-monorepo/marco-api");
jest.mock("@store-monorepo/repo", () => ({
  ...jest.requireActual("@store-monorepo/repo"),
  Product: {
    findMany: jest.fn().mockResolvedValue([])
  },
  Order: {
    create: jest.fn(),
    findById: jest.fn(),
    findBy: jest.fn(),
    update: jest.fn(),
    updateMany: jest.fn()
  },
  OrdersOnProducts: {
    findBy: jest.fn()
  }
}));

const mockOrder = (id?: string) => ({
  id,
  vendor: Vendor.Marco,
  carrier: ShippingProvider.Ups,
  shipTo: "John Hickle",
  street: "12083 Dorcas Tunnel",
  zipCode: "51940-7250",
  state: STATE.AZ,
  status: STATUS.DESIGN,
  line2: "Cool Organization",
  street2: "Unit 1",
  city: "Beierton",
  packingSlipId: "cd6a6696-90ed-4424-a25a-947486229b83",
  packingSlipTitle: "ae52a6cf-95bb-4bd3-8204-f4b02446b304",
  netsuiteId: "082dc15c-f1e2-49ae-9dbc-a067933379fc",
  trackingNumber: null,
  products: [],
  fundraiserId: "191919",
  shipToEmail: "<EMAIL>",
  shipToPhone: "**********",
  billingStreet: "456 Billing St",
  billingStreet2: "Suite 7",
  billingCity: "Billtown",
  billingState: STATE.NY,
  billingZipCode: "67890",
  confirmationId: undefined,
  orderId: undefined,
  trackingUrl: null,
  baseSubtotal: 0,
  shippingCost: 0,
  // TaxAmount: 0,
  // discountAmount: 0
});

describe("Order Activities", () => {
  describe("createOrderDraft", () => {
    it("creates an order in the database", async () => {
      const order = {
        ...mockOrder(),
        scheduleAt: new Date().toISOString()
      };
      await Activities.createOrderDraft(order);
      expect(Repo.Order.create).toHaveBeenCalledWith({
        ...order,
        scheduleAt: new Date(order.scheduleAt),
        products: { create: [] }
      });
    });
  });

  describe("createMarcoOrder", () => {
    const findById = jest.fn();
    const findOrdersProducts = jest.fn();

    beforeEach(() => {
      jest.clearAllMocks();
      Repo.Order.findById = findById;
      Repo.OrdersOnProducts.findBy = findOrdersProducts.mockResolvedValue([]);
      (MarcoAPI.createOrder as jest.Mock).mockResolvedValue({ status: "ok", order: 12345 });
      findById.mockReset();
    });

    it("creates a marco order when status is DESIGN", async () => {
      const order = {
        ...mockOrder(),
        externalId: "2e8499c6-efa6-4b34-a220-a6d1b4736229",
        createdAt: new Date(),
        scheduleAt: new Date(),
        updatedAt: new Date(),
        status: STATUS.DESIGN,
        id: "123",
        state: STATE.AZ
      };
      findById.mockResolvedValue(order);
      await Activities.createMarcoOrder(order as OrderWithProducts);
      expect(MarcoAPI.createOrder).toHaveBeenCalledWith({
        ...order,
        products: []
      });

      expect((Repo.Order.update as jest.Mock)).toHaveBeenCalledWith("123", { status: STATUS.CREATED, externalId: "12345" });
    });

    it("does not create a marco order when status is HOLD", async () => {
      const order = {
        ...mockOrder(),
        externalId: "2e8499c6-efa6-4b34-a220-a6d1b4736228",
        createdAt: new Date(),
        scheduleAt: new Date(),
        updatedAt: new Date(),
        state: STATE.AZ,
        status: STATUS.HOLD,
        id: "123",
      };
      findById.mockResolvedValue({ ...order });
      await Activities.createMarcoOrder(order as OrderWithProducts);
      expect(MarcoAPI.createOrder).not.toHaveBeenCalled();
    });

    it("does not create a marco order when status is SHIPPED", async () => {
      findById.mockResolvedValue({ status: STATUS.SHIPPED });
      const order = {
        ...mockOrder(),
        externalId: "2e8499c6-efa6-4b34-a220-a6d1b4736227",
        createdAt: new Date(),
        scheduleAt: new Date(),
        updatedAt: new Date(),
        state: STATE.AZ,
        status: STATUS.SHIPPED,
      };
      await Activities.createMarcoOrder(order as OrderWithProducts);
      expect(MarcoAPI.createOrder).not.toHaveBeenCalled();
    });
  });

  describe("closeDBOrders", () => {
    it("sets the status of orders to closed", async () => {
      const ids = ["1", "2", "3"];
      await Activities.closeDBOrders(ids);
      expect(Repo.Order.updateMany).toHaveBeenCalledWith({
        netsuiteId: { in: ids },
      }, { status: STATUS.CLOSED });
    });
  });
});
