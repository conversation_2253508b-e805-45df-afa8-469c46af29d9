/* eslint-disable check-file/folder-match-with-fex, functional/no-return-void*/
import Magento from "@store-monorepo/magento-api";
import Activities from "../../../../temporal/workflows/activities/magento";

jest.mock("@store-monorepo/magento-api");

describe("Magento Activities", () => {
  describe("sendNetsuiteIdToMagento", () => {
    it("sends a netsuiteId to Magento", async () => {
      await Activities.sendNetsuiteIdToMagento("123", "456");
      expect(Magento.Order.addNetsuiteId).toHaveBeenCalledWith("123", "456");
    });
  });
});
