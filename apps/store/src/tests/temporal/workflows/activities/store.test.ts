/* eslint-disable max-len, max-lines, max-lines-per-function, max-statements, functional/no-return-void, functional/immutable-data, import/no-unused-modules, check-file/folder-match-with-fex, jest/no-disabled-tests */
import { StoreType, LOGO_TYPE } from "@store-monorepo/repo";
import { Ticket, CreateTicketInput, UpdateTicketInput } from "@store-monorepo/zendesk-api";

import Repo from "@store-monorepo/repo";
import Tickets from "@store-monorepo/zendesk-api";

import StoreActivities from "../../../../temporal/workflows/activities/store";
import Slack from "../../../../lib/slack";
import Temporal from "../../../../lib/temporal";

const ZENDESK_SUBDOMAIN = process.env.ZENDESK_SUBDOMAIN;

const NEW_STORE: StoreType = {
  id: "store_7b000000000e",
  fundraiserId: 227457,
  fundraiserEntityId: 611532,
  activityType: "softball",
  startDate: new Date("2024-11-01T00:00:00.000Z"),
  endDate: new Date("2024-11-29T00:00:00.000Z"),
  city: "Br***k",
  state: "minnesota",
  zip: "55428",
  slug: "mn-spice-softball-2024",
  status: "approved",
  teamSize: 12,
  name: "MN***all",
  fanStore: true,
  incentiveStore: true,
  hasParticipants: false,
  organizationId: 81433,
  organizationLegalName: "P***y",
  organizationName: "Pi***y",
  salesRep: "J* A*",
  salesRepEmail: "j***<EMAIL>",
  salesRepUDID: "udu_clm127bz7002u0000000t6ap6",
  groupLeader: "T* M*",
  groupLeaderEmail: "m*t@d**9.org",
  groupLeaderUDID: "udu_clr6xsndv01ae0000004cwo",
  logoPrimaryColor: "#0C0C10|#2F3032",
  logoSecondaryColor: "#ED1C24|#B42232",
  logoDigitalUrl: "/uploads/tmp/1727501932-32-0027-6548/de49645d-9fb8-4f00-a6b0-c22b88f50cf7_d.svg",
  logoEmbroideryUrl: "/uploads/tmp/1727501932-32-0026-7512/de49645d-9fb8-4f00-a6b0-c22b88f50cf7_e.svg",
  logoHatUrl: "/uploads/tmp/1727501932-32-0025-3329/de49645d-9fb8-4f00-a6b0-c22b88f50cf7_h.svg",
  logoWebHeaderUrl: "/uploads/tmp/1727501932-32-0028-1569/de49645d-9fb8-4f00-a6b0-c22b88f50cf7_w.svg",
  logo: "74",
  previousLogo: false,
  logoScript: "MN SPICE | SOFTBALL",
  emailLogo: null,
  fundraiserLogo: "Screenshot_2024-09-27_at_10.38.12_PM.png",
  fundraiserLogoId: 2027913,
  raiseCreatedAt: new Date("2024-09-28T05:38:53.000Z"),
  raiseUpdatedAt: new Date("2024-09-28T05:38:53.000Z"),
  logoUpdatedAt: new Date("2024-09-28T05:38:54.000Z"),
  createdAt: new Date("2024-09-28T05:38:55.406Z"),
  updatedAt: new Date("2024-09-28T05:38:55.406Z"),
  teamId: "team_2a9874aa2c42000000001b",
  groupId: 133761,
  groupName: "softball",

  storeUrl: null,
  accountManager: null,
  accountManagerEmail: null,
  accountManagerUDID: null,
  fundraiserPrimaryColor: null,
  fundraiserSecondaryColor: null,
  logoDigitalVerifiedAt: null,
  logoEmbroideryVerifiedAt: null,
  logoHatVerifiedAt: null,
  logoWebHeaderVerifiedAt: null,
  magentoStoreId: null,
  magentoStoreCode: null,
  magentoManagerEmail: null,
  builtAt: null,

  logoType: LOGO_TYPE.TEAM,
  logoNotes: "Hoover above logo Soccer below logo",
  deactivatedAt: null,
  pointsPercentage: 10,
  storeCode: null,
  partnerId: null,
};

const TICKET: Ticket = {
  url: `https://${ZENDESK_SUBDOMAIN}.zendesk.com/api/v2/tickets/2.json`,
  id: 2,
  via: { channel: "api" },
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString(),
  type: null,
  subject: "",
  raw_subject: "",
  description: "",
  status: "new",
  requester_id: **************,
  submitter_id: **************,
  group_id: **************,
  has_incidents: false,
  is_public: true,
  due_at: null,
  tags: null,
  custom_status_id: **************,
  ticket_form_id: 27836215818779,
  brand_id: 27836201308955,
  allow_channelback: false,
  allow_attachments: true,
  from_messaging_channel: false,
  followup_ids: []
};

const fromStore = jest.fn();
const createTicket = jest.fn();
const updateTicket = jest.fn();

describe("Store Activities", () => {
  beforeEach(() => {
    jest.clearAllMocks();

    fromStore.mockClear();

    Repo.ZendeskTicket.fromStore = jest.fn().mockImplementation(() => fromStore());
    Repo.ZendeskTicket.upsert = jest.fn().mockImplementation(jest.fn());

    Tickets.createTicket = jest.fn().mockImplementation((create: CreateTicketInput) => createTicket(create));
    Tickets.updateTicket = jest.fn().mockImplementation((id: string, update: UpdateTicketInput) => updateTicket(id, update));
    Tickets.ticketUrl = jest.fn().mockImplementation((id: number) => `https://${ZENDESK_SUBDOMAIN}.zendesk.com/agent/tickets/${id}`);

    Slack.postMessage = jest.fn().mockImplementation((text: string) => text);
    Temporal.getWorkflowUrl = jest.fn().mockReturnValue("https://temporal.snap.app/");

    createTicket.mockImplementation((create: CreateTicketInput) => ({ ...TICKET, ...create }));
    updateTicket.mockImplementation((id: string, update: UpdateTicketInput) => ({ ...TICKET, ...update, id }));
  });

  describe("upsertZendeskTicket", () => {
    it("should not create a ticket for non-incentive store", async () => {
      const non_created = await StoreActivities.upsertZendeskTicket({ ...NEW_STORE, incentiveStore: false });
      expect(typeof non_created).toEqual("string");
    }, 15000);

    it("should set the correct attributes for team logo type", async () => {
      fromStore.mockResolvedValue([]);

      const ticket = await StoreActivities.upsertZendeskTicket({ ...NEW_STORE, logoType: LOGO_TYPE.TEAM });
      expect((ticket as Ticket).subject).toEqual(`Team Logo Request|2024-09-28|${NEW_STORE.fundraiserId}`);
    }, 15000);

    it("should set the correct attributes for template logo type", async () => {
      fromStore.mockResolvedValue([]);

      const ticket = await StoreActivities.upsertZendeskTicket({ ...NEW_STORE, logoType: LOGO_TYPE.TEMPLATE });
      expect((ticket as Ticket).subject).toEqual(`Template Logo Request|2024-09-28|${NEW_STORE.fundraiserId}`);
    }, 15000);

    it("should create new ticket", async () => {
      fromStore.mockResolvedValue([]);

      const ticket = await StoreActivities.upsertZendeskTicket(NEW_STORE);

      expect(createTicket).toHaveBeenCalled();
      expect(updateTicket).not.toHaveBeenCalled();
      expect((ticket as Ticket)?.description.includes("Closed Ticket")).toBe(false);
    }, 15000);

    it("should create new ticket and reference the closed tickets", async () => {
      fromStore.mockResolvedValue([{
        id: "id",
        zendeskId: 10,
        storeId: NEW_STORE.id,
        status: "CLOSED",
        createdAt: new Date(),
        updatedAt: new Date()
      }]);

      const ticket = await StoreActivities.upsertZendeskTicket(NEW_STORE);

      expect(createTicket).toHaveBeenCalled();
      expect(updateTicket).not.toHaveBeenCalled();
      expect((ticket as Ticket)?.description.includes("Closed Ticket")).toBe(true);
    }, 15000);

    it("should update the existing ticket", async () => {
      fromStore.mockResolvedValue([{
        id: "id",
        zendeskId: 10,
        storeId: NEW_STORE.id,
        status: "CLOSED",
        createdAt: new Date(),
        updatedAt: new Date()
      }, {
        id: "id",
        zendeskId: 11,
        storeId: NEW_STORE.id,
        status: "OPEN",
        createdAt: new Date(),
        updatedAt: new Date()
      }]);

      const ticket = await StoreActivities.upsertZendeskTicket(NEW_STORE);

      expect(createTicket).not.toHaveBeenCalled();
      expect(updateTicket).toHaveBeenCalled();
      expect((ticket as Ticket)?.id).toBe(11);
    }, 15000);

    it("should contains the link to team (custom) logo", async () => {
      fromStore.mockResolvedValue([]);

      const ticket = await StoreActivities.upsertZendeskTicket({ ...NEW_STORE, logo: "Custom Logo", logoType: LOGO_TYPE.TEAM });

      expect((ticket as Ticket)?.description.includes(`${NEW_STORE.fundraiserId}-999.png`)).toBe(true);
    }, 15000);

    it("should contains the link to template logos", async () => {
      fromStore.mockResolvedValue([]);

      const ticket = await StoreActivities.upsertZendeskTicket({ ...NEW_STORE, logo: "66", logoType: LOGO_TYPE.TEMPLATE });

      expect((ticket as Ticket)?.description.includes(`${NEW_STORE.fundraiserId}_h.svg`)).toBe(true);
      expect((ticket as Ticket)?.description.includes(`${NEW_STORE.fundraiserId}_d.svg`)).toBe(true);
      expect((ticket as Ticket)?.description.includes(`${NEW_STORE.fundraiserId}_e.svg`)).toBe(true);
      expect((ticket as Ticket)?.description.includes(`${NEW_STORE.fundraiserId}_w.svg`)).toBe(true);
    }, 15000);
  });

  describe("updateZendeskWithLogoChanges", () => {
    it("should not create or update ticket", async () => {
      fromStore.mockResolvedValue([]);

      const EXISTING_STORE = { ...NEW_STORE };
      const ticket = await StoreActivities.updateZendeskWithLogoChanges(NEW_STORE, EXISTING_STORE);

      expect(Slack.postMessage).not.toHaveBeenCalled();
      expect(typeof ticket).toEqual("string");
    }, 15000);

    it("should create the new ticket", async () => {
      fromStore.mockResolvedValue([]);

      const ticket = await StoreActivities.updateZendeskWithLogoChanges(NEW_STORE, null);

      expect(ticket).toBeDefined();
      expect(typeof ticket).toEqual("object");
    }, 15000);

    it("should update the existing ticket", async () => {
      fromStore.mockResolvedValue([]);

      const EXISTING_STORE = { ...NEW_STORE, logo: "previous_logo" };
      const ticket = await StoreActivities.updateZendeskWithLogoChanges(NEW_STORE, EXISTING_STORE);

      expect(ticket).toBeDefined();
      expect(typeof ticket).toEqual("string");
    }, 15000);
  });
});
