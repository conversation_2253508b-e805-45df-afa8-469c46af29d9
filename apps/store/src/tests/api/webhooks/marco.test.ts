/* eslint-disable functional/no-return-void, functional/no-let, import/no-unused-modules, check-file/folder-match-with-fex */
import express from "express";
import request from "supertest";

import addMarcoWebhook from "../../../api/webhooks/marco";
import { MarcoEventInput, MarcoOrderStatus } from "../../../api/graphql/types";

const marcoWebhook = jest.fn();

jest.mock("@store-monorepo/graphql", () => ({
  marcoWebhook: jest.fn().mockImplementation((event) => marcoWebhook(event))
}));

const MARCO_WEBHOOK_KEY = process.env.MARCO_WEBHOOK_KEY || "";

const EVENT: MarcoEventInput = {
  id: 8116900,
  purchase_order: "",
  status: MarcoOrderStatus.Shipped,
};

describe.skip("POST /api/webhooks/marco", () => {
  let server: express.Express;

  beforeEach(() => {
    server = express();
    addMarcoWebhook(server);

    marcoWebhook.mockImplementation((event: MarcoEventInput) => ({ success: true }));
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it("should return the authentication error", async () => {
    const resp = await request(server).post("/api/webhooks/marco").
      send(EVENT);

    expect(resp.status).toBe(401);
    expect(marcoWebhook).not.toHaveBeenCalled();
  });

  it("should authenticate by query param and call the marcoWebhook mutation", async () => {
    const resp = await request(server).post(`/api/webhooks/marco?key=${MARCO_WEBHOOK_KEY}`).
      send(EVENT);

    expect(resp.status).toBe(200);
    expect(marcoWebhook).toHaveBeenCalledTimes(1);
    expect(marcoWebhook).toHaveBeenCalledWith(EVENT);
  });

  it("should authenticate by header and call the marcoWebhook mutation", async () => {
    const resp = await request(server).post(`/api/webhooks/marco`).
      set("Authorization", `Bearer ${MARCO_WEBHOOK_KEY}`).
      send(EVENT);

    expect(resp.status).toBe(200);
    expect(marcoWebhook).toHaveBeenCalledTimes(1);
    expect(marcoWebhook).toHaveBeenCalledWith(EVENT);
  });
});
