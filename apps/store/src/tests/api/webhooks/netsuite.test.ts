/* eslint-disable max-lines-per-function, functional/no-return-void, functional/no-let, import/no-unused-modules, check-file/folder-match-with-fex */
import express from "express";
import request from "supertest";

import { NetsuiteEventInput, OrderInput } from "../../../api/graphql/types";
import addNetsuiteWebhooks from "../../../api/webhooks/netsuite";


const mockOrder = {
  fundraiserId: "1234"
};

const netsuiteWebhook = jest.fn();
const createOrder = jest.fn();
const findFirst = jest.fn();

jest.mock("@store-monorepo/graphql", () => ({
  netsuiteWebhook: jest.fn().mockImplementation((event) => netsuiteWebhook(event)),
  createOrder: jest.fn().mockImplementation((order: OrderInput) => createOrder(order))
}));

jest.mock("@store-monorepo/repo", () => ({
  Order: {
    findFirst: jest.fn().mockImplementation((query, include) => findFirst(query, include))
  }
}));

jest.mock("@store-monorepo/order-notifications", () => ({
  newOrder: jest.fn().mockImplementation((event, logo) => mockOrder)
}));

const API_KEY = "x-store-api-key";
const URL = "/api/webhooks/netsuite";

const EVENT: NetsuiteEventInput = {
  eventType: "edit",
  record: { id: "123" },
};

const RECREATE_EVENT: NetsuiteEventInput = {
  eventType: "create",
  record: {
    id: "123",
    fields: {
      custbody_fundraiser_id: "1234",
      custbody_parent_sales_order: {
        id: "123"
      }
    }
  }
};

const CREATE_EVENT: NetsuiteEventInput = {
  eventType: "create",
  record: {
    id: "123",
    fields: {
      custbody_fundraiser_id: "1234"
    }
  }
};

describe("POST /api/webhooks/netsuite", () => {
  let server: express.Express;

  beforeEach(() => {
    server = express();
    addNetsuiteWebhooks(server);
    netsuiteWebhook.mockReset();
    createOrder.mockReset();
    findFirst.mockReset();
    netsuiteWebhook.mockImplementation((event: NetsuiteEventInput) => ({ success: true }));
    createOrder.mockImplementation((order: OrderInput) => ({ success: true }));
    findFirst.mockImplementation(() => ({ products: [{ logo: "testLogo" }] }));
  });

  it("should do nothing if api key is invalid", async () => {
    const resp = await request(server).
      post(URL).
      set(API_KEY, "bad_api_key").
      send(EVENT);

    expect(resp.status).toBe(200);
    expect(netsuiteWebhook).toHaveBeenCalledTimes(0);
    expect(createOrder).toHaveBeenCalledTimes(0);
  });

  it("should call the netsuiteWebhook mutation", async () => {
    const resp = await request(server).
      post(URL).
      set(API_KEY, "test_api_key").
      send(EVENT);

    expect(resp.status).toBe(200);
    expect(netsuiteWebhook).toHaveBeenCalledTimes(1);
    expect(netsuiteWebhook).toHaveBeenCalledWith(EVENT);
  });

  it("should call the createOrder mutation for create event type with custbody_parent_sales_order.id present", async () => {
    const resp = await request(server).
      post(URL).
      set(API_KEY, "test_api_key").
      send(RECREATE_EVENT);

    expect(resp.status).toBe(200);
    expect(findFirst).toHaveBeenCalledTimes(1);
    expect(createOrder).toHaveBeenCalledTimes(1);
    expect(createOrder).toHaveBeenCalledWith(mockOrder);
    expect(netsuiteWebhook).toHaveBeenCalledTimes(0);
  });

  it("should not call the createOrder mutation for create event type when custbody_parent_sales_order.id is not present", async () => {
    const resp = await request(server).
      post(URL).
      set(API_KEY, "test_api_key").
      send(CREATE_EVENT);

    expect(resp.status).toBe(200);
    expect(findFirst).toHaveBeenCalledTimes(0);
    expect(createOrder).toHaveBeenCalledTimes(0);
    expect(netsuiteWebhook).toHaveBeenCalledTimes(0);
  });
});
