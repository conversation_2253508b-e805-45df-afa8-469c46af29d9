/* eslint-disable max-lines-per-function, functional/no-return-void, functional/no-let, import/no-unused-modules, check-file/folder-match-with-fex */
import { ApolloServer } from "apollo-server-express";
import { buildSubgraphSchema } from "@apollo/subgraph";
import { applyMiddleware } from "graphql-middleware";
import { gql } from "graphql-tag";
import express, { Application } from "express";
import supertest, { SuperTest, Test } from "supertest";
import Shield from "../../../api/graphql/shield";

const typeDefs = gql`
  type Query {
    testQuery: Boolean
  }
  input TestInput {
    test: String
  }
  type Mutation {
    testMutation(input: TestInput): Boolean
  }
`;

const resolvers = {
  Query: {
    testQuery: jest.fn(() => true),
  },
  Mutation: {
    testMutation: jest.fn(() => true),
  },
};

const schema = buildSubgraphSchema({ typeDefs, resolvers });
const server = new ApolloServer({
  schema: applyMiddleware(schema, Shield),
  context: ({ req }) => ({ req }),
});

let app: Application;
let request: SuperTest<Test>;

beforeAll(async () => {
  await server.start();

  app = express();
  app.use(express.json());

  server.applyMiddleware({ app, path: "/graphql" });

  request = supertest(app) as unknown as SuperTest<Test>;
});

afterAll(async () => {
  await server.stop();
});

describe("API shield", () => {
  it("should reject query without x-api-key header", async () => {
    const _res = await request.post("/graphql").
      send({
        query: `
          query TestQuery {
            testQuery
          }
        `,
      });

    expect(_res.body.errors).toBeDefined();
    expect(_res.body.errors[0].message).toContain("Not Authorised!");
    expect(resolvers.Query.testQuery).not.toHaveBeenCalled();
  });

  it("should reject mutation without x-api-key header", async () => {
    const _res = await request.post("/graphql").
      send({
        query: `
          mutation TestMutation($input: TestInput) {
            testMutation(input: $input)
          }
        `,
        variables: { input: { test: "test" } },
      });

    expect(_res.body.errors).toBeDefined();
    expect(_res.body.errors[0].message).toContain("Not Authorised!");
    expect(resolvers.Mutation.testMutation).not.toHaveBeenCalled();
  });

  it("should allow query with x-api-key header", async () => {
    const _res = await request.post("/graphql").
      set("x-api-key", "test-key").
      send({
        query: `
          query TestQuery {
            testQuery
          }
        `,
      });

    expect(_res.body.errors).toBeUndefined();
    expect(_res.body.data.testQuery).toBe(true);
    expect(resolvers.Query.testQuery).toHaveBeenCalled();
  });

  it("should allow mutation with x-api-key header", async () => {
    const _res = await request.post("/graphql").
      set("x-api-key", "test-key").
      send({
        query: `
          mutation TestMutation($input: TestInput) {
            testMutation(input: $input)
          }
        `,
        variables: { input: { test: "test" } },
      });

    expect(_res.body.errors).toBeUndefined();
    expect(_res.body.data.testMutation).toBe(true);
    expect(resolvers.Mutation.testMutation).toHaveBeenCalled();
  });
});
