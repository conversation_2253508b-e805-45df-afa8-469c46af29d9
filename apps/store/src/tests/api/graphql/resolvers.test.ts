/* eslint-disable max-len, max-lines, max-lines-per-function, functional/no-return-void, functional/no-let, import/max-dependencies, import/no-unused-modules, check-file/folder-match-with-fex, functional/immutable-data  */
import { readFileSync } from "fs";
import { ApolloServer } from "apollo-server-express";
import { buildSubgraphSchema } from "@apollo/subgraph";
import { gql } from "graphql-tag";
import express, { Application } from "express";
import supertest, { SuperTest, Test } from "supertest";

import Repo from "@store-monorepo/repo";

import { STATUS, VENDOR } from "@prisma/client";
import Resolvers from "../../../api/graphql/resolvers";
import Temporal from "../../../temporal";
import Signals from "../../../temporal/workflows/signals";
import Netsuite from "../../../lib/netsuite";
import Magento from "../../../lib/magento";
import { MarcoEventInput, ShippingProvider } from "../../../api/graphql/types";

jest.mock("@store-monorepo/repo", () => ({
  ...jest.requireActual("@store-monorepo/repo"),
  Product: {
    findMany: jest.fn().mockResolvedValue([])
  },
  Order: {
    findBy: jest.fn()
  },
  OrdersOnProducts: {
    findBy: jest.fn()
  }
}));
const typeDefs = gql(
  readFileSync("apps/store/src/api/graphql/schema.graphql", {
    encoding: "utf-8"
  })
);

const schema = buildSubgraphSchema({ typeDefs, resolvers: Resolvers });
const server = new ApolloServer({
  schema,
  context: ({ req }) => ({ req }),
});

const signal = jest.fn();

const getHandle = jest.fn().mockImplementation(() => ({
  signal
}));

Temporal.client = jest.fn().mockImplementation(() => ({
  workflow: {
    getHandle
  }
}));

let app: Application;
let request: SuperTest<Test>;

beforeAll(async () => {
  await server.start();

  app = express();
  app.use(express.json());

  server.applyMiddleware({ app, path: "/graphql" });

  request = supertest(app) as unknown as SuperTest<Test>;
});

afterAll(async () => {
  await server.stop();
});

describe("Resolvers", () => {
  describe("netsuiteWebhook", () => {
    it("sends a signal to an existing workflow", async () => {
      (Repo.Order.findBy as jest.Mock).mockReturnValue({ vendor: VENDOR.MARCO, netsuiteId: "order-1234" });
      const netsuiteEvent = {
        eventType: "CREATE",
        record: {
          id: "order-1234"
        }
      };
      await request.post("/graphql").
        send({
          query: `
            mutation TestQuery ($netsuiteEvent: NetsuiteEventInput) {
              netsuiteWebhook(netsuiteEvent: $netsuiteEvent) {
                success
              }
            }
          `,
          variables: {
            netsuiteEvent
          }
        });

      expect(getHandle).toHaveBeenCalledWith(`createOrder-order-1234`);
      expect(signal).toHaveBeenCalledWith(Signals.order.updateOrder, netsuiteEvent);
    });
  });

  describe("marcoWebhook", () => {
    const status: MarcoEventInput = {
      id: 123456,
      purchase_order: "654321",
      shipments: [{
        shipping_carrier: ShippingProvider.FedEx,
        tracking_number: "**********",
        search_link: "http://nsa-and-big-tech-are-tracking-you.com"
      }]
    };

    const now = Date.now();

    beforeEach(() => {
      jest.clearAllMocks();
      Repo.Order.updateWhere = jest.fn();
      Netsuite.updateNetsuiteTracking = jest.fn();
      Magento.addTrackingNumbers = jest.fn();
      Date.now = jest.fn(() => now);
    });

    it("should update carrier and tracking number", async () => {
      (Repo.Order.updateWhere as jest.Mock).mockReturnValue({
        status: STATUS.CREATED,
        trackingNumber: status.shipments[0].tracking_number
      });
      await Resolvers.Mutation.marcoWebhook(null, { status });

      expect(Repo.Order.updateWhere).toHaveBeenCalledWith({ netsuiteId: status.purchase_order, externalId: `${status.id}` }, {
        carrier: status.shipments[0].shipping_carrier,
        trackingNumber: status.shipments[0].tracking_number,
        trackingUrl: status.shipments[0].search_link
      });
      expect(Netsuite.updateNetsuiteTracking).toHaveBeenCalledTimes(1);
    });

    it("should handle errors during the update process", async () => {
      const errorMessage = "Failed to update order";
      Repo.Order.updateWhere = jest.fn().mockRejectedValue(new Error(errorMessage));

      const result = await Resolvers.Mutation.marcoWebhook(null, { status });

      expect(result).toEqual({ success: false, error: errorMessage });
      expect(Netsuite.updateNetsuiteTracking).not.toHaveBeenCalled();
    });

    it("should call Netsuite.updateNetsuiteTracking when tracking number is present and order status is not SHIPPED", async () => {
      const dbOrder = { status: "PROCESSING" };
      Repo.Order.updateWhere = jest.fn().mockResolvedValue(dbOrder);

      await Resolvers.Mutation.marcoWebhook(null, { status });

      expect(Netsuite.updateNetsuiteTracking).toHaveBeenCalledWith(dbOrder);
      expect(Repo.Order.updateWhere).toHaveBeenCalledWith({ netsuiteId: status.purchase_order }, { status: "SHIPPED", shippingReceivedAt: new Date(now) });
    });

    it("should not call Netsuite.updateNetsuiteTracking when order status is SHIPPED", async () => {
      const dbOrder = { status: "SHIPPED" };
      Repo.Order.updateWhere = jest.fn().mockResolvedValue(dbOrder);

      await Resolvers.Mutation.marcoWebhook(null, { status });

      expect(Netsuite.updateNetsuiteTracking).not.toHaveBeenCalled();
      expect(Repo.Order.updateWhere).not.toHaveBeenCalled();
    });

    it("should call Magento.addTrackingNumbers when vendor is STORE", async () => {
      const dbOrder = { status: "PROCESSING", vendor: "STORE" };
      Repo.Order.updateWhere = jest.fn().mockResolvedValue(dbOrder);

      await Resolvers.Mutation.marcoWebhook(null, { status });

      expect(Magento.addTrackingNumbers).toHaveBeenCalledWith(dbOrder);
      expect(Repo.Order.updateWhere).toHaveBeenCalledWith({ netsuiteId: status.purchase_order }, { status: "SHIPPED", shippingReceivedAt: new Date(now) });
    });

    it("should not call Magento.addTrackingNumbers when vendor is not STORE", async () => {
      const dbOrder = { status: "PROCESSING", vendor: "MARCO" };
      Repo.Order.updateWhere = jest.fn().mockResolvedValue(dbOrder);

      await Resolvers.Mutation.marcoWebhook(null, { status });

      expect(Magento.addTrackingNumbers).not.toHaveBeenCalled();
      expect(Repo.Order.updateWhere).toHaveBeenCalledWith({ netsuiteId: status.purchase_order }, { status: "SHIPPED", shippingReceivedAt: new Date(now) });
    });
  });
});
