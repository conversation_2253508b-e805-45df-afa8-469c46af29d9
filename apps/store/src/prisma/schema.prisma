datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

generator client {
  provider = "prisma-client-js"
}

model Ticket {
  id        String   @id @default(uuid())
  ticketId  Int      @unique
  scopeId   Int?
  subject   String?
  message   String?
  createdAt DateTime @default(now())
  updatedAt DateTime @default(now()) @updatedAt
}

model Transaction {
  id            String   @id @default(uuid())
  userSsoId     String?
  email         String?
  scopeId       Int?
  transactionId String?
  amount        Float?
  points        Float?
  status        String?
  transaction   Json
  createdAt     DateTime @default(now())
  updatedAt     DateTime @default(now()) @updatedAt
}

model StakeHolder {
  id        String @id @default(uuid())
  email     String @unique @db.VarChar(255)
  userSsoId String @unique
}

model UserPreference {
  id                   String  @id @default(uuid())
  userId               String  @unique @map("user_id")
  featureTourCompleted Boolean @default(false) @map("feature_tour_completed")

  @@map("user_preference")
}

model BuildRequest {
  id              String   @id @default(uuid())
  userSsoId       String
  email           String
  storeRequest    Boolean  @default(false)
  storeName       String?
  referenceTicket Int      @unique
  storeInfo       Json
  createdAt       DateTime @default(now())
  updatedAt       DateTime @default(now()) @updatedAt
}

enum PAYABLETYPE {
  CAMPAIGN
  STORE
}

model PayableDetails {
  id              String      @id @default(uuid())
  campaignId      Int?        @unique
  scopeId         Int?        @unique
  identityId      Int?        @unique
  groupLeaderInfo Json
  name            String?
  payableName     String
  ein             Int
  fullAddressOne  String
  fullAddressTwo  String?
  street          String?
  city            String
  region          String
  country         String
  zip             Int
  payableType     PAYABLETYPE
  createdAt       DateTime    @default(now())
  updatedAt       DateTime    @default(now()) @updatedAt
}

model PointsWithdrawalRequest {
  id                     String   @id @default(uuid())
  ticketId               Int      @unique
  amount                 Int
  scopeInfo              Json
  payableInfo            Json
  requesterInfo          Json
  pointsType             String
  expactedWithdrawalDate DateTime
  createdAt              DateTime @default(now())
  updatedAt              DateTime @default(now()) @updatedAt
}

enum VENDOR {
  MARCO
  STORE
  RAISE
  RAISE_MARCO
}

enum LOGO_POSITION {
  FRONT_CENTER
  FRONT_LEFT_ARM
  FRONT_LEFT_CHEST
  FRONT_LEFT_LEG
  FRONT_RIGHT_ARM
  FRONT_RIGHT_CHEST
  FRONT_RIGHT_LEG
  DIGITAL_PRODUCT
}

enum CARRIER {
  DHL
  UPS
  FedEx
  USPS
  OSM
}

enum PRODUCT_SIZE {
  XXS
  XS
  S
  M
  L
  XL
  XXL
  XXXL
  XXXXL
  YXS
  YS
  YM
  YL
  YXL
  YXXL
  YXXXL
  OSFA
  NO_SIZE
  DIGITAL_PRODUCT
}

model Order {
  id                     String             @id @default(dbgenerated("CONCAT('so_', RIGHT(uuid_generate_v4()::text, 12))"))
  fundraiserId           String?            @map("fundraiser_id")
  scheduleAt             DateTime           @default(now()) @map("schedule_at")
  vendor                 VENDOR
  carrier                CARRIER
  shipTo                 String             @map("ship_to")
  shipToEmail            String?            @map("ship_to_email")
  shipToPhone            String?            @map("ship_to_phone")
  line2                  String?
  street                 String
  street2                String?
  city                   String
  state                  STATE
  zipCode                String             @map("zip_code") @db.VarChar(10)
  billingStreet          String?            @map("billing_street")
  billingStreet2         String?            @map("billing_street2")
  billingCity            String?            @map("billing_city")
  billingState           String?            @map("billing_state")
  billingZipCode         String?            @map("billing_zip_code")
  packingSlipId          String             @map("packing_slip_id")
  packingSlipTitle       String?            @map("packing_slip_title")
  trackingNumber         String?            @map("tracking_number")
  trackingUrl            String?            @map("tracking_url")
  netsuiteId             String             @unique @map("netsuite_id")
  externalId             String?            @unique @map("external_id")
  createdAt              DateTime           @default(now()) @map("created_at")
  updatedAt              DateTime           @default(now()) @map("updated_at")
  products               OrdersOnProducts[]
  status                 STATUS?            @default(DESIGN)
  baseSubtotal           Int?               @map("base_subtotal")
  shippingCost           Int?               @map("shipping_cost")
  taxAmount              Int?               @map("tax_amount")
  discountAmount         Int?               @map("discount_amount")
  giftCardAmount         Int?               @map("gift_card_amount")
  orderId                String?            @unique @map("magento_order_id")
  confirmationId         String?            @unique @map("magento_confirmation_id")
  shippingReceivedAt     DateTime?          @map("shipping_received_at")
  priority               PRIORITY?          @default(NORMAL)
  source                 SOURCE?
  supplierOrderCreatedAt DateTime?          @map("supplier_order_created_at")
  deliveredAt        DateTime?          @map("delivered_at")

  @@index([fundraiserId], name: "idx_order_fundraiser_id")
  @@map("legacy_orders")
}

model Product {
  id           String             @id @default(dbgenerated("CONCAT('pdct_', RIGHT(uuid_generate_v4()::text, 12))"))
  name         String
  sku          String?
  netsuiteId   String?            @unique @map("netsuite_id")
  magentoSku   String?            @unique @map("magento_sku")
  color        String?            @map("color")
  logoPosition LOGO_POSITION      @map("logo_position")
  size         PRODUCT_SIZE
  orders       OrdersOnProducts[]
  createdAt    DateTime           @default(now()) @map("created_at")
  updatedAt    DateTime           @default(now()) @map("updated_at")

  @@map("products")
}

model OrdersOnProducts {
  id              String   @id @default(cuid())
  order           Order    @relation(fields: [orderId], references: [id])
  orderId         String   @map("order_id")
  product         Product  @relation(fields: [productId], references: [id])
  productId       String   @map("product_id")
  logo            String
  receiverName    String   @map("receiver_name")
  printAttributes Json?    @map("print_attributes")
  backLogo        String?  @map("back_print_logo")
  createdAt       DateTime @default(now()) @map("created_at")
  updatedAt       DateTime @default(now()) @map("updated_at")
  amount          Int?

  @@index([orderId, productId], name: "idx_orders_products_order_product")
  @@map("orders_products")
}

enum STATUS {
  DESIGN
  HOLD
  SHIPPED
  DELIVERED
  CREATED
  REJECTED
  MARCO_REJECTED
  CLOSED
}

enum SOURCE {
  MEDUSA
  MAGENTO
  OTK
}

enum STATE {
  AA // Armed Forces Americas (except Canada)
  AE // Armed Forces Europe, the Middle East, and Canada
  AL
  AK
  AP // Armed Forces Pacific
  AZ
  AR
  AS // American Samoa
  CA
  CO
  CT
  DC
  DE
  FL
  FM // Federal State of Micronesia
  GA
  GU // Guam
  HI
  ID
  IL
  IN
  IA
  KS
  KY
  LA
  ME
  MD
  MA
  MH // Marshall Islands
  MI
  MN
  MP // Northern Mariana Islands
  MS
  MO
  MT
  NE
  NV
  NH
  NJ
  NM
  NY
  NC
  ND
  OH
  OK
  OR
  PA
  PR
  PW // Palau
  RI
  SC
  SD
  TN
  TT
  TX
  UT
  VI // Virgin Islands
  VT
  VA
  WA
  WV
  WI
  WY
}

enum LOGO_TYPE {
  TEMPLATE
  TEAM
}

enum PRIORITY {
  NORMAL
  RUSH
}

model Store {
  id                       String          @id @default(dbgenerated("CONCAT('store_', RIGHT(uuid_generate_v4()::text, 12))"))
  fundraiserId             Int?            @unique @map("fundraiser_id")
  fundraiserEntityId       Int?            @map("fundraiser_entity_id")
  activityType             String          @map("activity_type")
  startDate                DateTime        @map("start_date")
  endDate                  DateTime        @map("end_date")
  city                     String
  state                    String
  zip                      String
  slug                     String
  status                   String
  teamSize                 Int?            @map("team_size")
  name                     String?
  incentiveStore           Boolean         @default(false) @map("incentive_store")
  fanStore                 Boolean         @map("fan_store")
  storeUrl                 String?         @map("store_url")
  hasParticipants          Boolean         @map("has_participants")
  organizationId           Int?            @map("organization_id")
  organizationLegalName    String?         @map("organization_legal_name")
  organizationName         String?         @map("organization_name")
  salesRep                 String?         @map("sales_rep")
  salesRepEmail            String?         @map("sales_rep_email")
  salesRepUDID             String?         @map("sales_rep_udid")
  accountManager           String?         @map("account_manager")
  accountManagerEmail      String?         @map("account_manager_email")
  accountManagerUDID       String?         @map("account_manager_udid")
  groupLeader              String?         @map("group_leader")
  groupLeaderEmail         String?         @map("group_leader_email")
  groupLeaderUDID          String?         @map("group_leader_udid")
  fundraiserPrimaryColor   String?         @map("fundraiser_primary_color")
  fundraiserSecondaryColor String?         @map("fundraiser_secondary_color")
  logoPrimaryColor         String?         @map("logo_primary_color")
  logoSecondaryColor       String?         @map("logo_secondary_color")
  logoDigitalUrl           String?         @map("logo_digital_url")
  logoEmbroideryUrl        String?         @map("logo_embroidery_url")
  logoHatUrl               String?         @map("logo_hat_url")
  logoWebHeaderUrl         String?         @map("logo_web_header_url")
  logo                     String?
  previousLogo             Boolean         @map("previous_logo")
  logoScript               String?         @map("logo_script")
  emailLogo                String?         @map("email_logo")
  fundraiserLogo           String?         @map("fundraiser_logo")
  fundraiserLogoId         Int?            @map("fundraiser_logo_id")
  raiseCreatedAt           DateTime        @map("raise_created_at")
  raiseUpdatedAt           DateTime        @map("raise_updated_at")
  logoUpdatedAt            DateTime        @map("logo_updated_at")
  createdAt                DateTime        @default(now()) @map("created_at")
  updatedAt                DateTime        @default(now()) @map("updated_at")
  logoDigitalVerifiedAt    DateTime?       @map("logo_digital_verified_at")
  logoEmbroideryVerifiedAt DateTime?       @map("logo_embroidery_verified_at")
  logoHatVerifiedAt        DateTime?       @map("logo_hat_verified_at")
  logoWebHeaderVerifiedAt  DateTime?       @map("logo_web_header_verified_at")
  teamId                   String?         @map("team_id")
  groupId                  Int?            @map("group_id")
  groupName                String?         @map("group_name")
  magentoStoreId           Int?            @map("magento_store_id")
  magentoStoreCode         String?         @map("magento_store_code")
  magentoManagerEmail      String?         @map("magento_manager_email")
  builtAt                  DateTime?       @map("built_at")
  deactivatedAt            DateTime?       @map("deactivated_at")
  logoType                 LOGO_TYPE       @default(TEMPLATE) @map("logo_type")
  logoNotes                String?         @map("logo_notes")
  ZendeskTicket            ZendeskTicket[]
  pointsPercentage         Int             @default(10) @map("points_percentage")
  storeCode                String?         @map("store_code")
  partnerId                Int?            @map("partner_id")
  schoolName               String?         @map("school_name")
  schoolId                 String?         @map("school_id")

  @@map("stores")
}

enum ZENDESK_TICKET_STATUS {
  NEW
  OPEN
  PENDING
  HOLD
  SOLVED
  CLOSED
}

model ZendeskTicket {
  id        String                @id @default(dbgenerated("CONCAT('storezd_', RIGHT(uuid_generate_v4()::text, 12))"))
  zendeskId Int                   @unique @map("fundraiser_id")
  store     Store                 @relation(fields: [storeId], references: [id])
  storeId   String                @map("store_id")
  status    ZENDESK_TICKET_STATUS
  createdAt DateTime              @default(now()) @map("created_at")
  updatedAt DateTime              @default(now()) @updatedAt @map("updated_at")

  @@map("zendesk_tickets")
}

enum DEAD_LETTER_ORDER_STATUS {
  FAILED_DESIGN
}

model DeadLetterOrders {
  orderId   String                   @id @default(dbgenerated("CONCAT('dlo_', RIGHT(uuid_generate_v4()::text, 12))"))
  status    DEAD_LETTER_ORDER_STATUS
  createdAt DateTime                 @default(now()) @map("created_at")

  @@map("dead_letter_orders")
}
