-- AlterTable
ALTER TABLE "stores" ADD COLUMN     "account_manager" TEXT,
ADD COLUMN     "account_manager_email" TEXT,
ADD COLUMN     "account_manager_udid" TEXT,
ADD COLUMN     "activity_type" TEXT NOT NULL,
ADD COLUMN     "city" TEXT NOT NULL,
ADD COLUMN     "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "email_logo" TEXT,
ADD COLUMN     "end_date" TIMESTAMP(3) NOT NULL,
ADD COLUMN     "fundraiser_entity_id" INTEGER NOT NULL,
ADD COLUMN     "fundraiser_logo" TEXT,
ADD COLUMN     "fundraiser_logo_id" INTEGER,
ADD COLUMN     "fundraiser_primary_color" TEXT,
ADD COLUMN     "fundraiser_secondary_color" TEXT,
ADD COLUMN     "group_leader" TEXT,
ADD COLUMN     "group_leader_email" TEXT,
ADD COLUMN     "group_leader_udid" TEXT,
ADD COLUMN     "logo" TEXT,
ADD COLUMN     "logo_digital_url" TEXT,
ADD COLUMN     "logo_embroidery_url" TEXT,
ADD COLUMN     "logo_hat_url" TEXT,
ADD COLUMN     "logo_primary_color" TEXT,
ADD COLUMN     "logo_script" TEXT,
ADD COLUMN     "logo_secondary_color" TEXT,
ADD COLUMN     "logo_updated_at" TIMESTAMP(3) NOT NULL,
ADD COLUMN     "logo_web_header_url" TEXT,
ADD COLUMN     "name" TEXT,
ADD COLUMN     "organization_id" INTEGER NOT NULL,
ADD COLUMN     "organization_legal_name" TEXT,
ADD COLUMN     "organization_name" TEXT,
ADD COLUMN     "previous_logo" BOOLEAN NOT NULL,
ADD COLUMN     "raise_created_at" TIMESTAMP(3) NOT NULL,
ADD COLUMN     "raise_updated_at" TIMESTAMP(3) NOT NULL,
ADD COLUMN     "sales_rep" TEXT,
ADD COLUMN     "sales_rep_email" TEXT,
ADD COLUMN     "sales_rep_udid" TEXT,
ADD COLUMN     "slug" TEXT NOT NULL,
ADD COLUMN     "start_date" TIMESTAMP(3) NOT NULL,
ADD COLUMN     "state" TEXT NOT NULL,
ADD COLUMN     "status" TEXT NOT NULL,
ADD COLUMN     "team_size" INTEGER,
ADD COLUMN     "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "zip" TEXT NOT NULL;
