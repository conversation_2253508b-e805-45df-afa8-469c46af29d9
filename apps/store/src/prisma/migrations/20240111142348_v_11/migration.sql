-- CreateEnum
CREATE TYPE "PAYABLETYPE" AS ENUM ('CAMPAIGN', 'STORE');

-- CreateTable
CREATE TABLE "PayableDetails" (
    "id" TEXT NOT NULL,
    "campaignId" INTEGER,
    "scopeId" INTEGER,
    "identityId" INTEGER,
    "groupLeaderInfo" JSONB NOT NULL,
    "name" TEXT,
    "payableName" TEXT NOT NULL,
    "ein" INTEGER NOT NULL,
    "fullAddressOne" TEXT NOT NULL,
    "fullAddressTwo" TEXT,
    "street" TEXT,
    "city" TEXT NOT NULL,
    "region" TEXT NOT NULL,
    "country" TEXT NOT NULL,
    "zip" INTEGER NOT NULL,
    "payableType" "PAYABLETYPE" NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "PayableDetails_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "PointsWithdrawalRequest" (
    "id" TEXT NOT NULL,
    "ticketId" INTEGER NOT NULL,
    "amount" INTEGER NOT NULL,
    "scopeInfo" JSONB NOT NULL,
    "payableInfo" JSONB NOT NULL,
    "requesterInfo" JSONB NOT NULL,
    "pointsType" TEXT NOT NULL,
    "expactedWithdrawalDate" TIMESTAMP(3) NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "PointsWithdrawalRequest_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "PayableDetails_campaignId_key" ON "PayableDetails"("campaignId");

-- CreateIndex
CREATE UNIQUE INDEX "PayableDetails_scopeId_key" ON "PayableDetails"("scopeId");

-- CreateIndex
CREATE UNIQUE INDEX "PayableDetails_identityId_key" ON "PayableDetails"("identityId");

-- CreateIndex
CREATE UNIQUE INDEX "PointsWithdrawalRequest_ticketId_key" ON "PointsWithdrawalRequest"("ticketId");
