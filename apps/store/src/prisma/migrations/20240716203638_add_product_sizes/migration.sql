/*
  Warnings:

  - The values [II_XL,III_XL,IV_XL,Y_II_XL,Y_III_XL] on the enum `PRODUCT_SIZE` will be removed. If these variants are still used in the database, this will fail.
  - Changed the type of `logo_position` on the `products` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.
  - Changed the type of `size` on the `products` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.

*/
-- CreateEnum
CREATE TYPE "LOGO_POSITION" AS ENUM ('FRONT_CENTER', 'FRONT_LEFT_ARM', 'FRONT_LEFT_CHEST', 'FRONT_LEFT_LEG', 'FRONT_RIGHT_ARM', 'FRONT_RIGHT_CHEST', 'FRONT_RIGHT_LEG');

-- AlterEnum
BEGIN;
CREATE TYPE "PRODUCT_SIZE_new" AS ENUM ('XS', 'S', 'M', 'L', 'XL', 'XXL', 'XXXL', 'XXXXL', 'YXS', 'YS', 'YM', 'YL', 'YXL', 'YXXL', 'YXXXL', 'OSFA', 'NO_SIZE');
ALTER TABLE "products" ALTER COLUMN "size" TYPE "PRODUCT_SIZE_new" USING ("size"::text::"PRODUCT_SIZE_new");
ALTER TYPE "PRODUCT_SIZE" RENAME TO "PRODUCT_SIZE_old";
ALTER TYPE "PRODUCT_SIZE_new" RENAME TO "PRODUCT_SIZE";
DROP TYPE "PRODUCT_SIZE_old";
COMMIT;

-- AlterTable
ALTER TABLE "orders" ALTER COLUMN "id" SET DEFAULT CONCAT('so_', RIGHT(uuid_generate_v4()::text, 12));

-- AlterTable
ALTER TABLE "products" ALTER COLUMN "id" SET DEFAULT CONCAT('pdct_', RIGHT(uuid_generate_v4()::text, 12)),
DROP COLUMN "logo_position",
ADD COLUMN     "logo_position" "LOGO_POSITION" NOT NULL,
DROP COLUMN "size",
ADD COLUMN     "size" "PRODUCT_SIZE" NOT NULL;
