-- CreateEnum
CREATE TYPE "VENDOR" AS ENUM ('marco', 'store', 'raise', 'raise_marco');

-- CreateEnum
CREATE TYPE "CARRIER" AS ENUM ('dhl', 'ups', 'fedex', 'usps', 'osn');

-- CreateEnum
CREATE TYPE "PRODUCT_SIZE" AS ENUM ('XS', 'S', 'M', 'L', 'XL', 'II_XL', 'III_XL', 'IV_XL', 'YXS', 'YS', 'YM', 'YL', 'YXL', 'Y_II_XL', 'Y_III_XL');

-- CreateEnum
CREATE TYPE "LOGO_POSITION" AS ENUM ('front', 'back');

-- CreateTable
CREATE TABLE "orders" (
    "id" TEXT NOT NULL,
    "schedule_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "vendor" "VENDOR" NOT NULL,
    "carrier" "CARRIER" NOT NULL,
    "ship_to" TEXT NOT NULL,
    "street" TEXT NOT NULL,
    "city" TEXT NOT NULL,
    "state" TEXT NOT NULL,
    "zip_code" VARCHAR(5) NOT NULL,
    "packing_slip_id" TEXT NOT NULL,
    "packing_slip_title" TEXT NOT NULL,
    "netsuite_id" TEXT NOT NULL,

    CONSTRAINT "orders_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "products" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "size" "PRODUCT_SIZE" NOT NULL,
    "sku" TEXT NOT NULL,
    "logo" TEXT NOT NULL,
    "logo_position" "LOGO_POSITION" NOT NULL,
    "receiver_name" TEXT NOT NULL,

    CONSTRAINT "products_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "orders_products" (
    "id" TEXT NOT NULL,
    "order_id" TEXT NOT NULL,
    "product_id" TEXT NOT NULL,

    CONSTRAINT "orders_products_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "products_sku_key" ON "products"("sku");

-- CreateIndex
CREATE INDEX "idx_orders_products_order_product" ON "orders_products"("order_id", "product_id");

-- AddForeignKey
ALTER TABLE "orders_products" ADD CONSTRAINT "orders_products_order_id_fkey" FOREIGN KEY ("order_id") REFERENCES "orders"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "orders_products" ADD CONSTRAINT "orders_products_product_id_fkey" FOREIGN KEY ("product_id") REFERENCES "products"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
