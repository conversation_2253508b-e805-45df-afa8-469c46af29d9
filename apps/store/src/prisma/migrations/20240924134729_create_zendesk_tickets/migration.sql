-- CreateEnum
CREATE TYPE "ZENDESK_TICKET_STATUS" AS ENUM ('OPEN', 'CLOSED');

-- CreateTable
CREATE TABLE "zendesk_tickets" (
    "id" TEXT NOT NULL DEFAULT CONCAT('storezd_', RIGHT(uuid_generate_v4()::text, 12)),
    "fundraiser_id" INTEGER NOT NULL,
    "status" "ZENDESK_TICKET_STATUS" NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "zendesk_tickets_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "zendesk_tickets_fundraiser_id_key" ON "zendesk_tickets"("fundraiser_id");
