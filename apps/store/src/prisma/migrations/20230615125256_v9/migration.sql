/*
  Warnings:

  - You are about to drop the `Customer` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `CustomersOnRoles` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `Order` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `Permission` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `Product` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `RewardPoint` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `RewardPointActivity` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `Role` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `SsoUser` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `Store` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `UserRole` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `UserRoleOnPermissions` table. If the table is not empty, all the data it contains will be lost.

*/
-- DropForeignKey
ALTER TABLE "Customer" DROP CONSTRAINT "Customer_scopeId_fkey";

-- DropForeignKey
ALTER TABLE "CustomersOnRoles" DROP CONSTRAINT "CustomersOnRoles_customerId_fkey";

-- DropForeignKey
ALTER TABLE "CustomersOnRoles" DROP CONSTRAINT "CustomersOnRoles_roleId_fkey";

-- DropForeignKey
ALTER TABLE "RewardPoint" DROP CONSTRAINT "RewardPoint_customerId_fkey";

-- DropForeignKey
ALTER TABLE "RewardPointActivity" DROP CONSTRAINT "RewardPointActivity_customerId_fkey";

-- DropForeignKey
ALTER TABLE "RewardPointActivity" DROP CONSTRAINT "RewardPointActivity_scopeId_fkey";

-- DropForeignKey
ALTER TABLE "SsoUser" DROP CONSTRAINT "SsoUser_userRoleId_fkey";

-- DropForeignKey
ALTER TABLE "UserRoleOnPermissions" DROP CONSTRAINT "UserRoleOnPermissions_permissionId_fkey";

-- DropForeignKey
ALTER TABLE "UserRoleOnPermissions" DROP CONSTRAINT "UserRoleOnPermissions_userRoleId_fkey";

-- DropTable
DROP TABLE "Customer";

-- DropTable
DROP TABLE "CustomersOnRoles";

-- DropTable
DROP TABLE "Order";

-- DropTable
DROP TABLE "Permission";

-- DropTable
DROP TABLE "Product";

-- DropTable
DROP TABLE "RewardPoint";

-- DropTable
DROP TABLE "RewardPointActivity";

-- DropTable
DROP TABLE "Role";

-- DropTable
DROP TABLE "SsoUser";

-- DropTable
DROP TABLE "Store";

-- DropTable
DROP TABLE "UserRole";

-- DropTable
DROP TABLE "UserRoleOnPermissions";

-- DropEnum
DROP TYPE "OrdersStatus";

-- DropEnum
DROP TYPE "OrdersType";

-- DropEnum
DROP TYPE "RewardActivityStatus";
