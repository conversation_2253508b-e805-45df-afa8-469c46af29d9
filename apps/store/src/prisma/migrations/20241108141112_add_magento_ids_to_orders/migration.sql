/*
  Warnings:

  - A unique constraint covering the columns `[magento_order_id]` on the table `orders` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[magento_confirmation_id]` on the table `orders` will be added. If there are existing duplicate values, this will fail.

*/
-- AlterTable
ALTER TABLE "dead_letter_orders" ALTER COLUMN "orderId" SET DEFAULT CONCAT('dlo_', RIGHT(uuid_generate_v4()::text, 12));

-- AlterTable
ALTER TABLE "orders" ADD COLUMN     "magento_confirmation_id" TEXT,
ADD COLUMN     "magento_order_id" TEXT,
ALTER COLUMN "id" SET DEFAULT CONCAT('so_', RIGHT(uuid_generate_v4()::text, 12));

-- AlterTable
ALTER TABLE "products" ALTER COLUMN "id" SET DEFAULT CONCAT('pdct_', RIGHT(uuid_generate_v4()::text, 12));

-- AlterTable
ALTER TABLE "stores" ALTER COLUMN "id" SET DEFAULT CONCAT('store_', RIGHT(uuid_generate_v4()::text, 12));

-- AlterTable
ALTER TABLE "zendesk_tickets" ALTER COLUMN "id" SET DEFAULT CONCAT('storezd_', RIGHT(uuid_generate_v4()::text, 12));

-- CreateIndex
CREATE UNIQUE INDEX "orders_magento_order_id_key" ON "orders"("magento_order_id");

-- CreateIndex
CREATE UNIQUE INDEX "orders_magento_confirmation_id_key" ON "orders"("magento_confirmation_id");
