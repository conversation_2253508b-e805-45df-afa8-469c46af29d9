-- AlterTable
ALTER TABLE "dead_letter_orders" ALTER COLUMN "orderId" SET DEFAULT CONCAT('dlo_', RIGHT(uuid_generate_v4()::text, 12));

-- AlterTable
ALTER TABLE "legacy_orders" RENAME CONSTRAINT "orders_pkey" TO "legacy_orders_pkey";
ALTER TABLE "legacy_orders" ADD COLUMN     "gift_card_amount" INTEGER;

-- AlterTable
ALTER TABLE "products" ALTER COLUMN "id" SET DEFAULT CONCAT('pdct_', RIGHT(uuid_generate_v4()::text, 12));

-- AlterTable
ALTER TABLE "stores" ALTER COLUMN "id" SET DEFAULT CONCAT('store_', RIGHT(uuid_generate_v4()::text, 12));

-- AlterTable
ALTER TABLE "zendesk_tickets" ALTER COLUMN "id" SET DEFAULT CONCAT('storezd_', RIGHT(uuid_generate_v4()::text, 12));

-- RenameIndex
ALTER INDEX "orders_external_id_key" RENAME TO "legacy_orders_external_id_key";

-- RenameIndex
ALTER INDEX "orders_magento_confirmation_id_key" RENAME TO "legacy_orders_magento_confirmation_id_key";

-- RenameIndex
ALTER INDEX "orders_magento_order_id_key" RENAME TO "legacy_orders_magento_order_id_key";

-- RenameIndex
ALTER INDEX "orders_netsuite_id_key" RENAME TO "legacy_orders_netsuite_id_key";
