-- AlterEnum
-- This migration adds more than one value to an enum.
-- With PostgreSQL versions 11 and earlier, this is not possible
-- in a single migration. This can be worked around by creating
-- multiple migrations, each migration adding only one value to
-- the enum.


ALTER TYPE "STATE" ADD VALUE 'AA';
ALTER TYPE "STATE" ADD VALUE 'AE';
ALTER TYPE "STATE" ADD VALUE 'AP';
ALTER TYPE "STATE" ADD VALUE 'AS';
ALTER TYPE "STATE" ADD VALUE 'FM';
ALTER TYPE "STATE" ADD VALUE 'GU';
ALTER TYPE "STATE" ADD VALUE 'MH';
ALTER TYPE "STATE" ADD VALUE 'MP';
ALTER TYPE "STATE" ADD VALUE 'PW';
ALTER TYPE "STATE" ADD VALUE 'TT';
ALTER TYPE "STATE" ADD VALUE 'VI';
