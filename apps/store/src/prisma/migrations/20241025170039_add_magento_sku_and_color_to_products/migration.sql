-- AlterTable
ALTER TABLE "dead_letter_orders" ALTER COLUMN "orderId" SET DEFAULT CONCAT('dlo_', RIGHT(uuid_generate_v4()::text, 12));

-- AlterTable
ALTER TABLE "orders" ALTER COLUMN "id" SET DEFAULT CONCAT('so_', RIGHT(uuid_generate_v4()::text, 12));

-- AlterTable
ALTER TABLE "products" ADD COLUMN     "color" TEXT,
ADD COLUMN     "magento_sku" TEXT,
ALTER COLUMN "id" SET DEFAULT CONCAT('pdct_', RIGHT(uuid_generate_v4()::text, 12));

-- AlterTable
ALTER TABLE "stores" ALTER COLUMN "id" SET DEFAULT CONCAT('store_', RIGHT(uuid_generate_v4()::text, 12));

-- AlterTable
ALTER TABLE "zendesk_tickets" ALTER COLUMN "id" SET DEFAULT CONCAT('storezd_', RIGHT(uuid_generate_v4()::text, 12));
