-- CreateTable
CREATE TABLE "BuildRequest" (
    "id" TEXT NOT NULL,
    "userSsoId" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "storeRequest" BOOLEAN NOT NULL DEFAULT false,
    "storeName" TEXT,
    "referenceTicket" INTEGER NOT NULL,
    "storeInfo" JSONB NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "BuildRequest_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "BuildRequest_referenceTicket_key" ON "BuildRequest"("referenceTicket");
