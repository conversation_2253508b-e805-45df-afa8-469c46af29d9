/*
  Warnings:

  - The values [dhl,ups,fedex,usps,osn] on the enum `CARRIER` will be removed. If these variants are still used in the database, this will fail.
  - The values [marco,store,raise,raise_marco] on the enum `VENDOR` will be removed. If these variants are still used in the database, this will fail.
  - You are about to drop the column `logo_position` on the `products` table. All the data in the column will be lost.
  - Added the required column `logo_position_Z` to the `products` table without a default value. This is not possible if the table is not empty.

*/
-- CreateEnum
CREATE TYPE "LOGO_POSITION_X" AS ENUM ('LEFT_CHEST', 'RIGHT_CHEST', 'LEFT_LEG', 'RIGHT_LEG', 'LEFT_ARM', 'RIGHT_ARM', 'CENTER', 'NECK');

-- C<PERSON><PERSON>num
CREATE TYPE "LOGO_POSITION_Y" AS ENUM ('SHIRT', 'SHORT', 'ACCESSORIES');

-- CreateEnum
CREATE TYPE "LOGO_POSITION_Z" AS ENUM ('FRONT', 'BACK');

-- AlterEnum
BEGIN;
CREATE TYPE "CARRIER_new" AS ENUM ('DHL', 'UPS', 'FedEx', 'USPS', 'OSN');
ALTER TABLE "orders" ALTER COLUMN "carrier" TYPE "CARRIER_new" USING ("carrier"::text::"CARRIER_new");
ALTER TYPE "CARRIER" RENAME TO "CARRIER_old";
ALTER TYPE "CARRIER_new" RENAME TO "CARRIER";
DROP TYPE "CARRIER_old";
COMMIT;

-- AlterEnum
BEGIN;
CREATE TYPE "VENDOR_new" AS ENUM ('MARCO', 'STORE', 'RAISE', 'RAISE_MARCO');
ALTER TABLE "orders" ALTER COLUMN "vendor" TYPE "VENDOR_new" USING ("vendor"::text::"VENDOR_new");
ALTER TYPE "VENDOR" RENAME TO "VENDOR_old";
ALTER TYPE "VENDOR_new" RENAME TO "VENDOR";
DROP TYPE "VENDOR_old";
COMMIT;

-- AlterTable
ALTER TABLE "products" DROP COLUMN "logo_position",
ADD COLUMN     "logo_position_Z" "LOGO_POSITION_Z" NOT NULL;

-- DropEnum
DROP TYPE "LOGO_POSITION";
