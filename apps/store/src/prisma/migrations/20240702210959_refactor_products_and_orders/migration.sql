CREATE TYPE "STATE" AS ENUM ('AL', 'AK', 'AZ', 'AR', 'CA', 'CO', 'CT', 'DE', 'FL', 'GA', 'HI', 'ID', 'IL', 'IN', 'IA', 'KS', 'KY', 'LA', 'ME', 'MD', 'MA', 'MI', 'MN', 'MS', 'MO', 'MT', 'NE', 'NV', 'NH', 'NJ', 'NM', 'NY', 'NC', 'ND', 'OH', 'OK', 'OR', 'PA', 'RI', 'SC', 'SD', 'TN', 'TX', 'UT', 'VT', 'VA', 'WA', 'WV', 'WI', 'WY');
CREATE TYPE "PRODUCT_SIZE" AS ENUM ('XS', 'S', 'M', 'L', 'XL', 'II_XL', 'III_XL', 'IV_XL', 'YXS', 'YS', 'YM', 'YL', 'YXL', 'Y_II_XL', 'Y_III_XL');
CREATE TYPE "LOGO_POSITION" AS ENUM ('FRONT', 'BACK');

CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TABLE IF EXISTS "orders";

CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

CREATE TABLE "orders" (
    "id" TEXT NOT NULL DEFAULT CONCAT('so_', RIGHT(uuid_generate_v4()::text, 12)),
    "schedule_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "vendor" "VENDOR" NOT NULL,
    "carrier" "CARRIER" NOT NULL,
    "ship_to" TEXT NOT NULL,
    "street" TEXT NOT NULL,
    "city" TEXT NOT NULL,
    "state" "STATE" NOT NULL,
    "zip_code" VARCHAR(5) NOT NULL,
    "packing_slip_id" TEXT NOT NULL,
    "packing_slip_title" TEXT NOT NULL,
    "netsuite_id" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "orders_pkey" PRIMARY KEY ("id")
);

CREATE TRIGGER orders_trigger_updated_at BEFORE UPDATE ON orders FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TABLE "products" (
    "id" TEXT NOT NULL DEFAULT CONCAT('pdct_', RIGHT(uuid_generate_v4()::text, 12)),
    "name" TEXT NOT NULL,
    "sku" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "products_pkey" PRIMARY KEY ("id")
);

CREATE TRIGGER products_trigger_updated_at BEFORE UPDATE ON products FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TABLE "orders_products" (
    "id" TEXT NOT NULL,
    "order_id" TEXT NOT NULL,
    "product_id" TEXT NOT NULL,
    "size" "PRODUCT_SIZE" NOT NULL,
    "logo" TEXT NOT NULL,
    "logo_position" "LOGO_POSITION" NOT NULL,
    "receiver_name" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "orders_products_pkey" PRIMARY KEY ("id")
);

CREATE TRIGGER orders_products_trigger_updated_at BEFORE UPDATE ON orders_products FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE UNIQUE INDEX "products_sku_key" ON "products"("sku");

CREATE INDEX "idx_orders_products_order_product" ON "orders_products"("order_id", "product_id");

ALTER TABLE "orders_products" ADD CONSTRAINT "orders_products_order_id_fkey" FOREIGN KEY ("order_id") REFERENCES "orders"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

ALTER TABLE "orders_products" ADD CONSTRAINT "orders_products_product_id_fkey" FOREIGN KEY ("product_id") REFERENCES "products"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
