/*
  Warnings:

  - A unique constraint covering the columns `[magento_sku]` on the table `products` will be added. If there are existing duplicate values, this will fail.

*/
-- AlterEnum
ALTER TYPE "STATUS" ADD VALUE 'CLOSED';

-- AlterTable
ALTER TABLE "dead_letter_orders" ALTER COLUMN "orderId" SET DEFAULT CONCAT('dlo_', RIGHT(uuid_generate_v4()::text, 12));

-- AlterTable
ALTER TABLE "orders" ALTER COLUMN "id" SET DEFAULT CONCAT('so_', RIGHT(uuid_generate_v4()::text, 12));

-- AlterTable
ALTER TABLE "products" ALTER COLUMN "id" SET DEFAULT CONCAT('pdct_', RIGHT(uuid_generate_v4()::text, 12));

-- AlterTable
ALTER TABLE "stores" ALTER COLUMN "id" SET DEFAULT CONCAT('store_', RIGHT(uuid_generate_v4()::text, 12));

-- AlterTable
ALTER TABLE "zendesk_tickets" ALTER COLUMN "id" SET DEFAULT CONCAT('storezd_', RIGHT(uuid_generate_v4()::text, 12));

-- CreateIndex
CREATE UNIQUE INDEX "products_magento_sku_key" ON "products"("magento_sku");
