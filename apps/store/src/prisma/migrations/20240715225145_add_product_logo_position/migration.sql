/*
  Warnings:

  - You are about to drop the column `logo_position` on the `orders_products` table. All the data in the column will be lost.
  - Added the required column `logo_position` to the `products` table without a default value. This is not possible if the table is not empty.
  - Changed the type of `size` on the `products` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.

*/
-- AlterTable
ALTER TABLE "orders" ALTER COLUMN "id" SET DEFAULT CONCAT('so_', RIGHT(uuid_generate_v4()::text, 12));

-- AlterTable
ALTER TABLE "orders_products" DROP COLUMN "logo_position";

-- AlterTable
ALTER TABLE "products" ADD COLUMN     "logo_position" TEXT NOT NULL,
ALTER COLUMN "id" SET DEFAULT CONCAT('pdct_', RIGHT(uuid_generate_v4()::text, 12)),
DROP COLUMN "size",
ADD COLUMN     "size" TEXT NOT NULL;

-- DropEnum
DROP TYPE "LOGO_POSITION";
