/*
  Warnings:

  - A unique constraint covering the columns `[external_id]` on the table `orders` will be added. If there are existing duplicate values, this will fail.

*/
-- AlterTable
ALTER TABLE "orders" ADD COLUMN     "external_id" TEXT,
ALTER COLUMN "id" SET DEFAULT CONCAT('so_', RIGHT(uuid_generate_v4()::text, 12));

-- AlterTable
ALTER TABLE "products" ALTER COLUMN "id" SET DEFAULT CONCAT('pdct_', RIGHT(uuid_generate_v4()::text, 12));

-- CreateIndex
CREATE UNIQUE INDEX "orders_external_id_key" ON "orders"("external_id");
