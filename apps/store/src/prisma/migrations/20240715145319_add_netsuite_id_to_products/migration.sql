/*
  Warnings:

  - You are about to drop the column `created_at` on the `orders_products` table. All the data in the column will be lost.
  - You are about to drop the column `size` on the `orders_products` table. All the data in the column will be lost.
  - You are about to drop the column `updated_at` on the `orders_products` table. All the data in the column will be lost.
  - You are about to drop the column `created_at` on the `products` table. All the data in the column will be lost.
  - You are about to drop the column `updated_at` on the `products` table. All the data in the column will be lost.
  - A unique constraint covering the columns `[netsuite_id]` on the table `orders` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[netsuite_id]` on the table `products` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `size` to the `products` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE "orders" ALTER COLUMN "id" SET DEFAULT CONCAT('so_', RIGHT(uuid_generate_v4()::text, 12)),
ALTER COLUMN "packing_slip_title" DROP NOT NULL;

-- AlterTable
ALTER TABLE "orders_products" DROP COLUMN "created_at",
DROP COLUMN "size",
DROP COLUMN "updated_at";

-- AlterTable
ALTER TABLE "products" DROP COLUMN "created_at",
DROP COLUMN "updated_at",
ADD COLUMN     "netsuite_id" TEXT,
ADD COLUMN     "size" "PRODUCT_SIZE" NOT NULL,
ALTER COLUMN "id" SET DEFAULT CONCAT('pdct_', RIGHT(uuid_generate_v4()::text, 12));

-- CreateIndex
CREATE UNIQUE INDEX "orders_netsuite_id_key" ON "orders"("netsuite_id");

-- CreateIndex
CREATE UNIQUE INDEX "products_netsuite_id_key" ON "products"("netsuite_id");
