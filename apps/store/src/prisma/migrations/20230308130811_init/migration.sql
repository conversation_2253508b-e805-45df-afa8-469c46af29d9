-- CreateEnum
CREATE TYPE "RewardActivityStatus" AS ENUM ('Processing', 'Credit', 'Transferred');

-- CreateEnum
CREATE TYPE "OrdersStatus" AS ENUM ('Processing', 'Shipped', 'Complete', 'Cancelled', 'Pending', 'Closed', 'Holded', 'Fraud');

-- CreateEnum
CREATE TYPE "OrdersType" AS ENUM ('ECommerce', 'Store');

-- CreateTable
CREATE TABLE "Store" (
    "id" TEXT NOT NULL,
    "storeId" INTEGER NOT NULL,
    "entityId" INTEGER NOT NULL,
    "name" TEXT NOT NULL,
    "code" TEXT NOT NULL,
    "enabled" INTEGER NOT NULL,
    "status" TEXT,
    "parentStoreId" INTEGER,
    "domain" TEXT NOT NULL,
    "primaryColor" TEXT,
    "favicon" TEXT,
    "headerLogo" TEXT,
    "homepageId" INTEGER,
    "heroSliderVariableId" INTEGER,
    "hatLogo" TEXT,
    "digitalLogo" TEXT,
    "embroideryLogo" TEXT,
    "orderConfirmationEmailTemplate" TEXT,
    "shipmentEmailTemplate" TEXT,
    "stripeCreditCardDescriptor" TEXT,
    "extensionAttributes" JSONB,
    "storeVariablesId" INTEGER NOT NULL,
    "programId" INTEGER NOT NULL,
    "orgId" INTEGER NOT NULL,
    "campaignId" INTEGER NOT NULL,
    "pointsPercentage" INTEGER,
    "accountManagerId" TEXT,
    "salesRepId" TEXT,
    "campaignStartDate" TEXT,
    "campaignEndDate" TEXT,
    "state" TEXT,
    "zipCode" INTEGER,
    "groupLeaderEmail" TEXT,
    "ncesId" INTEGER,
    "teamName" TEXT,
    "secondaryColor" TEXT,
    "activityType" TEXT,
    "additionalAttributes" JSONB,
    "storeName" TEXT,
    "raiseId" INTEGER,
    "city" TEXT,
    "organizationName" TEXT,
    "qrCode" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "Store_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Customer" (
    "id" TEXT NOT NULL,
    "customerId" INTEGER NOT NULL,
    "firstName" TEXT,
    "lastName" TEXT,
    "email" TEXT NOT NULL,
    "password" TEXT,
    "createdIn" TEXT,
    "addresses" JSONB[],
    "storeId" INTEGER,
    "storeIdRef" INTEGER,
    "websiteId" INTEGER,
    "scopeId" INTEGER,
    "groupId" INTEGER,
    "extensionAttributes" JSONB,
    "customAttributes" JSONB[],
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "Customer_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Role" (
    "id" TEXT NOT NULL,
    "name" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "Role_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "CustomersOnRoles" (
    "customerId" INTEGER NOT NULL,
    "roleId" TEXT NOT NULL,
    "isPrimary" BOOLEAN,
    "assignedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "assignedBy" TEXT NOT NULL,

    CONSTRAINT "CustomersOnRoles_pkey" PRIMARY KEY ("customerId","roleId")
);

-- CreateTable
CREATE TABLE "RewardPoint" (
    "id" TEXT NOT NULL,
    "rewardpointId" INTEGER,
    "customerId" INTEGER,
    "customerIdRef" INTEGER,
    "customerBalanceId" INTEGER,
    "points" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "websiteId" INTEGER,
    "isSync" BOOLEAN,
    "expirationDate" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "RewardPoint_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "RewardPointActivity" (
    "id" TEXT NOT NULL,
    "transactionId" INTEGER,
    "customerId" INTEGER,
    "customerIdRef" INTEGER,
    "date" TIMESTAMP(3),
    "customerBalanceId" INTEGER,
    "storeId" INTEGER,
    "storeIdRef" INTEGER,
    "scopeId" INTEGER,
    "pointsBalance" DOUBLE PRECISION,
    "pointsDelta" TEXT,
    "status" "RewardActivityStatus",
    "type" TEXT,
    "eventCode" TEXT,
    "eventData" JSONB,
    "comment" TEXT,
    "websiteId" INTEGER,
    "isChecked" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "RewardPointActivity_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Order" (
    "id" TEXT NOT NULL,
    "orderId" INTEGER NOT NULL,
    "incrementId" VARCHAR(32),
    "storeId" INTEGER,
    "customerFirstName" TEXT,
    "customerLastName" TEXT,
    "customerEmail" TEXT,
    "customerId" INTEGER,
    "type" "OrdersType" NOT NULL DEFAULT 'ECommerce',
    "items" JSONB[],
    "subtotal" DOUBLE PRECISION,
    "tax" DOUBLE PRECISION,
    "total" DOUBLE PRECISION,
    "scopeId" INTEGER,
    "status" "OrdersStatus" NOT NULL DEFAULT 'Processing',
    "isChecked" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "Order_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Ticket" (
    "id" TEXT NOT NULL,
    "ticketId" INTEGER NOT NULL,
    "storeId" INTEGER,
    "subject" TEXT,
    "message" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "Ticket_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Transaction" (
    "id" TEXT NOT NULL,
    "userSsoId" TEXT,
    "email" TEXT,
    "storeId" INTEGER,
    "transactionId" TEXT,
    "amount" DOUBLE PRECISION,
    "points" DOUBLE PRECISION,
    "status" TEXT,
    "transaction" JSONB NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "Transaction_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Product" (
    "id" TEXT NOT NULL,
    "productId" INTEGER NOT NULL,
    "storeId" INTEGER,
    "sku" TEXT,
    "name" TEXT,
    "attributeSetId" INTEGER,
    "price" INTEGER,
    "status" BOOLEAN,
    "visibility" INTEGER,
    "typeId" TEXT,
    "weight" DOUBLE PRECISION,
    "extensionAttributes" JSONB,
    "productLinks" JSONB[],
    "options" JSONB[],
    "mediaGalleryEntries" JSONB[],
    "tierPrices" JSONB[],
    "customAttributes" JSONB[],
    "createdAt" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "Product_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "SsoUser" (
    "id" TEXT NOT NULL,
    "firstName" TEXT,
    "lastName" TEXT,
    "email" TEXT,
    "userSsoId" TEXT,
    "campaigns" JSONB[],
    "userRoleId" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "SsoUser_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "UserRole" (
    "id" TEXT NOT NULL,
    "title" VARCHAR(255),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "UserRole_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "UserRoleOnPermissions" (
    "userRoleId" TEXT NOT NULL,
    "permissionId" TEXT NOT NULL,
    "assignedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "assignedBy" TEXT NOT NULL,

    CONSTRAINT "UserRoleOnPermissions_pkey" PRIMARY KEY ("userRoleId","permissionId")
);

-- CreateTable
CREATE TABLE "Permission" (
    "id" TEXT NOT NULL,
    "name" VARCHAR(255),
    "value" VARCHAR(255),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "Permission_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "Store_storeId_key" ON "Store"("storeId");

-- CreateIndex
CREATE UNIQUE INDEX "Store_code_key" ON "Store"("code");

-- CreateIndex
CREATE UNIQUE INDEX "Customer_customerId_key" ON "Customer"("customerId");

-- CreateIndex
CREATE UNIQUE INDEX "Customer_email_key" ON "Customer"("email");

-- CreateIndex
CREATE UNIQUE INDEX "RewardPoint_rewardpointId_key" ON "RewardPoint"("rewardpointId");

-- CreateIndex
CREATE UNIQUE INDEX "RewardPoint_customerId_key" ON "RewardPoint"("customerId");

-- CreateIndex
CREATE UNIQUE INDEX "RewardPoint_customerIdRef_key" ON "RewardPoint"("customerIdRef");

-- CreateIndex
CREATE UNIQUE INDEX "RewardPointActivity_transactionId_key" ON "RewardPointActivity"("transactionId");

-- CreateIndex
CREATE UNIQUE INDEX "Order_orderId_key" ON "Order"("orderId");

-- CreateIndex
CREATE UNIQUE INDEX "Ticket_ticketId_key" ON "Ticket"("ticketId");

-- CreateIndex
CREATE UNIQUE INDEX "Product_productId_key" ON "Product"("productId");

-- CreateIndex
CREATE UNIQUE INDEX "SsoUser_email_key" ON "SsoUser"("email");

-- CreateIndex
CREATE UNIQUE INDEX "SsoUser_userSsoId_key" ON "SsoUser"("userSsoId");

-- AddForeignKey
ALTER TABLE "CustomersOnRoles" ADD CONSTRAINT "CustomersOnRoles_customerId_fkey" FOREIGN KEY ("customerId") REFERENCES "Customer"("customerId") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CustomersOnRoles" ADD CONSTRAINT "CustomersOnRoles_roleId_fkey" FOREIGN KEY ("roleId") REFERENCES "Role"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "RewardPoint" ADD CONSTRAINT "RewardPoint_customerId_fkey" FOREIGN KEY ("customerId") REFERENCES "Customer"("customerId") ON DELETE SET NULL ON UPDATE SET NULL;

-- AddForeignKey
ALTER TABLE "RewardPointActivity" ADD CONSTRAINT "RewardPointActivity_customerId_fkey" FOREIGN KEY ("customerId") REFERENCES "Customer"("customerId") ON DELETE SET NULL ON UPDATE SET NULL;

-- AddForeignKey
ALTER TABLE "SsoUser" ADD CONSTRAINT "SsoUser_userRoleId_fkey" FOREIGN KEY ("userRoleId") REFERENCES "UserRole"("id") ON DELETE SET NULL ON UPDATE SET NULL;

-- AddForeignKey
ALTER TABLE "UserRoleOnPermissions" ADD CONSTRAINT "UserRoleOnPermissions_userRoleId_fkey" FOREIGN KEY ("userRoleId") REFERENCES "UserRole"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "UserRoleOnPermissions" ADD CONSTRAINT "UserRoleOnPermissions_permissionId_fkey" FOREIGN KEY ("permissionId") REFERENCES "Permission"("id") ON DELETE CASCADE ON UPDATE CASCADE;
