/*
  Warnings:

  - A unique constraint covering the columns `[entityId]` on the table `Store` will be added. If there are existing duplicate values, this will fail.

*/
-- AlterTable
ALTER TABLE "Customer" ADD COLUMN     "scopeIdRef" INTEGER;

-- CreateIndex
CREATE UNIQUE INDEX "Store_entityId_key" ON "Store"("entityId");

-- AddForeignKey
ALTER TABLE "Customer" ADD CONSTRAINT "Customer_scopeId_fkey" FOREIGN KEY ("scopeId") REFERENCES "Store"("entityId") ON DELETE SET NULL ON UPDATE SET NULL;
