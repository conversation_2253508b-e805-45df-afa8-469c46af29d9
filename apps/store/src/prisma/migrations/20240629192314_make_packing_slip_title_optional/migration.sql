/*
  Warnings:

  - You are about to drop the `orders_products` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `products` table. If the table is not empty, all the data it contains will be lost.

*/
-- DropForeignKey
ALTER TABLE "orders_products" DROP CONSTRAINT "orders_products_order_id_fkey";

-- DropForeignKey
ALTER TABLE "orders_products" DROP CONSTRAINT "orders_products_product_id_fkey";

-- AlterTable
ALTER TABLE "orders" ADD COLUMN     "products" JSONB[],
ALTER COLUMN "packing_slip_title" DROP NOT NULL;

-- DropTable
DROP TABLE "orders_products";

-- DropTable
DROP TABLE "products";

-- DropEnum
DROP TYPE "LOGO_POSITION_X";

-- DropEnum
DROP TYPE "LOGO_POSITION_Y";

-- DropEnum
DROP TYPE "LOGO_POSITION_Z";

-- DropEnum
DROP TYPE "PRODUCT_SIZE";
