/*
  Warnings:

  - The values [OSN] on the enum `CAR<PERSON><PERSON>` will be removed. If these variants are still used in the database, this will fail.
  - A unique constraint covering the columns `[netsuite_id]` on the table `orders` will be added. If there are existing duplicate values, this will fail.

*/
-- AlterEnum
BEGIN;
CREATE TYPE "CARRIER_new" AS ENUM ('DHL', 'UPS', 'FedEx', 'USPS', 'OSM');
ALTER TABLE "orders" ALTER COLUMN "carrier" TYPE "CARRIER_new" USING ("carrier"::text::"CARRIER_new");
ALTER TYPE "CARRIER" RENAME TO "CARRIER_old";
ALTER TYPE "CARRIER_new" RENAME TO "CARRIER";
DROP TYPE "CARRIER_old";
COMMIT;

-- CreateIndex
CREATE UNIQUE INDEX "orders_netsuite_id_key" ON "orders"("netsuite_id");
