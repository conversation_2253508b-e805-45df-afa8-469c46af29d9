-- CreateEnum
CREATE TYPE "DEAD_LETTER_ORDER_STATUS" AS ENUM ('FAILED_DESIGN');

-- CreateTable
CREATE TABLE "dead_letter_orders" (
    "orderId" TEXT NOT NULL DEFAULT CONCAT('dlo_', RIGHT(uuid_generate_v4()::text, 12)),
    "status" "DEAD_LETTER_ORDER_STATUS" NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "dead_letter_orders_pkey" PRIMARY KEY ("orderId")
);
