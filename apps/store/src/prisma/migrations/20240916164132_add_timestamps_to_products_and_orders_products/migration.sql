-- AlterTable
ALTER TABLE "orders" ALTER COLUMN "id" SET DEFAULT CONCAT('so_', RIGHT(uuid_generate_v4()::text, 12));

-- AlterTable
ALTER TABLE "orders_products" ADD COLUMN     "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP;

-- AlterTable
ALTER TABLE "products" ADD COLUMN     "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ALTER COLUMN "id" SET DEFAULT CONCAT('pdct_', RIGHT(uuid_generate_v4()::text, 12));

-- AlterTable
ALTER TABLE "stores" ALTER COLUMN "id" SET DEFAULT CONCAT('store_', RIGHT(uuid_generate_v4()::text, 12));
