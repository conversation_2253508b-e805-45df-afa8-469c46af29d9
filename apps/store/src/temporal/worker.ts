/* eslint-disable functional/no-let */
/* eslint-disable max-statements, complexity, max-len, import/no-unused-modules */
import * as path from "path";
import { NativeConnection, Worker } from "@temporalio/worker";

import Sentry from "@store-monorepo/sentry";
import Slack from "@store-monorepo/slack-api";

import Activities from "./workflows/activities";
import Temporal from ".";

const isIgnorableErrorMessage = (message: string) => {
  // Handle non-slack exceptions
  if (message.startsWith("HTTP error!")) {
    const messageBody = message.split("message: ");
    try {
      const messageJson = JSON.parse(messageBody[1]);
      if (messageJson?.error?.message === "The record was not found.") {
        console.log(`Unhandled Exception - Slack bypassed - ${message}`);
        return true;
      }
    } catch (_e) {
      return false;
    }
  } else if (message === "Split SDK emitted SDK_READY_TIMED_OUT event.") {
    console.log(`Unhandled Exception - Slack bypassed - ${message}`);
    return true;
  } else {
    return false;
  }
  return false;
};

// eslint-disable-next-line sonarjs/cognitive-complexity
(async function start() {
  const SLACK_ORDER_CREATION_NOTIFICATION_CHANNEL = process.env.SLACK_ORDER_CREATION_NOTIFICATION_CHANNEL || "";
  const NODE_ENV = process.env.NODE_ENV || "production";

  try {
    const connection = await NativeConnection.connect({ address: process.env.TEMPORAL_URL });

    const worker = await Worker.create({
      connection,
      workflowsPath: path.resolve(__dirname, "./workflows"),
      activities: {
        ...Activities.logo,
        ...Activities.feature,
        ...Activities.magento,
        ...Activities.medusa,
        ...Activities.netsuite,
        ...Activities.notification,
        ...Activities.order,
        ...Activities.store,
        ...Activities.ticket,
        ...Activities.transaction,
        ...Activities.donors
      },
      taskQueue: Temporal.TASK_QUEUE,
      maxConcurrentWorkflowTaskExecutions: 1,
      maxConcurrentActivityTaskExecutions: 1,
      interceptors: {
        activityInbound: [
          () => ({
            async execute(input, next) {
              try {
                return await next(input);
              } catch (error) {
                const workflowId = typeof input.args?.[0] === "object" && input.args[0] !== null && "workflowId" in input.args[0]
                  ? (input.args[0] as { workflowId: string }).workflowId
                  : undefined;
                if (SLACK_ORDER_CREATION_NOTIFICATION_CHANNEL === "") {
                  console.log("Slack message unable to send with empty channel name", error.message);
                } else {
                  if (isIgnorableErrorMessage(error.message)) {
                    throw error;
                  }
                  let attachmentFields = [
                    { title: "Response", value: Slack.codeBlock(error.message) },
                    { title: "Environment", value: NODE_ENV, short: true }
                  ];
                  if (workflowId) {
                    attachmentFields = [
                      { title: "Workflow Id", value: workflowId, short: true },
                      ...attachmentFields
                    ];
                  }
                  console.log(`Unhandled activity error in workflow`, attachmentFields);
                  // Await Slack.postMessage({
                  //   channel: SLACK_ORDER_CREATION_NOTIFICATION_CHANNEL,
                  //   text: `⚠️ Unhandled activity error in workflow`,
                  //   attachments: [{
                  //     color: NODE_ENV === "production" ? "danger" : "warning",
                  //     fields: attachmentFields
                  //   }]
                  // });
                }
                throw error;
              }
            },
          }),
        ],
      }
    });

    await worker.run();
  } catch (error) {
    Sentry.captureException(error);
    console.error("Error starting worker", error);
    process.exit(1);
  }
})();
