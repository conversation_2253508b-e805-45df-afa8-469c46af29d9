import {
  Connection,
  Client,
  WorkflowExecutionAlreadyStartedError,
  WorkflowFailedError
} from "@temporalio/client";

const TASK_QUEUE = process.env.TASK_QUEUE || "store";
let clientInstance : Client | null;

const client = async () => {
  return clientInstance ||= (await createClient());
};

const createClient = async () => {
  const connection = await Connection.connect({ address: process.env.TEMPORAL_URL });
  return new Client({ connection });
};

const terminateCron = async (client: Client, workflowId: string): Promise<void> => {
  const workflow = client.workflow.getHandle(workflowId);
  await workflow.terminate();
};

const cancelWorkflow = async (client: Client, workflowId: string) => {
  const workflow = client.workflow.getHandle(workflowId);
  await workflow.cancel();
};

const alreadyStartedError = WorkflowExecutionAlreadyStartedError;

const workflowFailedError = WorkflowFailedError;

const temporalModule: {
  createClient: () => Promise<Client>,
  cancelWorkflow: (client: Client, workflowId: string) => Promise<void>,
  terminateCron: (client: Client, workflowId: string) => Promise<void>,
  alreadyStartedError: typeof WorkflowExecutionAlreadyStartedError,
  workflowFailedError: typeof WorkflowFailedError,
  TASK_QUEUE: string,
  client: () => Promise<Client>
} = {
  createClient,
  cancelWorkflow,
  terminateCron,
  alreadyStartedError,
  workflowFailedError,
  TASK_QUEUE,
  client
};
export default temporalModule;
