# Store Temporal Set up

## Schedulers

Dispatcher `otfRecapFundraiserEmailDispatcherWorkflow` ([source](https://github.com/snap-mobile/fundraisers/tree/main/packages/workflows/otf-recap-fundraiser-email-dispatcher))

```bash
temporal schedule create \
  --schedule-id 'otf-recap-email-dispatcher-workflow' \
  --workflow-id 'otf-recap-email-dispatcher-workflow' \
  --workflow-type 'otfRecapFundraiserEmailDispatcherWorkflow' \
  --task-queue 'fundraisers-queue' \
  --overlap-policy 'TerminateOther' \
  --cron '0 20 * * *'
```
