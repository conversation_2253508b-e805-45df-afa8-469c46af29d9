import { WorkflowStartOptions } from "@temporalio/client/lib/workflow-options";
import Temporal from "..";

const WORKFLOW_ID = "orgs-sample-cron";
const CRON_SCHEDULE = "15 8 * * *";

const cronOptions: WorkflowStartOptions = {
  taskQueue: Temporal.TASK_QUEUE,
  workflowId: WORKFLOW_ID,
  cronSchedule: CRON_SCHEDULE,
  args: []
};

const cronModule = {
  // eslint-disable-next-line @typescript-eslint/no-empty-function
  workflow: async (): Promise<void> => {},
  options: cronOptions
};
export default cronModule;
