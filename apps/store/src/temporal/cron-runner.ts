import { Client, WorkflowStartOptions } from "@temporalio/client";
import CronJobs from "./cron-jobs";
import Temporal from ".";

type Cron = {
  workflow: () => Promise<void>;
  options: WorkflowStartOptions;
};

const runAll = async () => {
  const client = await Temporal.createClient();
  CronJobs.forEach(async (cron) => {
    const workflowId = cron.options.workflowId;
    try {
      console.log(`Running Cron: '${workflowId}'`);
      await initiateCron(client, cron, workflowId);
    } catch (error) {
      if (error instanceof Temporal.alreadyStartedError) {
        await terminateAndRestart(client, cron, workflowId);
      } else if (error instanceof Temporal.workflowFailedError) {
        console.log(`${workflowId}: ${error}`);
      } else {
        console.error(`Unexpected error running ${workflowId}: ${error}`);
      }
    }
  });
};

const addCancellationHook = () => {
  process.on("SIGINT", async () => {
    const client = await Temporal.createClient();
    CronJobs.forEach(async (cron) => {
      const { workflowId } = cron.options;
      await Temporal.terminateCron(client, workflowId);
      console.log(`Terminated Cron: '${workflowId}}'`);
      process.exit(0);
    });
  });
};

const terminateAndRestart = async (
  client: Client,
  cron: Cron,
  workflowId: string
) => {
  try {
    console.log(
      `Workflow currently running. Terminating and restarting ${workflowId}.`
    );
    await Temporal.terminateCron(client, workflowId);
  } catch (error) {
    if (error instanceof Temporal.workflowFailedError) {
      console.log(`Workflow ${workflowId} terminated: ${error}`);
    } else {
      console.error(`Unexpected error restarting ${workflowId}: ${error}`);
    }
  } finally {
    await initiateCron(client, cron, workflowId);
  }
};

const initiateCron = async (client: Client, cron: Cron, workflowId: string) => {
  await client.workflow.start(cron.workflow, cron.options);
  console.log(`Started workflow ${workflowId}`);
};

const cronRunnerModule = { runAll, addCancellationHook };
export default cronRunnerModule;
