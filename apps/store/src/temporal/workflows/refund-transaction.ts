/* eslint-disable import/prefer-default-export */
import { proxyActivities } from "@temporalio/workflow";
import Activities from "./activities";
import { MagentoRefundPointsLiability } from "./activities/transaction";

const {
  processRefundPointsTransaction
} = proxyActivities<typeof Activities.transaction>({
  startToCloseTimeout: "1 minute",
  retry: {
    maximumAttempts: 1
  }
});

export const handleRefundTransaction = async (params: MagentoRefundPointsLiability): Promise<void> => {
  await processRefundPointsTransaction(params);
};
