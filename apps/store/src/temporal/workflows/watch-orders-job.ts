import { proxyActivities } from "@temporalio/workflow";

import Activities from "./activities";

const { findPastDueOrders } = proxyActivities<typeof Activities.order>({
  startToCloseTimeout: "1 minute",
  retry: {
    maximumAttempts: 1
  }
});

const { notifyPastDueOrder } = proxyActivities<typeof Activities.notification>({
  startToCloseTimeout: "1 minute",
  retry: {
    maximumAttempts: 1
  }
});

export const watchOrdersJob = async () => {
  const orders = await findPastDueOrders();

  if (orders.length > 0) {
    return await Promise.all(orders.map(async order => notifyPastDueOrder(order)));
  }

  return null;
};
