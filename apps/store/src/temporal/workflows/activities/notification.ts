import Prisma from "@prisma/client";
import Slack from "@store-monorepo/slack-api";

const SLACK_ORDER_CREATION_NOTIFICATION_CHANNEL = process.env.SLACK_ORDER_CREATION_NOTIFICATION_CHANNEL || "";
const NODE_ENV = process.env.NODE_ENV || "production";

const notifyPastDueOrder = async (order: Prisma.Order) => {
  return Slack.postMessage({
    channel: SLACK_ORDER_CREATION_NOTIFICATION_CHANNEL,
    text: `*Order Past Due ${order.netsuiteId} ${order.vendor}*`,
    attachments: [{
      color: NODE_ENV === "production" ? "danger" : "warning",
      fields: [
        { title: "Order ID", value: order.id, short: true },
        { title: "NetSuite SO ID", value: order.netsuiteId, short: true },
        { title: "Schedule At", value: order.scheduleAt?.toLocaleString(), short: true },
        { title: "Status", value: order.status, short: true },
        { title: "Environment", value: NODE_ENV, short: true }
      ]
    }]
  });
};

export default { notifyPastDueOrder };
