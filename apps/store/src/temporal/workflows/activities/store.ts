/* eslint-disable max-len, no-param-reassign, max-statements, max-lines, complexity, functional/immutable-data, functional/no-let, unicorn/prefer-spread, sonarjs/cognitive-complexity */
import Repo, { LOGO_TYPE, NewStore, RaiseFundraiser, StoreType, ZENDESK_TICKET_STATUS } from "@store-monorepo/repo";
import Tickets, { Ticket, UpdateTicketMode } from "@store-monorepo/zendesk-api";
import Graphql, { Types } from "@store-monorepo/graphql";
import Temporal from "../../../lib/temporal";
import Slack from "../../../lib/slack";
import Magento from "../../../lib/magento";
import Klaviyo from "../../../lib/klaviyo";
import Comms from "../../../lib/comms";
import Raise from "../../../lib/raise";
import { StoreInput } from "../../../api/graphql/types";

type NewExternalStore = {
  schoolId?: string
  schoolName?: string
}

const serializeStore = async (input: RaiseFundraiser): Promise<NewStore> => {
  if (typeof input === "string") input = JSON.parse(input) as RaiseFundraiser;
  return Repo.Store.fromRaise(input);
};

const parseNumber = (value: number | string | null, defaultValue: number): number => {
  if (typeof value === "number" && Number.isInteger(value)) return value;
  if (!value) return defaultValue;
  const number = parseInt(`${value}`, 10);
  return isNaN(number) ? defaultValue : number;
};

const serializeExternalStore = async (input: StoreInput): Promise<NewStore & NewExternalStore> => {
  return {
    ...input,

    fundraiserId: input.fundraiserId || null,
    storeCode: input.storeCode || null,

    salesRep: input.salesRep || null,
    salesRepEmail: input.salesRepEmail || null,
    salesRepUDID: input.salesRepUDID || null,
    accountManager: input.accountManager || null,
    accountManagerEmail: input.accountManagerEmail || null,
    accountManagerUDID: input.accountManagerUDID || null,
    groupLeader: input.groupLeader || null,
    groupLeaderEmail: input.groupLeaderEmail || null,
    groupLeaderUDID: input.groupLeaderUDID || null,
    organizationId: parseNumber(input.organizationId, null),
    partnerId: parseNumber(input.partnerId, null),
    schoolName: input.schoolName || null,
    schoolId: input.schoolId || null,

    pointsPercentage: parseNumber(input.pointsPercentage, 10),
    fanStore: true,
    raiseCreatedAt: new Date(),
    raiseUpdatedAt: new Date(),
    hasParticipants: false,

    emailLogo: "",
    endDate: new Date(),
    fundraiserEntityId: null,
    fundraiserLogo: "",
    fundraiserLogoId: null,
    fundraiserPrimaryColor: "",
    fundraiserSecondaryColor: "",
    groupId: null,
    incentiveStore: false,
    logo: "",
    logoNotes: "",
    logoScript: "",
    logoSecondaryColor: "",
    logoType: LOGO_TYPE.TEAM,
    logoUpdatedAt: new Date(),
    organizationName: "",
    previousLogo: false,
    slug: "",
    startDate: new Date(),
    status: "",
    teamId: "",
    teamSize: null
  };
};

const findStore = async (storeAttrs: NewStore) => {
  return await Repo.Store.findStoreByFundraiserId(storeAttrs.fundraiserId);
};

const synchronizeStore = async (storeAttrs: NewStore, existingStore?: StoreType) => {
  return Repo.Store.upsert(storeAttrs, existingStore);
};

const createStore = async (storeAttrs: NewStore) => {
  return Repo.Store.create(storeAttrs);
};

type LogoVerificationResult = Awaited<ReturnType<typeof Repo.Store.verifyLogos>>;

const verifyLogos = async (store: StoreType): Promise<LogoVerificationResult> => {
  const requiresVerification = Object.keys(store).some(k => k.endsWith("VerifiedAt") && (!store[k] || store[k] < store.logoUpdatedAt));
  if (requiresVerification) return Repo.Store.verifyLogos(store);
  return [true, store, null];
};

const originalStore = async (store: StoreType) => {
  return await Repo.Store.originalStore(store);
};

const updateBuiltStore = async (store: StoreType, previousStore?: StoreType) => {
  return await Repo.Store.updateBuiltStore(store, previousStore);
};

const findExistingTickets = async (store: StoreType) => {
  const tickets = await Repo.ZendeskTicket.fromStore(store.id);

  if (!tickets?.length) return [];

  const fromZendesk = await Tickets.getTickets(tickets.map(_ => _.zendeskId));
  const updates: Array<[number, ZENDESK_TICKET_STATUS]> = fromZendesk.map(ticket => (
    [ticket.id, Repo.ZendeskTicket.toZendeskStatus(ticket.status)]
  ));

  await Repo.ZendeskTicket.updateStatuses(updates);

  const statuses: Record<number, ZENDESK_TICKET_STATUS> = Object.fromEntries(updates);

  return tickets.map(ticket => ({
    ...ticket,
    status: statuses[ticket.zendeskId] || ticket.status,
  }));
};

const isTicketCreated = (ticket: Ticket): boolean => {
  return ticket.created_at === ticket.updated_at;
};

const upsertZendeskTicket = async (store: StoreType, mode?: UpdateTicketMode): Promise<Ticket | string> => {
  if (store.previousLogo && store.storeUrl) return "Still should not create the ticket (?)";

  const existingTickets = await findExistingTickets(store);
  const ticket = await Tickets.upsertRaiseLogoDesignTicket(store, existingTickets, mode);

  if (ticket) {
    await storeSlackMessage(store, ":zendesk:", isTicketCreated(ticket)
      ? `Zendesk Ticket Created ${Tickets.ticketUrl(ticket?.id)}`
      : `Zendesk Ticket Updated ${Tickets.ticketUrl(ticket?.id)}`
    );

    await Repo.ZendeskTicket.upsert({
      zendeskId: ticket.id,
      storeId: store.id,
      status: Repo.ZendeskTicket.toZendeskStatus(ticket?.status)
    });

    return ticket;
  }

  return "No ticket was created or updated";
};

const updateZendeskWithLogoChanges = async (newStore: StoreType, existingStore?: StoreType) => {
  if (existingStore?.logoUpdatedAt !== newStore.logoUpdatedAt) {
    if (!existingStore?.logo && newStore.logo) {
      await storeSlackMessage(newStore, ":artist:", `New Logo Ready for Zendesk (${newStore.id})`);
      return upsertZendeskTicket(newStore);
    }

    await storeSlackMessage(newStore, ":artist:", `Logo Was Updated Zendesk (${newStore.id})`);
    return upsertZendeskTicket(newStore);
  }

  if(existingStore?.logoNotes !== newStore.logoNotes) {
    await storeSlackMessage(newStore, ":pencil:", `Fundraiser Message Updated (${newStore.fundraiserId})`);
    return upsertZendeskTicket(newStore);
  }

  if (existingStore?.startDate !== newStore.startDate) {
    await storeSlackMessage(newStore, ":date:", `Fundraiser Start Date Updated (${newStore.fundraiserId})`);
    return upsertZendeskTicket(newStore, "UPDATE_CUSTOM_FIELDS_ONLY");
  }

  return "Nothing was updated";
};

const createKlaviyoProfile = async (store: StoreType) => {
  const { attributes: { email }, id } = await Klaviyo.createOrUpdateProfile(store);
  const subscription = await Klaviyo.createSubscription(email, id);
  return [id, email, subscription];
};

const sendStoreBuildReadyEmail = async (store: StoreType) => {
  const contacts = [store.salesRepEmail, store.accountManagerEmail].filter(email => email).map(email => ({ to: email }));

  if (contacts.length > 0) {
    return await Comms.sendEmail("store_build_ready", contacts, {
      storeUrl: store.storeUrl,
      fundraiserId: store.fundraiserId,
      fundraiserName: store.name
    });
  }

  return "No contacts to send";
};

const storeSlackMessage = async (store: StoreType, icon: string, text: string) => {
  const temporalUrl = Temporal.getWorkflowUrl();
  return Slack.postMessage(`[<${temporalUrl}|${store.fundraiserId ?? store.storeCode}>] ${icon} ${text}`);
};

const checkStoreStatus = async (store: StoreType, logosVerified: boolean) => {
  return {
    fanStore: store.fanStore,
    storeUrl: store.storeUrl,
    hasParticipants: store.hasParticipants,
    logosVerified,
    builtAt: store.builtAt,
    skip: ![store.fanStore, store.storeUrl, store.hasParticipants, logosVerified, !store.builtAt].every(x => x)
  };
};

const retrieveSchool = (orgs: Types.Org[]): Types.Org | undefined => {
  if(!orgs || !orgs[0]) return undefined;

  const org = orgs[0];

  if (org.type === "School") return org;
  if(org.parent && org.parent.type === "School") return org.parent;

  return undefined;
};

const setStoreSchool = async (store: StoreType) => {
  if (store.schoolId && store.schoolName) return store;
  if (!store.fundraiserId) return store;

  const orgId  = await Graphql.orgId(store.fundraiserId);
  if (!orgId) return store;

  const orgs = await Graphql.orgPayable(orgId);
  if (!orgs) return store;

  const school = retrieveSchool(orgs);
  if (!school) return store;

  return Repo.Store.updateStoreSchool(store, school);
};

export default {
  serializeStore, serializeExternalStore,
  findStore,
  synchronizeStore,
  createStore,
  verifyLogos,
  originalStore,
  updateBuiltStore,
  updateZendeskWithLogoChanges,
  slackMessage: storeSlackMessage,
  createMagentoStore: Magento.createStore,
  upsertZendeskTicket,
  createKlaviyoProfile,
  sendStoreBuildReadyEmail,
  updateRaiseStoreUrl: Raise.updateStoreUrl,
  checkStoreStatus,
  setStoreSchool
};
