import Graphql from "@store-monorepo/graphql";
import { DonorUser, PublicFundraiserData, DonorsDonationStatsResponse } from "@store-monorepo/graphql/types";
import Repo from "@store-monorepo/repo";

import Formatter from "../../../api/pdf/format";
import Comms from "../../../lib/comms";

type FundraiserDonationState = {
  donors?: DonorUser[],
  stats?: DonorsDonationStatsResponse
};

const getDonationStats = async (fundraiserId?: string): Promise<DonorsDonationStatsResponse | undefined> => {
  return Graphql.donorsDonationStats(fundraiserId);
};

const getFundraiserDonationState = async (fundraiserId: number): Promise<FundraiserDonationState> => {
  const payables = await Graphql.donorsFundraiserPayables({ fundraiserRaiseId: fundraiserId });
  const fundraiserInternalId = payables?.find(p => p.__typename === "DonorDonation")?.fundraiserId;

  const stats = fundraiserId
    ? await getDonationStats(fundraiserInternalId)
    : { totalAmountCents: 0 };

  const donors = payables?.
    filter(p => !p.deletedAt).
    map(p => p?.donor).
    filter(Boolean).
    filter((donor, index, array) => array.findIndex(_ => _.email === donor.email) === index);

  return { stats, donors };
};

const getFundraiser = async (fundraiserId: number): Promise<PublicFundraiserData | undefined> => {
  return Graphql.publicFundraiserData(fundraiserId);
};

const findStoreByFundraiserId = async (fundraiserId: number) => {
  return Repo.Store.findStoreByFundraiserId(fundraiserId);
};

type StoreType = Awaited<ReturnType<typeof findStoreByFundraiserId>>;

const sendEmailToDonors = async (
  store: StoreType,
  fundraiser: PublicFundraiserData,
  donors: DonorUser[],
  stats?: DonorsDonationStatsResponse
) => {
  const contacts = donors.map(donor => ({
    to: donor.email,
    attributes: {
      email: donor.email,
      firstName: donor.firstName,
      lastName: donor.lastName,
    }
  }));

  const templateAttributes = {
    storeUrl: `${fundraiser.storeUrl}?utm_source=fund_recap_email`,
    storeName: store.name,
    organizationName: store.organizationLegalName,
    fundraiserName: fundraiser.name,
    fundraiserTotalAmount: Formatter.toCurrency(stats.totalAmountCents)
  };

  return Comms.sendEmail("store_otf_recap_post_fundraiser", contacts, templateAttributes);
};

export default {
  findStoreByFundraiserId,
  getFundraiserDonationState,
  getFundraiser,
  sendEmailToDonors
};
