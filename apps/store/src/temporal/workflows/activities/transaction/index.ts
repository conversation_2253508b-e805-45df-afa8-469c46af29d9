/* eslint-disable import/group-exports, max-len */
import { MagentoConsumerPointsLiability as ConsumerPointsLiability, MagentoRefundPointsLiability as RefundPointsLiability } from "@store-monorepo/magento-api";
/* eslint-disable max-statements, functional/no-let */
import MagentoToNetsuite from "./dto";

export type MagentoConsumerPointsLiability = ConsumerPointsLiability;
export type MagentoRefundPointsLiability = RefundPointsLiability;

const postRedeemPointsToNetsuite = async (params: ConsumerPointsLiability) => {
  if (MagentoToNetsuite.shouldHandleRedeemedOrExpired(params)) await MagentoToNetsuite.recordRedeemedOrExpiredPoints(params);
};

const postEarnedPointsToNetsuite = async (params: ConsumerPointsLiability) => {
  let internalId: number;
  let externalId: string;
  if (MagentoToNetsuite.shouldHandleOrderFailure(params)) {
    await MagentoToNetsuite.recordOrderFailureRestore(params);
    return;
  }
  if (MagentoToNetsuite.shouldHandleEarnedPoints(params)) {
    const { netsuiteInternalId, netsuiteExternalId } = await MagentoToNetsuite.recordEarnedPoints(params);
    if (netsuiteInternalId) {
      internalId = netsuiteInternalId;
      externalId = netsuiteExternalId;
    }
  }
  if (MagentoToNetsuite.shouldHandleTransfer(params)) {
    const { netsuiteInternalId, netsuiteExternalId } = await MagentoToNetsuite.recordTransferPoints(params);
    internalId = netsuiteInternalId;
    externalId = netsuiteExternalId;
  }
  await MagentoToNetsuite.updateMagentoPointsTransaction({
    netsuiteInternalId: internalId,
    netsuiteExternalId: externalId,
    transactionId: params.transactionId,
    transactionType: params.transactionType
  });
};

const postBoughtPointsToNetsuite = async (params: ConsumerPointsLiability) => {
  let internalId;
  let externalId;
  if (MagentoToNetsuite.shouldHandlePointsBoughtCD(params)) {
    const { netsuiteInternalId, netsuiteExternalId } = await MagentoToNetsuite.recordBoughtPoints(params);
    internalId = netsuiteInternalId;
    externalId = netsuiteExternalId;
  }
  if (MagentoToNetsuite.shouldHandleTransfer(params)) {
    const { netsuiteInternalId, netsuiteExternalId } = await MagentoToNetsuite.recordTransferPoints(params);
    internalId = netsuiteInternalId;
    externalId = netsuiteExternalId;
  }
  await MagentoToNetsuite.updateMagentoPointsTransaction({
    netsuiteInternalId: internalId,
    netsuiteExternalId: externalId,
    transactionId: params.transactionId,
    transactionType: params.transactionType
  });
};

const formatTransactionType = (netsuiteType: string): string => {
  const type = netsuiteType.toLowerCase();
  const isDashboardBought = type.includes("dashboard") && type.includes("bought");
  return isDashboardBought ? "dashboardBought" : type;
};

const processPointsByTransactionType = async (params: ConsumerPointsLiability) => {
  const transactionHandlers = {
    earnedPoints: postEarnedPointsToNetsuite,
    boughtPoints: postBoughtPointsToNetsuite,
    dashboardBoughtPoints: postBoughtPointsToNetsuite,
    redeemPoints: postRedeemPointsToNetsuite,
    expiredPoints: postRedeemPointsToNetsuite
  };
  const transactionType = formatTransactionType(params.transactionType);
  const transactionHandler = transactionHandlers[`${transactionType}Points`];
  return await transactionHandler(params);
};

const processPointsTransactionTransfer = async (params: ConsumerPointsLiability) => {
  if (!MagentoToNetsuite.shouldHandleTransfer(params)) return;
  const transferredNetsuiteResponse = await MagentoToNetsuite.recordTransferPoints(params);
  await MagentoToNetsuite.updateMagentoPointsTransaction({
    transactionId: params.transactionId,
    netsuiteInternalId: transferredNetsuiteResponse.netsuiteInternalId,
    netsuiteExternalId: transferredNetsuiteResponse.netsuiteExternalId,
    transactionType: params.transactionType
  });
};

const shouldBillRefund = async (params: RefundPointsLiability) => {
  const hasShipped = await MagentoToNetsuite.hasOrderShipped(params.netsuiteOrderId);
  const hasTracking = await MagentoToNetsuite.hasOrderShipmentTracking(params.netsuiteOrderId);
  return hasTracking && hasShipped;
};

const processRefundPointsTransaction = async (params: RefundPointsLiability) => {
  const isBilledRefund = await shouldBillRefund(params);
  if (isBilledRefund) {
    return MagentoToNetsuite.recordBilledRefundPoints(params);
  }
};

export default {
  processPointsTransactionTransfer,
  processPointsByTransactionType,
  processRefundPointsTransaction,
  updateMagentoPointsTransactionNetsuiteId: MagentoToNetsuite.updateMagentoPointsTransaction
};
