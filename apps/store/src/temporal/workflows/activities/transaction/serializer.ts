/* eslint-disable max-lines */
import Netsuite, { NetsuiteJournalEntry, NetsuiteCustomerDeposit } from "@store-monorepo/netsuite";
import { MagentoConsumerPointsLiability, MagentoRefundPointsLiability } from "@store-monorepo/magento-api";

const generateOrderFailureRestoreSerializer = (transactionId: number, points: number): NetsuiteJournalEntry => {
  return {
    subsidiary: { id: Netsuite.SUBSIDIARY_ID },
    externalId: `JE-Points_Restore-${transactionId}`,
    line: {
      items: [
        {
          account: { id: Netsuite.ACCOUNTS.POINTS_LIABILITY },
          debit: points,
          memo: `$${points} transferred back to points deferred account due to order failure restore`
        },
        {
          account: { id: Netsuite.ACCOUNTS.POINTS_DEFERRED },
          credit: points,
          memo: `$${points} transferred back to points deferred account due to order failure restore`
        }
      ]
    },
    memo: `When points are restored`
  };
};

const generatePointsEarnedSerializer = (params: MagentoConsumerPointsLiability): NetsuiteCustomerDeposit => {
  const paymentMethodId = Netsuite.PAYMENT_METHOD[params.paymentMethod];
  return {
    customer: { id: Netsuite.CUSTOMER_ID },
    currency: { id: Netsuite.USD_CURRENCY_ID },
    salesOrder: { externalId: `SO_SnapV4${params.orderId}` },
    externalId: `Store Points - Customer Deposit: ${params.orderId}`,
    account: { id: Netsuite.ACCOUNTS[params.paymentMethod] },
    class: { id: Netsuite.CLASS_ID },
    payment: params.pointsDelta,
    paymentOption: { id: Netsuite.PAYMENT_METHOD[params.paymentMethod] },
    customForm: { id: Netsuite.CUSTOM_FORM.DEPOSIT },
    custbodycustbodypayment_method: paymentMethodId,
    custbodypayment_method: paymentMethodId,
    department: { id: Netsuite.DEPARTMENT_ID },
    custbodyorg_id: params.organizationId,
    custbodytxn_id: String(params.paymentEntityId),
    custbodyactive_points: params.pointsDelta,
    custbodytotal_points: params.pointsBalance,
  };
};

const generatePointsBoughtSerializer = (params: MagentoConsumerPointsLiability): NetsuiteCustomerDeposit => {
  return {
    customer: { id: Netsuite.CUSTOMER_ID },
    currency: { id: Netsuite.USD_CURRENCY_ID },
    customForm: { id: Netsuite.CUSTOM_FORM.DEPOSIT },
    externalId: `${params.customerId}_${params.transactionId}`,
    account: { id: Netsuite.ACCOUNTS.STRIPE },
    class: { id: Netsuite.CLASS_ID },
    custbodyactive_points: params.pointsDelta,
    custbodytotal_points: params.pointsBalance,
    payment: params.pointsDelta
  };
};

const generatePointsBoughtJournalEntrySerializer = (params: MagentoConsumerPointsLiability): NetsuiteJournalEntry => {
  return {
    externalId: `JE-Points_Migration-${params.transactionId}`,
    subsidiary: { id: Netsuite.SUBSIDIARY_ID },
    line: {
      items: [
        {
          account: { id: Netsuite.ACCOUNTS.STRIPE },
          debit: params.pointsDelta,
          memo: `${params.pointsDelta} points being transferred to Points Deferred Account`
        },
        {
          account: { id: Netsuite.ACCOUNTS.POINTS_DEFERRED },
          credit: params.pointsDelta,
          memo: `${params.pointsDelta} points being transferred to Points Deferred Account`
        }
      ]
    },
    memo: "Amount is transferred from Stripe to Points Deferred Account"
  };
};

const generatePointsTransferSerializer = (params: MagentoConsumerPointsLiability): NetsuiteCustomerDeposit => {
  return {
    customer: { id: Netsuite.CUSTOMER_ID },
    currency: { id: Netsuite.USD_CURRENCY_ID },
    externalId: `${params.customerId}_${params.transactionId}`,
    account: { id: Netsuite.ACCOUNTS.STRIPE },
    class: { id: Netsuite.CLASS_ID },
    customForm: { id: Netsuite.CUSTOM_FORM.DEPOSIT },
    department: { id: Netsuite.DEPARTMENT_ID },
    custbodyorg_id: params.organizationId,
    payment: params.pointsDelta,
  };
};

const generateRedeemedOrExpiredPointsSerializer = (params: MagentoConsumerPointsLiability): NetsuiteJournalEntry => {
  const isRedeemed = params.comment === "order_placed_spend";
  const pointsDelta = Math.abs(params.pointsDelta);
  return {
    subsidiary: { id: Netsuite.SUBSIDIARY_ID },
    externalId: isRedeemed ? `JE-Points_used-${params.transactionId}` : `Point_Expiry${params.transactionId}`,
    line: {
      items: [
        {
          account: { id: Netsuite.ACCOUNTS.POINTS_DEFERRED },
          debit: pointsDelta,
          memo: isRedeemed
            ? `${pointsDelta}$ transferred to store products deferred account. Points were used for this purchase`
            : `${pointsDelta} points expired`
        },
        {
          account: { id: Netsuite.ACCOUNTS.POINTS_LIABILITY },
          credit: pointsDelta,
          memo: isRedeemed
            ? `${pointsDelta}$ credited to Product Income account from points store as points were used for this purchase`
            : `${pointsDelta} points expired and credited to cash account`
        }
      ]
    },
    memo: isRedeemed
      ? "When points are used and order is not shipped yet"
      : `${pointsDelta} Points are expired`
  };
};

const generateRefundPointsSerializer = (params: MagentoRefundPointsLiability, accounts: number[]): NetsuiteJournalEntry => {
  const [refundLiability, refundDeferred] = accounts;
  const accountType = refundLiability === Netsuite.ACCOUNTS.POINTS_LIABILITY ? "cash" : "deferred revenue";
  return {
    subsidiary: { id: Netsuite.SUBSIDIARY_ID },
    externalId: `JE-Refund_SnapV4${params.orderId}`,
    line: {
      items: [
        {
          account: { id: refundLiability },
          debit: params.amount,
          memo: `${params.amount} debited from ${accountType} account due to refund against order: ${params.orderId}`
        },
        {
          account: { id: refundDeferred },
          credit: params.amount,
          memo: `${params.amount} credited to refunds of product account against order: ${params.orderId}`
        }
      ]
    },
    memo: `Refund has occurred`
  };
};

const generateBilledRefundPointsSerializer = (params: MagentoRefundPointsLiability): NetsuiteJournalEntry => {
  return generateRefundPointsSerializer(params, [Netsuite.ACCOUNTS.POINTS_LIABILITY, Netsuite.ACCOUNTS.REFUND_DEFERRED]);
};

export default {
  generateOrderFailureRestore: generateOrderFailureRestoreSerializer,
  generatePointsEarned: generatePointsEarnedSerializer,
  generatePointsBought: generatePointsBoughtSerializer,
  generatePointsBoughtJournalEntry: generatePointsBoughtJournalEntrySerializer,
  generatePointsTransfer: generatePointsTransferSerializer,
  generateRedeemedOrExpiredPoints: generateRedeemedOrExpiredPointsSerializer,
  generateBilledRefundPoints: generateBilledRefundPointsSerializer
};
