/* eslint-disable max-lines, @typescript-eslint/no-unused-vars */
import Magento, { MagentoConsumerPointsLiability as ConsumerPointsLiability, MagentoPointsTransaction as PointsTransaction, MagentoRefundPointsLiability as RefundPointsLiability } from "@store-monorepo/magento-api";
import Netsuite, { NetsuiteJournalEntry, NetsuiteCustomerDeposit } from "@store-monorepo/netsuite";
import Repo from "@store-monorepo/repo";
import Serializer from "./serializer";

type NetsuiteResponse = {
  netsuiteInternalId: number;
  netsuiteExternalId: string;
};

const createNetsuiteJournalEntry = async (params: NetsuiteJournalEntry): Promise<number> => {
  const journalEntry = await Netsuite.JournalEntry.create(params);
  if (!journalEntry) throw new Error("Failed to create Netsuite journal entry");
  return journalEntry;
};

const createNetsuiteCustomerDeposit = async (params: NetsuiteCustomerDeposit): Promise<number> => {
  const customerDeposit = await Netsuite.CustomerDeposit.create(params);
  if (!customerDeposit) throw new Error("Failed to create Netsuite customer deposit");
  return customerDeposit;
};

const hasOrderShipped = async (netsuiteSalesOrderId: number) => {
  const salesOrder = await Repo.Order.findBy({
    netsuiteId: `${netsuiteSalesOrderId}`
  });
  if (!salesOrder) throw new Error("Failed to get Order");
  return salesOrder.status === "SHIPPED";
};

const hasOrderShipmentTracking= async (netsuiteSalesOrderId: number) => {
  const salesOrder = await Repo.Order.findBy({
    netsuiteId: `${netsuiteSalesOrderId}`
  });
  if (!salesOrder) throw new Error("Failed to get Netsuite sales order");
  return salesOrder.trackingNumber;
};

const updateMagentoPointsTransaction = async (params: PointsTransaction) => {
  return await Magento.PointsTransaction.update(params.transactionId, {
    netsuiteInternalId: params.netsuiteInternalId,
    netsuiteExternalId: params.netsuiteExternalId,
  });
};

const shouldHandleOrderFailure = (params: ConsumerPointsLiability) => params.eventCode === "OrderFailureRestore";

const shouldHandleTransfer = (params: ConsumerPointsLiability) => params.senderId && params.receiverId;

const shouldHandleRedeemedOrExpired = (params: ConsumerPointsLiability) => (params.netsuiteInternalId && params.netsuiteExternalId) && (Number(params.netsuiteInternalId) !== 0) && ["expired", "order_placed_spend"].includes(params.comment);

const shouldHandleParentTransaction = (params: ConsumerPointsLiability) => ["Earned", "Bought", "Dashboard_Bought"].includes(params.parentTransactionType) && shouldHandleRedeemedOrExpired(params);

const shouldHandleEarnedPoints = (params: ConsumerPointsLiability) => params.comment === "Points earned on checkout" && params.orderId;

const shouldHandlePointsBoughtCD = (params: ConsumerPointsLiability) => params.comment === "buy-points";

const recordOrderFailureRestore = async (params: ConsumerPointsLiability): Promise<void> => {
  const journalEntryParams = Serializer.generateOrderFailureRestore(params.transactionId, params.pointsDelta);
  await createNetsuiteJournalEntry(journalEntryParams);
};

const recordEarnedPoints = async (params: ConsumerPointsLiability): Promise<NetsuiteResponse> => {
  const customerDeposit = Serializer.generatePointsEarned(params);
  const netsuiteInternalId = await createNetsuiteCustomerDeposit(customerDeposit);
  return { netsuiteInternalId, netsuiteExternalId: customerDeposit.externalId };
};

const recordTransferPoints = async (params: ConsumerPointsLiability): Promise<NetsuiteResponse> => {
  const customerDeposit = Serializer.generatePointsTransfer(params);
  const netsuiteInternalId = await createNetsuiteCustomerDeposit(customerDeposit);
  return { netsuiteInternalId, netsuiteExternalId: customerDeposit.externalId };
};

const recordRedeemedOrExpiredPoints = async (params: ConsumerPointsLiability): Promise<void> => {
  const journalEntry = Serializer.generateRedeemedOrExpiredPoints(params);
  await createNetsuiteJournalEntry(journalEntry);
};

const recordBoughtPoints = async (params: ConsumerPointsLiability): Promise<NetsuiteResponse> => {
  const customerDeposit = Serializer.generatePointsBought(params);
  const netsuiteInternalId = await createNetsuiteCustomerDeposit(customerDeposit);
  return {
    netsuiteInternalId,
    netsuiteExternalId: customerDeposit.externalId
  };
};

const recordParentTransaction = async (params: ConsumerPointsLiability): Promise<NetsuiteResponse | void> => {
  if (!shouldHandleParentTransaction(params)) return;
  await updateMagentoPointsTransaction({
    transactionId: params.transactionId,
    netsuiteInternalId: params.parentNetsuiteInternalId,
    netsuiteExternalId: params.parentNetsuiteExternalId,
    transactionType: params.parentTransactionType
  });

  if (shouldHandleRedeemedOrExpired(params)) await recordRedeemedOrExpiredPoints(params);
  return {
    netsuiteInternalId: params.parentNetsuiteInternalId,
    netsuiteExternalId: params.parentNetsuiteExternalId
  };
};

const recordBilledRefundPoints = async (params: RefundPointsLiability): Promise<void> => {
  const journalEntry = Serializer.generateBilledRefundPoints(params);
  await createNetsuiteJournalEntry(journalEntry);
};

export default {
  updateMagentoPointsTransaction,
  recordParentTransaction,
  recordBoughtPoints,
  recordOrderFailureRestore,
  recordEarnedPoints,
  recordTransferPoints,
  recordRedeemedOrExpiredPoints,
  shouldHandleParentTransaction,
  shouldHandleRedeemedOrExpired,
  shouldHandleTransfer,
  shouldHandleOrderFailure,
  shouldHandleEarnedPoints,
  recordBilledRefundPoints,
  hasOrderShipped,
  hasOrderShipmentTracking,
  shouldHandlePointsBoughtCD
};
