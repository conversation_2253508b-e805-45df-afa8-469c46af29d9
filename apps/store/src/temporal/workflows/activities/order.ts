/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable import/max-dependencies */
/* eslint-disable max-len, max-lines, id-denylist, max-lines-per-function, no-warning-comments, functional/immutable-data, import/no-internal-modules, sonarjs/cognitive-complexity, complexity, max-statements, functional/no-let */
import Repo from "@store-monorepo/repo";
import Marco<PERSON><PERSON> from "@store-monorepo/marco-api";
import MedusaRequest from "@store-monorepo/medusa-api";
import Prisma, { STATE, STATUS, Order as PrismaOrder, OrdersOnProducts, Product, PrismaClient, SOURCE } from "@prisma/client";
import OrderNotifications from "@store-monorepo/order-notifications";
import Netsuite from "@store-monorepo/netsuite";
import Graphql from "@store-monorepo/graphql";
import { log } from "@store-monorepo/logger";
import { JsonArray } from "@prisma/client/runtime/library";
import { MarcoEventInput, OrderInput, Vendor } from "../../../api/graphql/types";
import OrderHelper from "../../../lib/order";
import Helpers from "../../../lib/helpers";
import Comms from "../../../lib/comms";

const GIFT_CARD_NETSUITE_ID = "24103";

const prisma = new PrismaClient();

const updateOrderToShipped = async (order: PrismaOrder) => {
  return Repo.Order.updateWhere({ netsuiteId: order.netsuiteId }, { status: STATUS.SHIPPED, shippingReceivedAt: new Date(Date.now()) });
};

const retrieveOrder = (netsuiteId: string) => {
  return Repo.Order.findBy({ netsuiteId }, true);
};

const recreateOrder = async (event): Promise<OrderWithProducts> => {
  const order = await updateOrder(event);
  if ((order.status !== STATUS.CREATED) && (order.status !== STATUS.SHIPPED)) {
    await recreateOrderLineItems(event);
  }
  return Repo.Order.findById(order.id, true) as Promise<OrderWithProducts>;
};

/* The NS webhook does not have identifiers on line items that we can use to tie our OrdersProducts directly to a line item.
   Therefore we just delete all existing OrdersProducts tied to an order, then recreate them with the line items
   from the webhook
*/
const recreateOrderLineItems = async (event) => {
  const lineItems = OrderNotifications.lineItems(event);
  const products = await Repo.Product.findMany({ netsuiteId: { in: lineItems.map(li => li.netsuiteId) } });
  const productMap = products.reduce((obj, product) => {
    return { ...obj, [product.netsuiteId]: product.id };
  }, {});
  const sampleLineItem = await prisma.ordersOnProducts.findFirst({ where: { order: { netsuiteId: event.record.id } } });
  const newLineItems = lineItems.map(li => {
    return {
      orderId: sampleLineItem.orderId,
      productId: productMap[li.netsuiteId],
      receiverName: li.receiverName,
      logo: sampleLineItem.logo,
      printAttributes: li.printAttributes as JsonArray

    };
  });
  await prisma.$transaction([
    prisma.ordersOnProducts.deleteMany({ where: { order: { netsuiteId: event.record.id } } }),
    prisma.ordersOnProducts.createMany({
      data: newLineItems
    })
  ]);
};

const updateOrder = async (event) => {
  const status = OrderNotifications.orderStatus(event);
  const address = OrderNotifications.parseAddress(event);
  return prisma.order.update({
    where: { netsuiteId: event.record.id }, data: {
      ...address,
      status,
      ...(status === STATUS.SHIPPED ? { shippingReceivedAt: new Date(Date.now()) } : {})
    }
  });
};

// Used when original order fails to create due to missing new products in db.
// Often the process is to manually resend order.
const createResentOrder = async (params: Omit<OrderInput, "netsuiteId"> & {
  netsuiteId: string;
}, orderId: string) => {
  const products = await Repo.Product.findMany({ netsuiteId: { in: params.products.map(li => li.netsuiteId) } });
  const productMap = products.reduce((obj, product) => {
    return { ...obj, [product.netsuiteId]: product.id };
  }, {});
  const newLineItems = params.products.map(li => {
    return {
      orderId,
      productId: productMap[li.netsuiteId],
      receiverName: li.receiverName,
      logo: li.logo,
      printAttributes: li.printAttributes as JsonArray

    };
  });
  await prisma.$transaction([
    prisma.ordersOnProducts.deleteMany({ where: { orderId } }),
    prisma.ordersOnProducts.createMany({
      data: newLineItems
    })
  ]);
  return Repo.Order.findById(orderId, true) as Promise<OrderWithProducts>;
};

const createOrderDraft = async (params: Omit<OrderInput, "netsuiteId"> & {
  netsuiteId: string;
}): Promise<OrderWithProducts> => {
  const existingOrder = await (Repo.Order.findBy({ netsuiteId: params.netsuiteId }, true) as Promise<OrderWithProducts>);
  if (existingOrder) {
    if (params.vendor === Vendor.Store) {
      return createResentOrder(params, existingOrder.id);
    }
    return existingOrder;
  }
  const dbProducts = await Repo.Product.findMany({
    OR: [
      {
        netsuiteId: {
          in: params.products.map((p) => p.netsuiteId).filter((p) => !!p)
        }
      },
      {
        sku: {
          in: params.products.map((p) => p.sku).filter((p) => !!p)
        }
      }
    ]
  });

  const productLookup = dbProducts.reduce((acc, product) => {
    if (product.netsuiteId) {
      acc[product.netsuiteId] = product;
    }
    acc[product.sku] = product;
    return acc;
  }, {});
  let source: PrismaOrder["source"];
  switch (params.source) {
    case "MEDUSA":
      source = SOURCE.MEDUSA;
      break;
    case "MAGENTO":
      source = SOURCE.MAGENTO;
      break;
    case "OTK":
      source = SOURCE.OTK;
      break;
    default:
      source = null;
  }
  return Repo.Order.create({
    ...params,
    fundraiserId: params.fundraiserId ? String(params.fundraiserId) : undefined,
    scheduleAt: params.scheduleAt ? new Date(params.scheduleAt) : undefined,
    state: params.state as STATE,
    baseSubtotal: Math.round(params.baseSubtotal * 100),
    shippingCost: Math.round(params.shippingCost * 100),
    taxAmount: Math.round(params.taxAmount * 100),
    discountAmount: Math.round(params.discountAmount * 100),
    giftCardAmount: Math.round(params.giftCardAmount * 100),
    products: {
      create: params.products.map((p) => {
        const product = productLookup[p.netsuiteId] || productLookup[p.sku];
        /*
          TODO: Determine how to handle failed product lookups. Right now if Raise sends a
          NS product id that's not referenced in the Raise Marco mappings csv, the following lookup will fail.
          I don't think we want to silently filter out products that aren't accounted for in our DB/the csv, but we
          also don't have any good guarantees that the CSV is accurate (I've had to recreate products multiple times already as corrections
          have been made to the csv. Here I'm filtering, but I don't think this is the path forward.
          ).
        */
        if (!product) {
          return undefined;
        }
        return {
          productId: product.id,
          logo: p.logo ?? "",
          printAttributes: p.printAttributes as JsonArray,
          receiverName: p.receiverName,
          amount: Math.round(p.amount * 100)
        };
      }).filter(product => !!product)
    },
    source
  }) as Promise<OrderWithProducts>;
};

type OrderWithProducts = PrismaOrder & { products: (OrdersOnProducts & { product: Product })[] };

const digitalProducts = new Set(["24346", "24347", "24343", "24344", "24340", "24341", "24338", "24337", "24334", "24335", "24331", "24332", "24328", "24329", "24348", "24349", "24350", "24351", "24352", "24353", "24356", "24357", "24358", "24361", "24363"]);

const createMarcoOrder = async (params: OrderWithProducts) => {
  const order = await Repo.Order.findById(params.id);
  const products = await Repo.OrdersOnProducts.findBy({ orderId: order.id });
  if (order.status === STATUS.DESIGN) {
    const filteredProducts = products.filter(orderProduct => orderProduct.product.netsuiteId !== GIFT_CARD_NETSUITE_ID && !digitalProducts.has(orderProduct.product.netsuiteId));
    if (filteredProducts.length === 0) {
      return true;
    }
    const response = await MarcoAPI.createOrder({ ...order, products: filteredProducts });
    await Repo.Order.update(order.id, { status: STATUS.CREATED, externalId: String(response.order), supplierOrderCreatedAt: new Date(Date.now()) });
    return response;
  }
  return null;
};

const applyMarcoUpdate = async (event: MarcoEventInput) => {
  log("temporal", "info", "marco webhook", event);

  if (event.shipments.length > 0) {
    const carrier = event.shipments[0].shipping_carrier;
    await Repo.Order.updateWhere({ netsuiteId: event.purchase_order! }, { carrier });
  }
};

enum NetsuiteStatus {
  Design = "Design",
  Shipped = "Shipped",
  MarcoCreated = "MarcoCreated",
  MarcoRejected = "MarcoRejected",
  Rejected = "Rejected"
}

const toPrismaStatus = (status: NetsuiteStatus) => {
  switch (status) {
    case NetsuiteStatus.MarcoRejected:
      return STATUS.MARCO_REJECTED;
    case NetsuiteStatus.Rejected:
      return STATUS.REJECTED;
    default:
      return undefined;
  }
};

const updateNetsuiteOrderStatus = async (order: Prisma.Order, status: NetsuiteStatus) => {
  const currentOrder = await Repo.Order.findById(order.id);
  if (currentOrder.status !== toPrismaStatus(status)) {
    await Netsuite.SalesOrder.updateShipmentWorkflowStage(order, status);
  }
};

const updateDBOrderStatus = async (id: string, status: string) => Repo.Order.update(id, {
  status: status as STATUS,
  ...(status === STATUS.SHIPPED ? { shippingReceivedAt: new Date(Date.now()) } : {}),
  ...(status === STATUS.DELIVERED ? { deliveredAt: new Date(Date.now()) } : {})
});

const closeDBOrders = async (netsuiteIds: string[]) => {
  return Repo.Order.updateMany({
    netsuiteId: { in: netsuiteIds }
  }, { status: STATUS.CLOSED });
};

const findPastDueOrders = async (): Promise<Prisma.Order[]> => {
  const to = new Date(Date.now() - 1000 * 60 * 60 * 8);
  const from = new Date(Number(to) - 1000 * 60 * 60 * 24);

  return Repo.Order.findMany({
    scheduleAt: {
      gte: from,
      lt: to
    },
    status: Prisma.STATUS.DESIGN
  }, false);
};

const toStoreLogo = (url: string | null) => `${url}`.replace(/.*\/([^/]+\.(svg|png))$/, "https://s3.us-west-1.amazonaws.com/snapraiselogos/PrinterLogos/$1").replace(/\.svg$/, ".png");

const splitName = (fullName: string): { firstName: string; lastName: string } => {
  const parts = (fullName || "").trim().split(" ");
  return {
    firstName: parts[0] || "",
    lastName: parts.slice(1).join(" ") || "",
  };
};

const getStatusUpdateEmailInput = async (oldestOrder: { id: string; }, status: string) => {
  const order = await Repo.Order.findFirst({ id: oldestOrder.id });
  if (!order) {
    throw new Error("Order not found");
  }
  //! Determine Store
  const store = await Repo.Store.findStoreByFundraiserId(Number(order.fundraiserId));
  if (!store) {
    throw new Error("Store not found");
  }

  const cleanedUrl = toStoreLogo(store.logoWebHeaderUrl);

  const rawItems = await Promise.all(
    (order.products || []).map(async (p: any) => {
      let thumbnailUrl: string | null = null;
      if (p.product?.netsuiteId) {
        try {
          const res = await MedusaRequest.ProductVariant.findByNetsuiteId(p.product.netsuiteId);
          if (res.success && res?.variant) {
            const variant = res?.variant;
            const url = new URL(variant.metadata?.thumbnail);
            thumbnailUrl = url.toString();
          }
        } catch (e) {
          console.error("Image fetch failed for netsuite_id:", p.product.netsuite_id);
        }
      }

      return {
        id: p.product?.id,
        sku: p.product?.sku,
        logo: p.logo,
        size: p.product?.size,
        color: p.product?.color,
        quantity: 1,
        productUrl: p.product?.url,
        productName: p.product?.name,
        productImageUrl: thumbnailUrl
      };
    })
  );

  const totalQuantity = rawItems.length;

  const items = Object.values(
    rawItems.reduce((acc, item) => {
      const key = item.id || "unknown";
      if (!acc[key]) {
        acc[key] = { ...item };
      } else {
        acc[key].quantity += 1;
      }
      return acc;
    }, {})
  );

  const baseAttributes = {
    items,
    storeUrl: store.storeUrl,
    storeName: store.name,
    paymentType: "Credit Card",
    customerName: order.shipTo,
    storeLogoUrl: cleanedUrl,
    orderIncrementId: order.orderId,
    trackingNumberUrl: order.trackingUrl,
    formattedBillingAddress: Helpers.formatAddress(order, "billing"),
    formattedShippingAddress: Helpers.formatAddress(order, "shipping")
  };

  let templateName;
  let templateAttributes: Record<string, any>;
  let contacts: any[] = [];
  const groupLeader = splitName(store.groupLeader || "");
  const salesRep = splitName(store.salesRep || "");
  const accountManager = splitName(store.accountManager || "");

  switch (order.vendor) {
    case Vendor.Raise:
    case Vendor.RaiseMarco:
    case Vendor.Marco:
      templateName = status === STATUS.DELIVERED ? "raise_order_delivered" : "raise_order_shipped";
      contacts = [
        {
          to: store.groupLeaderEmail,
          attributes: {
            email: store.groupLeaderEmail,
            firstName: groupLeader.firstName,
            lastName: groupLeader.lastName,
          }
        },
        {
          to: store.accountManagerEmail,
          attributes: {
            email: store.accountManagerEmail,
            firstName: accountManager.firstName,
            lastName: accountManager.lastName,
          }
        },
        {
          to: store.salesRepEmail,
          attributes: {
            email: store.salesRepEmail,
            firstName: salesRep.firstName,
            lastName: salesRep.lastName,
          }
        }
      ];
      templateAttributes = status === STATUS.DELIVERED
        ? {
          ...baseAttributes,
          itemsCount: totalQuantity,
          fundraiserName: order.packingSlipTitle,
          trackingNumber: order.trackingNumber,
          trackingNumberUrl: order.trackingUrl,
          groupLeaderName: store.groupLeader,
          trackingLocation: Helpers.formatAddress(order, "shipping"),
        }
        : {
          ...baseAttributes,
          fundraiserName: order.packingSlipTitle,
          trackingNumber: order.trackingNumber,
          trackingNumberUrl: order.trackingUrl,
          groupLeaderName: store.groupLeader,
        };
      break;

    case Vendor.Store:
      templateName = status === STATUS.DELIVERED ? "store_order_delivered" : "store_order_shipped";
      contacts = [{
        to: order.shipToEmail,
        attributes: {
          email: order.shipToEmail,
          firstName: "", //shipToName?
          lastName: "",
        }
      }];
      templateAttributes = status === STATUS.DELIVERED
        ? {
          ...baseAttributes,
          deliveredDate: new Date(order.deliveredAt).toLocaleDateString("en-US"),
          customerAccountUrl: `${store.storeUrl.replace(/\/+$/, "")}/customer/account`
        }
        : {
          ...baseAttributes,
          customerAccountUrl: `${store.storeUrl.replace(/\/+$/, "")}/customer/account`,
          trackingNumber: order.trackingNumber,
          trackingNumberUrl: order.trackingUrl,
          trackingCarrier: order.carrier,
        };
      break;

    default:
      throw new Error("Invalid vendor");
  }

  return {
    templateName,
    contacts,
    templateAttributes
  };
};

const sendStatusUpdateEmail = async (params: any) => {
  console.log("Sending status update email", params);

  return await Comms.sendEmail(
    params.templateName,
    params.contacts,
    params.templateAttributes
  );
};

const findRejectedOrders = async (): Promise<OrderWithProducts[]> => {
  return await Repo.Order.findMany({ status: Prisma.STATUS.REJECTED }, true) as OrderWithProducts[];
};

const canRejectedOrderBeRecovered = async (order: OrderWithProducts): Promise<boolean> => {
  const missing = await OrderHelper.missingLogoUrls(order.products);
  return missing.length === 0;
};

const recoverRejectedOrderByRestarting = async (order: OrderWithProducts) => {
  const { netsuiteId } = order;
  const response = await Graphql.restartOrderWorkflow(netsuiteId);
  return { ...response, netsuiteId };
};

const orderActivities = {
  createOrderDraft,
  createMarcoOrder,
  closeDBOrders,
  recreateOrder,
  applyMarcoUpdate,
  updateDBOrderStatus,
  updateNetsuiteOrderStatus,
  findPastDueOrders,
  retrieveOrder,
  updateOrderToShipped,
  findRejectedOrders,
  canRejectedOrderBeRecovered,
  recoverRejectedOrderByRestarting,
  getStatusUpdateEmailInput,
  sendStatusUpdateEmail
};

export default orderActivities;
