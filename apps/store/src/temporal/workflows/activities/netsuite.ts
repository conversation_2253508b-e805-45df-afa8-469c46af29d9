/* eslint-disable max-lines */
import Netsuite, { NetsuiteOrder, NetsuiteLineItem, NetsuiteCustomerDeposit } from "@store-monorepo/netsuite";
import { Order } from "@prisma/client";
import Repo from "@store-monorepo/repo";
import { OrderInput, OrderProductInput } from "../../../api/graphql/types";
type LineItemInput = {
  itemId: number;
  rate: number;
  quantity: number;
  sku: string;
  logoUrl?: string;
  printAttributes?: PrintAttributes
};
type PrintAttributes = {
  name?: {
    value?: string,
    font?: string,
    color: string
  },
  number?: {
    value?: string,
    font?: string,
    color?: string
  }
}

const updateNetsuiteTracking = async (order: Order) => {
  return Netsuite.SalesOrder.createItemFulfillment(order);
};

const orderUrl = (netsuiteId: string): string => {
  return `https://${process.env.NETSUITE_ACCOUNT_ID}.app.netsuite.com/app/accounting/transactions/salesord.nl?id=${netsuiteId}&whence=`;
};

const createNetsuiteOrder = async (order: NetsuiteOrder) => {
  const dbOrder = await Repo.Order.findBy({ orderId: order.externalId.split("SO_SnapV4")[1] });
  if (dbOrder) {
    return dbOrder.netsuiteId;
  }
  const netsuiteId = await Netsuite.SalesOrder.create(order);
  if (!netsuiteId) throw new Error("Failed to create Netsuite order");
  return netsuiteId;
};

const createCustomerDeposit = async (params: NetsuiteCustomerDeposit) => {
  await Netsuite.CustomerDeposit.create(params);
};

const customerDepositParams = async (nsOrder: NetsuiteOrder, order: OrderInput, paymentAmount: number): Promise<NetsuiteCustomerDeposit> => {
  return {
    customer: { id: Netsuite.CUSTOMER_ID },
    currency: { id: Netsuite.USD_CURRENCY_ID },
    salesOrder: { externalId: `SO_SnapV4${order.orderId}` },
    externalId: `CD_SnapV4${order.orderId}`,
    account: { id: Netsuite.ACCOUNTS[order.paymentMethod] },
    class: { id: Netsuite.CLASS_ID },
    payment: paymentAmount,
    customForm: { id: Netsuite.CUSTOM_FORM.DEPOSIT },
    custbodycustbodypayment_method: Netsuite.PAYMENT_METHOD[order.paymentMethod?.toUpperCase()] || Netsuite.PAYMENT_METHOD.STRIPE,
    department: { id: Netsuite.DEPARTMENT_ID },
    custbodytxn_id: order.transactionId,
  };
};

const createBaseNetsuiteOrder = (order: OrderInput) => ({
  billaddresslist: null,
  entity: { id: parseInt(process.env.NETSUITE_CUSTOMER_ID) },
  department: { id: Netsuite.DEPARTMENT_ID },
  subsidiary: { id: Netsuite.SUBSIDIARY_ID },
  class: { id: Netsuite.CLASS_ID },
  custbody_order_type: Netsuite.ORDER_TYPE,
  email: order.shipToEmail,
  custbody_primary_contact_name: order.shipTo,
  custbody_primary_contact_email: order.shipToEmail,
  custbody_primary_contact_phone: order.shipToPhone,
  custbodytxn_id: order.transactionId,
  custbodydesign_details_store: order.designEps,
  custbody_fundraiser_id: order.fundraiserId,
  custbodycustbodypayment_method: Netsuite.PAYMENT_METHOD[order.paymentMethod?.toUpperCase()] || Netsuite.PAYMENT_METHOD.STRIPE,
  custbodyorder_confirmation: order.confirmationId,
  customform: {
    id: 126
  },
  externalId: `SO_SnapV4${order.orderId}`,
  custbody_shipment_workflow_stage: Netsuite.WORKFLOW_STAGE.NEW_ORDER,
});

const createAddress = (addressInfo: Partial<OrderInput>, isShipping: boolean) => ({
  addressee: addressInfo.shipTo,
  addr1: isShipping ? addressInfo.street : addressInfo.billingStreet,
  addr2: isShipping ? addressInfo.street2 : addressInfo.billingStreet2,
  city: isShipping ? addressInfo.city : addressInfo.billingCity,
  country: "US",
  state: isShipping ? addressInfo.state : addressInfo.billingState,
  zip: isShipping ? addressInfo.zipCode : addressInfo.billingZipCode,
});

const createLineItem = (lineItem: LineItemInput, name?: string, email?: string) => {
  const { printAttributes } = lineItem;
  return {
    item: { id: lineItem.itemId },
    custcol_recipient_name: name,
    custcol_recipient_email: email,
    rate: lineItem.rate,
    quantity: lineItem.quantity,
    custcol_sku: lineItem.sku,
    ...(printAttributes && {
      custcolname_text: printAttributes?.name?.value,
      custcolname_style: printAttributes?.name?.font,
      custcolname_font_color: printAttributes?.name?.color,
      custcolnumber_text: printAttributes?.number?.value,
      custcolnumber_color: printAttributes?.number?.color
    }),
    ...(lineItem.logoUrl && { custcolcust_col_logourl: lineItem.logoUrl }),
  };
};

const createProductLineItem = (product: OrderProductInput, name?: string, email?: string): NetsuiteLineItem[] => [
  createLineItem({
    itemId: parseInt(product.netsuiteId),
    rate: product.amount,
    quantity: 1,
    sku: product.sku,
    logoUrl: product.logo,
    printAttributes: product.printAttributes as PrintAttributes,
  }, name, email)
];

const createItems = (order: OrderInput): NetsuiteLineItem[] => {
  const items = order.products?.flatMap((product) => createProductLineItem(product, order.shipTo, order.shipToEmail)) ?? [];
  const discountAmount = -Math.abs(order.discountAmount);
  const giftCardAmount = -Math.abs(order.giftCardAmount);
  const discountItem = discountAmount ? [createLineItem({
    itemId: Netsuite.DISCOUNT_ITEM_ID,
    rate: discountAmount,
    quantity: 1,
    sku: `${Netsuite.DISCOUNT_ITEM_ID}`
  })] : [];
  const giftCardItem = giftCardAmount ? [createLineItem({
    itemId: Netsuite.GIFT_CARD_ITEM_ID,
    rate: giftCardAmount,
    quantity: 1,
    sku: `${Netsuite.GIFT_CARD_ITEM_ID}`
  })] : [];
  const taxAmount = order.products?.reduce((acc, product) => acc + (100 * product.taxAmount), 0) / 100;
  const taxItem = taxAmount ? [createLineItem({
    itemId: Netsuite.TAX_ITEM_ID,
    rate: order.taxAmount,
    quantity: 1,
    sku: `${Netsuite.TAX_ITEM_ID}`
  })] : [];
  const pointsItem = order.pointAmount ? [createLineItem({
    itemId: Netsuite.POINTS_ITEM_ID,
    rate: -Math.abs(order.pointAmount),
    quantity: 1,
    sku: `${Netsuite.POINTS_ITEM_ID}`
  })] : [];
  return [...items, ...taxItem, ...discountItem, ...pointsItem, ...giftCardItem];
};


const prepareNetsuiteOrder = async (order: OrderInput) => {
  const baseOrder = createBaseNetsuiteOrder(order);
  const shippingAddress = createAddress(order, true);
  const billingAddress = createAddress(order, false);
  const items = createItems(order);
  const shippingItem = order.shippingCost
    ? [createLineItem({
      itemId: Netsuite.SHIPPING_ITEM_ID,
      rate: Math.abs(order.shippingCost),
      quantity: 1,
      sku: `${Netsuite.SHIPPING_ITEM_ID}`
    })]
    : [];
  return {
    ...baseOrder,
    shippingaddress: shippingAddress,
    billingaddress: billingAddress,
    item: { items: [...items, ...shippingItem] },
  };
};

interface ClosedSalesOrder {
  id: string;
  lastmodifieddate: string;
}

const getClosedSalesOrders = async (n: number) => {
  const resp = await Netsuite.SalesOrder.getClosedSalesOrders(n);
  return resp.body.items as ClosedSalesOrder[];
};

export default {
  orderUrl,
  createCustomerDeposit,
  customerDepositParams,
  createNetsuiteOrder,
  prepareNetsuiteOrder,
  getClosedSalesOrders,
  updateNetsuiteTracking,
  createItemFulfillment: Netsuite.SalesOrder.createItemFulfillment
};
