/* eslint-disable import/max-dependencies */
import Logo from "./logo";
import Order from "./order";
import Ticket from "./ticket";
import Store from "./store";
import Transaction from "./transaction";
import Magento from "./magento";
import Medusa from "./medusa";
import Netsuite from "./netsuite";
import Notification from "./notification";
import Feature from "./feature";
import Donors from "./donors";

export default {
  order: Order,
  logo: Logo,
  ticket: Ticket,
  store: Store,
  transaction: Transaction,
  netsuite: Netsuite,
  medusa: Medusa,
  magento: Magento,
  notification: Notification,
  feature: Feature,
  donors: Donors,
};
