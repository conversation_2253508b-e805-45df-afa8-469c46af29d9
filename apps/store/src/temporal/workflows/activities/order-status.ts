import Repo from "@store-monorepo/repo";
import Comms from "../../../lib/comms";

export const getOrderDetails = async (orderId: string) => {
  const order = await Repo.Order.findById(orderId);
  if (!order) {
    throw new Error("Order not found");
  }
  return order;
};

export const sendStatusUpdateEmail = async (templateName: any, order: any) => {
  if (!order.shipToEmail) {
    throw new Error("No email address found for order");
  }

  return await Comms.sendEmail(templateName, [{ to: order.shipToEmail }], {
    orderId: order.netsuiteId,
    trackingNumber: order.trackingNumber,
    status: order.status,
    vendor: order.vendor,
    shipTo: order.shipTo,
    shipToEmail: order.shipToEmail,
    trackingUrl: order.trackingUrl
  });
};
