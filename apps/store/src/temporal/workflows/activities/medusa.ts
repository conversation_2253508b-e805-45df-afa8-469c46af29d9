/* eslint-disable complexity, max-statements */
import Medusa from "@store-monorepo/medusa-api";

const getOrderById = async (orderId: string) => {
  return Medusa.Order.get(orderId);
};

const updateMetadataByOrderId = async (orderId: string, metadata: Record<string, unknown>) => {
  return Medusa.Order.updateMetadata(orderId, metadata);
};

export default {
  updateMetadataByOrderId,
  getOrderById
};
