/* eslint-disable functional/no-loop-statements, functional/no-let, functional/immutable-data */
import Upload from "@store-monorepo/logo-uploader";
import Repo from "@store-monorepo/repo";
import { ImageInput } from "../../../api/graphql/types";

import { Order } from "../../../types";

const customLogoUrl = (id: string) => {
  const host = {
    local: "http://localhost:3000",
    dev: "https://store.dev.snap.app",
    staging: "https://store.staging.snap.app",
    production: "https://store.snap.app"
  }[process.env.NODE_ENV as string] ?? "https://store.snap.app";
  return `${host}/api/custom-logo/${id}_d.png`;
};

const uploadLogo = async (input: ImageInput) => {
  await Upload.fromS3Url(input.url);
};

const createCustomLogos = async (order: Order): Promise<Order> => {
  for (let i = 0; i < order.products.length; i++) {
    const product = order.products[i];

    if (product.printAttributes) {
      const logoSvg = customLogoUrl(product.id);
      const op = await Repo.OrdersOnProducts.updateWhere({ id: product.id }, { backLogo: logoSvg });
      order.products[i] = { product: order.products[i].product, ...op };
    }
  }

  return order;
};

export default {
  uploadLogo, createCustomLogos
};
