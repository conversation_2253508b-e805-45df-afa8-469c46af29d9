/* eslint-disable functional/immutable-data, sonarjs/cognitive-complexity */
import { STATUS } from "@prisma/client";

import Ticket from "@store-monorepo/zendesk-api";
import Repo from "@store-monorepo/repo";
import Graphql from "@store-monorepo/graphql";

import { Order } from "../../../types";
import NetsuiteHelper from "../../../lib/netsuite";
import OrderHelper from "../../../lib/order";

const catchFailedOrder = async (order: Order) => {
  const result = {
    missingLogoUrls: null,
    ticket: null
  };

  const fundraiser = order.fundraiserId
    ? await Graphql.publicFundraiserData(parseInt(order.fundraiserId))
    : null;

  result.missingLogoUrls = await OrderHelper.missingLogoUrls(order.products);

  result.ticket = await Ticket.createFailedOrderHandlingTicket(order, {
    ...(fundraiser ? { fundraiser: { id: fundraiser.id, name: fundraiser.name } } : {}),
    netsuiteId: order.netsuiteId,
    netsuiteUrl: NetsuiteHelper.orderUrl(order.netsuiteId),
    missingLogoUrls: result.missingLogoUrls
  });

  await Repo.Order.updateWhere({ netsuiteId: order.netsuiteId! }, {
    status: STATUS.HOLD
  });

  return result;
};

const checkDesignFiles = async (order: Order, skipTicketCreation = false) => {
  const result = {
    missingLogoUrls: null,
    ticket: null
  };

  result.missingLogoUrls = await OrderHelper.missingLogoUrls(order.products);

  if (result.missingLogoUrls.length > 0) {
    if (!skipTicketCreation) {
      const fundraiser = order.fundraiserId
        ? await Graphql.publicFundraiserData(parseInt(order.fundraiserId))
        : null;

      result.ticket = await Ticket.createCorruptedDesignFileTicket(order, {
        ...(fundraiser ? { fundraiser: { id: fundraiser.id, name: fundraiser.name } } : {}),
        netsuiteId: order.netsuiteId,
        netsuiteUrl: NetsuiteHelper.orderUrl(order.netsuiteId),
        missingLogoUrls: result.missingLogoUrls
      });
    }

    await Repo.Order.updateWhere({ netsuiteId: order.netsuiteId! }, {
      status: STATUS.HOLD
    });
  }

  return result;
};

export default {
  catchFailedOrder,
  checkDesignFiles
};
