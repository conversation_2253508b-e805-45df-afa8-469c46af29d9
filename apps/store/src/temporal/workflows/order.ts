/* eslint-disable functional/immutable-data */
/* eslint-disable import/group-exports, max-len, max-lines, max-depth, capitalized-comments, no-warning-comments, max-statements, import/prefer-default-export,max-lines-per-function, etc/no-commented-out-code, complexity, sonarjs/cognitive-complexity */
import { proxyActivities, sleep, setHandler } from "@temporalio/workflow";
import { OrderInput } from "../../api/graphql/types";
import Activities from "./activities";
import Signals from "./signals";

const STATUS = {
  DESIGN: "DESIGN",
  HOLD: "HOLD",
  SHIPPED: "SHIPPED",
  CREATED: "CREATED",
  REJECTED: "REJECTED",
  MARCO_REJECTED: "MARCO_REJECTED"
};

const { prepareNetsuiteOrder, createNetsuiteOrder, createCustomerDeposit, customerDepositParams, getClosedSalesOrders } = proxyActivities<typeof Activities.netsuite>({
  startToCloseTimeout: "1 minute",
  retry: {
    maximumAttempts: 3
  }
});

const { sendNetsuiteIdToMagento } = proxyActivities<typeof Activities.magento>({
  startToCloseTimeout: "1 minute",
  retry: {
    maximumAttempts: 3
  }
});

const { updateMetadataByOrderId, getOrderById } = proxyActivities<typeof Activities.medusa>({
  startToCloseTimeout: "1 minute",
  retry: {
    maximumAttempts: 3
  }
});

const { featureOn } = proxyActivities<typeof Activities.feature>({
  startToCloseTimeout: "1 minute",
  retry: {
    maximumAttempts: 3
  }
});

const { createOrderDraft, recreateOrder, updateNetsuiteOrderStatus, updateDBOrderStatus, closeDBOrders, retrieveOrder, updateOrderToShipped } = proxyActivities<typeof Activities.order>({
  startToCloseTimeout: "1 minute",
  retry: {
    maximumAttempts: 3
  }
});

const { createMarcoOrder } = proxyActivities<typeof Activities.order>({
  startToCloseTimeout: "12h",
  retry: {
    maximumAttempts: 3,
    initialInterval: "4h",
    backoffCoefficient: 1
  }
});

const { catchFailedOrder, checkDesignFiles } = proxyActivities<typeof Activities.ticket>({
  startToCloseTimeout: "1 minute",
  retry: {
    maximumAttempts: 3
  }
});

const { createCustomLogos } = proxyActivities<typeof Activities.logo>({
  startToCloseTimeout: "1 minute",
  retry: {
    maximumAttempts: 3
  }
});

const { createItemFulfillment } = proxyActivities<typeof Activities.netsuite>({
  startToCloseTimeout: "4h",
  retry: {
    maximumAttempts: 3,
    backoffCoefficient: 5
  }
});

enum NetsuiteStatus {
  Design = "Design",
  Shipped = "Shipped",
  MarcoCreated = "MarcoCreated",
  MarcoRejected = "MarcoRejected",
  Rejected = "Rejected"
}

export const updateExternalTracking = async (order) => {
  await updateOrderToShipped(order);
  await createItemFulfillment(order);
};

export const closeOrders = async () => {
  const orders = await getClosedSalesOrders(40);
  const date = new Date();
  date.setDate(date.getDate() - 3);

  const filteredOrderIds = orders.filter(order => {
    const lastModifiedDate = new Date(order.lastmodifieddate);
    return lastModifiedDate >= date;
  }).map(order => order.id);


  return closeDBOrders(filteredOrderIds);
};

export const triggerOrderSend = async (netsuiteId: string) => {
  const order = await retrieveOrder(netsuiteId);
  await updateNetsuiteOrderStatus(order, NetsuiteStatus.Design);
};

export const createOrder = async (params: OrderInput, restartWorkflow: boolean) => {
  const medusaOn = await featureOn("STOR-4106-medusa-create-order", { source: params.source });
  if (!medusaOn) {
    return null;
  }

  const salesOrder = await (async () => {
    if (!params.netsuiteId) {
      const salesOrderRequest = await prepareNetsuiteOrder(params);
      const netsuiteId = await createNetsuiteOrder(salesOrderRequest);
      params.netsuiteId = params.packingSlipId = `${netsuiteId}`;

      if (params.source === "MEDUSA") {
        const { order } = await getOrderById(params.orderId);
        const newMetadata = order.metadata ? { ...order.metadata, netsuiteId: params.netsuiteId } : { netsuiteId: params.netsuiteId };
        await updateMetadataByOrderId(params.orderId, newMetadata);
      } else {
        await sendNetsuiteIdToMagento(params.orderId, params.netsuiteId);
      }

      return salesOrderRequest;
    }
    return null;
  })();
  if (!salesOrder && !params.netsuiteId && params.vendor !== "MARCO") {
    return;
  }

  setHandler(Signals.order.createCustomerDeposit, async (body) => {
    if (body.transactionType === "EARNED") {
      const customerDeposit = await customerDepositParams(salesOrder, params, body.customerLiability);
      await createCustomerDeposit(customerDeposit);
    }
  });

  const { transactionId, paymentMethod, shippingCost, update, pointAmount, ...dbOrderParams } = params;

  // eslint-disable-next-line functional/no-let
  let order = await createOrderDraft({ ...dbOrderParams, netsuiteId: params.netsuiteId, shippingCost });
  if (restartWorkflow) {
    await updateNetsuiteOrderStatus(order, NetsuiteStatus.Design);
  }
  if (order.products.some(product => !!product.printAttributes)) {
    order = await createCustomLogos(order);
  }
  try {
    const designFiles = await checkDesignFiles(order);
    if (designFiles.missingLogoUrls.length) {
      await updateNetsuiteOrderStatus(order, NetsuiteStatus.Rejected);
      await updateDBOrderStatus(order.id, STATUS.REJECTED);
    }
  } catch (error) {
    await updateNetsuiteOrderStatus(order, NetsuiteStatus.Rejected);
    await updateDBOrderStatus(order.id, STATUS.REJECTED);
  }

  setHandler(Signals.order.updateOrder, async (event) => {
    // update order only if it's not a STORE order for now && it's in DESIGN
    if (event.record.fields.custbody_shipment_workflow_stage === "2") {
      const updatedOrder = await recreateOrder(event);
      const withBackprints = await createCustomLogos(updatedOrder);
      try {
        const designFiles = await checkDesignFiles(withBackprints, true);
        if (designFiles.missingLogoUrls.length) {
          await updateNetsuiteOrderStatus(updatedOrder, NetsuiteStatus.Rejected);
          await updateDBOrderStatus(updatedOrder.id, STATUS.REJECTED);
        } else {
          const marcoOrder = await createMarcoOrder(updatedOrder);
          if (marcoOrder) {
            await updateNetsuiteOrderStatus(updatedOrder, NetsuiteStatus.MarcoCreated);
            await updateDBOrderStatus(updatedOrder.id, STATUS.CREATED);
          }
        }
      } catch (error) {
        await updateNetsuiteOrderStatus(updatedOrder, NetsuiteStatus.MarcoRejected);
        await updateDBOrderStatus(updatedOrder.id, STATUS.MARCO_REJECTED);
        await catchFailedOrder(order);
      }
    }
  });

  if (params.scheduleAt) {
    const delay = new Date(params.scheduleAt).getTime() - Date.now();
    if (delay > 0) {
      await sleep(delay);
    }
  }

  try {
    const designFiles = await checkDesignFiles(order, true);
    if (designFiles.missingLogoUrls.length) {
      await updateNetsuiteOrderStatus(order, NetsuiteStatus.Rejected);
      await updateDBOrderStatus(order.id, STATUS.REJECTED);
      await sleep("10days");
    } else {
      const marcoOrder = await createMarcoOrder(order);
      if (marcoOrder) {
        await updateNetsuiteOrderStatus(order, NetsuiteStatus.MarcoCreated);
        await updateDBOrderStatus(order.id, STATUS.CREATED);
        return;
      }
      await sleep("10days");
    }
  } catch (error) {
    console.log(error.message);
    await updateNetsuiteOrderStatus(order, NetsuiteStatus.MarcoRejected);
    await catchFailedOrder(order);
    await updateDBOrderStatus(order.id, STATUS.MARCO_REJECTED);
    const tenDaysInMilliseconds = 10 * 24 * 60 * 60 * 1000;
    await sleep(tenDaysInMilliseconds);
  }
};
