/* eslint-disable complexity, import/prefer-default-export, sonarjs/cognitive-complexity */
import { proxyActivities } from "@temporalio/workflow";
import { RaiseFundraiser } from "@store-monorepo/repo";
import Activities from "./activities";

const {
  serializeStore, synchronizeStore, verifyLogos, originalStore,
  createMagentoStore, updateBuiltStore, slackMessage, findStore, checkStoreStatus,
  updateZendeskWithLogoChanges, createKlaviyoProfile, sendStoreBuildReadyEmail,
  updateRaiseStoreUrl, setStoreSchool
} = proxyActivities<typeof Activities.store >({
  startToCloseTimeout: "1 minute",
  retry: {
    maximumAttempts: 3,
  },
});

const { featureOn } = proxyActivities<typeof Activities.feature>({
  startToCloseTimeout: "1 minute",
  retry: {
    maximumAttempts: 1,
  },
});

export const createStore = async (fundraiser: RaiseFundraiser) => {
  const newStoreAttrs = await serializeStore(fundraiser);
  const existingStore = await findStore(newStoreAttrs);
  const syncedStore = await synchronizeStore(newStoreAttrs, existingStore);
  const [logosVerified, store] = await verifyLogos(syncedStore);
  await updateZendeskWithLogoChanges(store, existingStore);
  await setStoreSchool(store);

  const previousStore = await originalStore(store);
  if (previousStore && !existingStore?.builtAt) {
    const reusedStore = await updateBuiltStore(store, previousStore);

    if (await featureOn("STOR-3996-store-creation-cutover", { fundraiserId: reusedStore.fundraiserId })) {
      await createKlaviyoProfile(reusedStore);
      if (store.fundraiserId) await updateRaiseStoreUrl(reusedStore);
    }
    await slackMessage(reusedStore, ":rocket:", `Launching Store: *${reusedStore.name}* using previous URL (${reusedStore.storeUrl})`);
    return `Launched Store: '${reusedStore.name}' using previous URL (${reusedStore.storeUrl})`;
  }

  const status = await checkStoreStatus(store, logosVerified);
  if (status.skip) return "Sync Performed, Store Creation Skipped";

  if (await featureOn("STOR-3996-store-creation-list-of-exceptions", {
    organizationId: store.organizationId,
    partnerId: store.partnerId
  })) {
    const { magentoStoreId, magentoStoreCode, magentoManagerEmail, error, warning } = await createMagentoStore(store);
    if (error) {
      await slackMessage(store, ":red-negative:", `Failed to Launch Store ${store.id} with *Magento Error*\n${error}`);
      return `Attempted Store Creation But Failed: '${error}'`;
    }
    if (warning) {
      await slackMessage(store, ":warning:", `Store ${store.id} Scope created but with *Magento Error*\n${warning}`);
    }

    await updateBuiltStore({ ...store, magentoStoreId, magentoStoreCode, magentoManagerEmail });

    if (await featureOn("STOR-3996-store-creation-cutover", { fundraiserId: store.fundraiserId })) {
      await sendStoreBuildReadyEmail(store);
      await createKlaviyoProfile(store);
      if (store.fundraiserId) await updateRaiseStoreUrl(store);
    }
  }

  await slackMessage(store, ":rocket:", `Launching Store: *${store.name}* (${store.storeUrl})`);
  return `Launched Store: '${store.name} (${store.storeUrl})'`;
};
