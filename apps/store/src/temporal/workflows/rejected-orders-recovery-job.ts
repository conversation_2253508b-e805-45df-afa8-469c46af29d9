import { Order as PrismaOrder, OrdersOnProducts, Product } from "@prisma/client";
import { proxyActivities, executeChild, ChildWorkflowCancellationType, ParentClosePolicy } from "@temporalio/workflow";

import Activities from "./activities";

enum OrderStatus {
  Failure = 'FAILURE',
  Success = 'SUCCESS'
}

type OrderWithProducts = PrismaOrder & { products: (OrdersOnProducts & { product: Product })[] };

type OrderResult = {
  errors?: string[];
  netsuiteId?: string;
  status?: OrderStatus;
}

const {
  findRejectedOrders,
  canRejectedOrderBeRecovered,
  recoverRejectedOrderByRestarting
} = proxyActivities<typeof Activities.order>({
  startToCloseTimeout: "1 minute",
  retry: {
    maximumAttempts: 1
  }
});

export const rejectedOrderRecovery= async (order: OrderWithProducts): Promise<OrderResult> => {
  if(!await canRejectedOrderBeRecovered(order)) {
    return {
      status: OrderStatus.Failure,
      errors: ["Order cannot be restarted, still missing logos"],
      netsuiteId: order.netsuiteId,
    };
  }

  return await recoverRejectedOrderByRestarting(order);
};

export const rejectedOrdersRecoveryJob = async () => {
  const orders = await findRejectedOrders();
  const result = [];

  if (orders.length > 0) {
    for await(const order of orders) {
      result.push(await executeChild("rejectedOrderRecovery", {
        workflowId: `rejected-order-recovery-${order.netsuiteId}`,
        args: [order],
        parentClosePolicy: ParentClosePolicy.PARENT_CLOSE_POLICY_ABANDON,
        cancellationType: ChildWorkflowCancellationType.ABANDON
      }));
    }
  }

  return result;
};
