/* eslint-disable complexity, import/group-exports, capitalized-comments, etc/no-commented-out-code */
import { proxyActivities } from "@temporalio/workflow";
import Activities from "./activities";

const { getFundraiser, getFundraiserDonationState, findStoreByFundraiserId, sendEmailToDonors } = proxyActivities<typeof Activities.donors>({
  startToCloseTimeout: "1 minute",
  retry: {
    maximumAttempts: 3,
  },
});

const { featureOn } = proxyActivities<typeof Activities.feature>({
  startToCloseTimeout: "1 minute",
  retry: {
    maximumAttempts: 3,
  },
});

export const sendRecapFundraiserEmail = async (fundraiserId: number) => {
  if (!(await featureOn("STOR-3957-otf-recap-fundraiser-email", { fundraiserId }))) return "Feature flag is off";

  const store = await findStoreByFundraiserId(fundraiserId);
  if (!store) return "Store not found";
  if (![store.fanStore, store.storeUrl].every(Boolean)) return "The store does not meet the requirements";

  const fundraiser = await getFundraiser(fundraiserId);
  if (!fundraiser) return "Fundraiser not found.";
  if (![fundraiser.storeUrl].every(Boolean)) return "The fundraiser does not meet the requirements";

  const { donors, stats } = await getFundraiserDonationState(fundraiserId);
  if (!donors?.length) return "No donors found";

  return sendEmailToDonors(store, fundraiser, donors, stats);
};
