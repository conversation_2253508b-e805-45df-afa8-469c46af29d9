/* eslint-disable max-lines-per-function, import/prefer-default-export */
import { proxyActivities } from "@temporalio/workflow";
import Activities from "./activities";
import { MagentoConsumerPointsLiability } from "./activities/transaction";

const {
  processPointsTransactionTransfer,
  processPointsByTransactionType,
  updateMagentoPointsTransactionNetsuiteId
} = proxyActivities<typeof Activities.transaction>({
  startToCloseTimeout: "1 minute",
  retry: {
    maximumAttempts: 1
  }
});

export const handlePointsTransaction = async (params: MagentoConsumerPointsLiability) => {
  const netsuiteResponse = await processPointsByTransactionType(params);
  if (!netsuiteResponse) return processPointsTransactionTransfer(params);
  return await updateMagentoPointsTransactionNetsuiteId({
    transactionId: params.transactionId,
    netsuiteInternalId: netsuiteResponse.netsuiteInternalId,
    netsuiteExternalId: netsuiteResponse.netsuiteExternalId,
  });
};
