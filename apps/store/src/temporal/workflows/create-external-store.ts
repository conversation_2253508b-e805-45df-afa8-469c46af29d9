/* eslint-disable complexity, import/prefer-default-export */
import { proxyActivities } from "@temporalio/workflow";
import { StoreInput } from "../../api/graphql/types";
import Activities from "./activities";

const {
  serializeExternalStore, createStore, verifyLogos,
  createMagentoStore, updateBuiltStore, slackMessage,
  createKlaviyoProfile, sendStoreBuildReadyEmail, updateRaiseStoreUrl,
  setStoreSchool
} = proxyActivities<typeof Activities.store>({
  startToCloseTimeout: "1 minute",
  retry: {
    maximumAttempts: 3,
  },
});

const { featureOn } = proxyActivities<typeof Activities.feature>({
  startToCloseTimeout: "1 minute",
  retry: {
    maximumAttempts: 1,
  },
});

export const createExternalStore = async (storeInput: StoreInput, overrideSkipLogic: boolean = false) => {
  const newStoreAttrs = await serializeExternalStore(storeInput);
  const newStore = await createStore(newStoreAttrs);
  const [, store] = await verifyLogos(newStore);
  await setStoreSchool(store);

  if (overrideSkipLogic || await featureOn("STOR-3996-store-creation-list-of-exceptions", {
    organizationId: store.organizationId,
    partnerId: store.partnerId
  })) {
    const { magentoStoreId, magentoStoreCode, magentoManagerEmail, error, warning } = await createMagentoStore(store);
    if (error) {
      await slackMessage(store, ":red-negative:", `Failed to Launch External Store ${store.id} with *Magento Error*\n${error}`);
      return `Attempted Store Creation But Failed: '${error}'`;
    }
    if (warning) {
      await slackMessage(store, ":warning:", `External Store ${store.id} Scope created but with *Magento Error*\n${warning}`);
    }
    await updateBuiltStore({ ...store, magentoStoreId, magentoStoreCode, magentoManagerEmail });

    if (await featureOn("STOR-3996-store-creation-cutover", { fundraiserId: store.fundraiserId ?? 0 })) {
      await sendStoreBuildReadyEmail(store);
      await createKlaviyoProfile(store);
      if (store.fundraiserId) await updateRaiseStoreUrl(store);
    }
  }

  await slackMessage(store, ":rocket:", `Launching External Store: *${store.name}* (${store.storeUrl})`);
  return `Launched External Store: '${store.name} (${store.storeUrl})'`;
};
