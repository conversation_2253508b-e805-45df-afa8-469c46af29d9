import { defineSignal } from "@temporalio/workflow";
import { MagentoConsumerPointsLiabilityInput, MarcoEventInput } from "../../../api/graphql/types";

type HasNameOrEmail = ({custcol_recipient_name: string | undefined, custcol_recipient_email: string }
  | {custcol_recipient_name: string, custcol_recipient_email: string | undefined })

// Raise ensures at least one of recipient name or email is present
type NetsuiteItem = {
  item: string
  custcol_recipient_name: string | undefined
  custcol_recipient_email: string
} & HasNameOrEmail

type Event = {
  eventType: string;
  record: {
    id: string;
    fields: {
      custbody_shipment_workflow_stage: string
    }
    sublists: {
      item: Record<string, NetsuiteItem>
    }
  }
}

const orderSend = defineSignal("orderSend");
const orderClosed = defineSignal("orderClosed");
const updateOrder = defineSignal<[Event]>("updateOrder");

const marcoOrderUpdate = defineSignal<[MarcoEventInput]>("marcoOrderUpdate");

const createCustomerDeposit = defineSignal<[MagentoConsumerPointsLiabilityInput]>("createCustomerDeposit");
const orderSignals = {
  createCustomerDeposit,
  orderSend,
  orderClosed,
  updateOrder,
  marcoOrderUpdate
};

export default orderSignals;
