/* eslint-disable indent */
/* eslint-disable complexity */
/* eslint-disable import/prefer-default-export */
import { proxyActivities } from "@temporalio/workflow";
import { Order, STATUS } from "@prisma/client";
import Activities from "./activities";

const {
  updateDBOrderStatus,
  getStatusUpdateEmailInput,
  sendStatusUpdateEmail,
  updateOrderToShipped
} = proxyActivities<typeof Activities.order>({
  startToCloseTimeout: "1 minute",
  retry: {
    maximumAttempts: 3,
  },
});

const { createItemFulfillment } = proxyActivities<typeof Activities.netsuite>({
  startToCloseTimeout: "4h",
  retry: {
    maximumAttempts: 3,
    backoffCoefficient: 5
  }
});
export const handleTrackingStatus = async (order: Order, status: STATUS) => {
  try {
    const updatedOrder = await (async () => {
      if (status === STATUS.SHIPPED) {
        const uo = await updateOrderToShipped(order);
        await createItemFulfillment(uo);
        return uo;
      }
      return await updateDBOrderStatus(order.id, status);
    })();

    const input = await getStatusUpdateEmailInput(updatedOrder, status);
    if (input) {
      await sendStatusUpdateEmail(input);
    }

    return { success: true };
  } catch (error) {
    console.error("Error handling order tracking status:", error);
    return { success: false, error: error.message };
  }
};
