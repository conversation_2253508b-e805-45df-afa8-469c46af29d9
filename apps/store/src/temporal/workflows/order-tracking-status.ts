import { proxyActivities } from "@temporalio/workflow";
import { Order, STATUS } from "@prisma/client";
import Activities from "./activities";
import { OrderInput } from "../../api/graphql/types";

const { updateDBOrderStatus, getStatusUpdateEmailInput, sendStatusUpdateEmail } = proxyActivities<typeof Activities.order>({
  startToCloseTimeout: "1 minute",
  retry: {
    maximumAttempts: 3,
  },
});

export const handleTrackingStatus = async (order: Order, status: string) => {
  try {
    let mappedStatus: STATUS;
    switch (status) {
      case "delivered":
        mappedStatus = STATUS.DELIVERED;
        break;
      case "shipped":
      case "in_transit":
        mappedStatus = STATUS.SHIPPED;
        break;
      default:
        throw new Error(`Unknown order status: ${status}`);
    }

    if (mappedStatus === STATUS.SHIPPED && order.shippingReceivedAt != null) {
      return { success: true, message: "SHIPPED status already handled - ignoring updates." };
    }
    await updateDBOrderStatus(order.id, mappedStatus);
    const input = await getStatusUpdateEmailInput(order, mappedStatus);
    if (input) {
      await sendStatusUpdateEmail(input);
    }

    return { success: true };
  } catch (error) {
    console.error("Error handling order tracking status:", error);
    return { success: false, error: error.message };
  }
};
