/* eslint-disable complexity, max-statements, sonarjs/cognitive-complexity, functional/no-let */
import { OrdersOnProducts, Order, Product } from "@prisma/client";
import { OrderInput, OrderProductInput, PrintAttributesInput, Color, Priority, ShippingProvider, Vendor } from "../api/graphql/types";

const missingLogoUrls = async (products: Array<OrdersOnProducts> = []): Promise<string[]> => {
  const allLogos: string[] = products.map((product: OrdersOnProducts) => product.logo);
  const uniqueLogos: string[] = [...new Set(allLogos.filter(Boolean))];

  const status = await Promise.allSettled<Response>(uniqueLogos.map(url => fetch(url)));

  return status.reduce((acc: string[], check: PromiseSettledResult<Response>) => {
    if (check.status === "fulfilled" && check.value.ok) {
      return acc.filter(url => check.value.url !== url);
    }
    return acc;
  }, allLogos);
};

type Valid = { valid: true };
type Invalid = { valid: false, errors: string[] };
type Validation = Valid | Invalid;

const validatePrintAttributes = (products: Array<OrderProductInput> = []): Validation => {
  const attrs: Array<PrintAttributesInput> = products.map(p => p.printAttributes).filter(Boolean);
  let errors = [];
  const validColors = [Color.Black, Color.White];

  if (attrs.findIndex(a => a.name?.value && a.name.value.length > 13) >= 0) {
    errors = [...errors, `Name is too long.`];
  }

  if (attrs.findIndex(a => a.number?.value && a.number.value.length > 2) >= 0) {
    errors = [...errors, `Number is too long.`];
  }

  if (attrs.findIndex(a => a.number?.value && !a.number.value.match(/^[0-9]+$/)) >= 0) {
    errors = [...errors, `Number must be a number.`];
  }

  if (attrs.findIndex(a => a.name?.color && !validColors.includes(a.name?.color)) >= 0) {
    errors = [...errors, `Color is not supported.`];
  }

  if (attrs.findIndex(a => a.number?.color && !validColors.includes(a.number?.color)) >= 0) {
    errors = [...errors, `Color is not supported.`];
  }

  return errors.length > 0 ? { valid: false, errors } : { valid: true };
};

const validateProductLogos = (products: Array<OrderProductInput> = []): Validation => {
  const productsWithEmptyLogo = products.filter(product => !product.logo);

  if (productsWithEmptyLogo.length > 0) {
    return {
      valid: false,
      errors: productsWithEmptyLogo.map(product => `${product.name || product.sku} (${product.netsuiteId})`)
    };
  }

  return { valid: true };
};

const formatDate = (date: Date): string => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");
  const hours = String(date.getHours()).padStart(2, "0");
  const minutes = String(date.getMinutes()).padStart(2, "0");
  const seconds = String(date.getSeconds()).padStart(2, "0");

  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
};

const toOrderInput = (order: Order & { products: (OrdersOnProducts & { product: Product })[] }): OrderInput => {
  return {
    city: order.city,
    carrier: order.carrier as ShippingProvider,
    vendor: order.vendor as Vendor,
    packingSlipTitle: order.packingSlipTitle!,
    state: order.state as string,
    packingSlipId: order.packingSlipId!,
    street: order.street,
    zipCode: order.zipCode,
    shipTo: order.shipTo,
    confirmationId: order.confirmationId,
    netsuiteId: order.netsuiteId,
    orderId: order.orderId,
    priority: Priority.Normal,
    scheduleAt: formatDate(order.scheduleAt),
    products: order.products.map(p => ({
      logo: p.logo!,
      printAttributes: p.printAttributes as PrintAttributesInput,
      receiverName: p.receiverName!,
      netsuiteId: p.product.netsuiteId!
    }))
  };
};
export type {
  Valid, Invalid, Validation
};

export default {
  missingLogoUrls,
  toOrderInput,
  validatePrintAttributes,
  validateProductLogos
};
