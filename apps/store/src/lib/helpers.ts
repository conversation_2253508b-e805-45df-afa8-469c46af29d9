import { Request } from "express";

import { Environment } from "../types";

const SSO_URL: Record<Environment, string> = {
  [Environment.local]: "https://accounts.dev.snap.app",
  [Environment.dev]: "https://accounts.dev.snap.app",
  [Environment.staging]: "https://accounts.staging.snap.app",
  [Environment.production]: "https://accounts.snap.app"
};

const STORE_URL: Record<Environment, string> = {
  [Environment.local]: "http://localhost:3000",
  [Environment.dev]: "https://store.dev.snap.app",
  [Environment.staging]: "https://store.staging.snap.app",
  [Environment.production]: "https://store.snap.app"
};

const cookieName = (name: string): string => {
  return (
    name +
    (process.env.NODE_ENV === Environment.production
      ? ""
      : `-${process.env.NODE_ENV}`)
  );
};

const fetchToken = (req: Request): string | null => {
  const name = cookieName("token");

  if (req.cookies[name]) {
    return req.cookies[name];
  }

  if (typeof req.query.token === "string") {
    return req.query.token;
  }

  return null;
};

const redirectTo = (req: Request): string => {
  const sso = SSO_URL[process.env.NODE_ENV];
  const store = STORE_URL[process.env.NODE_ENV];
  const to = encodeURIComponent(`${store}${req.originalUrl}`);

  return `${sso}/login?redirectTo=${to}`;
};

const formatAddress = (order: Record<string, any>, type: string): string => {
  const get = (field: string) => order[field] ?? "";
  const isBilling = type === "billing";

  const fullName = get("shipTo");

  const street1 = isBilling ? get("billingStreet") : get("street");
  const street2 = isBilling ? get("billingStreet2") : get("street2");

  const city = isBilling ? get("billingCity") : get("city");
  const state = isBilling ? get("billingState") : get("state");
  const zip = isBilling ? get("billingZipCode") : get("zip_code");

  const regionLine = [city, state, zip].filter(Boolean).join(", ");
  const country = "United States";

  const lines = [
    fullName,
    street1,
    street2,
    regionLine,
    country
  ].filter(Boolean);

  return lines.join("\n");
}

export default {
  cookieName, fetchToken, redirectTo, formatAddress
};
