import type { StateCode } from "@store-monorepo/graphql/types";

export const STATES: Record<StateCode, string> = {
  "AK": "Alaska",
  "AL": "Alabama",
  "AR": "Arkansas",
  "AS": "American Samoa",
  "AZ": "Arizona",
  "CA": "California",
  "CO": "Colorado",
  "CT": "Connecticut",
  "DC": "District of Columbia",
  "DE": "Delaware",
  "FL": "Florida",
  "GA": "Georgia",
  "GU": "Guam",
  "HI": "Hawaii",
  "IA": "Iowa",
  "ID": "Idaho",
  "IL": "Illinois",
  "IN": "Indiana",
  "KS": "Kansas",
  "KY": "Kentucky",
  "LA": "Louisiana",
  "MA": "Massachusetts",
  "MD": "Maryland",
  "ME": "Maine",
  "MI": "Michigan",
  "MN": "Minnesota",
  "MO": "Missouri",
  "MP": "",
  "MS": "Mississippi",
  "MT": "Montana",
  "NC": "North Carolina",
  "ND": "North Dakota",
  "NE": "Nebraska",
  "NH": "New Hampshire",
  "NJ": "New Jersey",
  "NM": "New Mexico",
  "NV": "Nevada",
  "NY": "New York",
  "OH": "Ohio",
  "OK": "Oklahoma",
  "OR": "Oregon",
  "PA": "Pennsylvania",
  "PR": "Puerto Rico",
  "RI": "Rhode Island",
  "SC": "South Carolina",
  "SD": "South Dakota",
  "TN": "Tennessee",
  "TX": "Texas",
  "UM": "",
  "UT": "Utah",
  "VA": "Virginia",
  "VI": "",
  "VT": "Vermont",
  "WA": "Washington",
  "WI": "Wisconsin",
  "WV": "West Virginia",
  "WY": "Wyoming",
};

export const inState = (state?: string): string[] | undefined => {
  if(!state) return;

  const result = Object.entries(STATES).find(([code, name]) => code.toUpperCase() === state.toUpperCase() || name.toLowerCase() === state.toLowerCase());
  if(result) return [result[0], result[1].toLowerCase()];
};

export const stateCode = (state?: string): string | undefined => {
  if(!state) return;
  return inState(state)?.[0];
};
