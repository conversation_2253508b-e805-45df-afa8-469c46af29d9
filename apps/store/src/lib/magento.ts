/* eslint-disable max-lines-per-function, max-lines, max-statements, complexity, sonarjs/cognitive-complexity */
import Prisma from "@prisma/client";
import { on as featureOn } from "@store-monorepo/splitio-client";
import { StoreType } from "@store-monorepo/repo";

const { MAGENTO_USERNAME, MAGENTO_PASSWORD, MAGENTO_WORKER_URL, MAGENTO_BASE_URL } = process.env;

const LOGO_BUCKET = "https://snapraiselogos.s3.amazonaws.com";

const toDomain = (url: string): string => {
  return !url.includes("//")
    ? url :
    `${url.split("//")[1]?.replace(/\/$/, "")}`;
};

const isProductionReady = async (store: StoreType) => {
  return await featureOn("STOR-3996-store-creation-cutover", { fundraiserId: store.fundraiserId ?? 0 })
    ? {
      storeCode: store.storeCode ?? `SNP${store.fundraiserEntityId}`,
      domain: toDomain(store.storeUrl),
      groupLeaderEmail: store.groupLeaderEmail ?? null,
      salesRepId: store.salesRepEmail ?? null,
      accountManagerId: store.accountManagerEmail ?? null,
      teamId: store.teamId
    }
    : {
      storeCode: store.storeCode ?? `SNPTEST${store.fundraiserEntityId}`,
      domain: `999${toDomain(store.storeUrl)}`,
      groupLeaderEmail: store.groupLeaderEmail ? `999_${store.groupLeaderEmail}` : null,
      salesRepId: store.salesRepEmail ? `999_${store.salesRepEmail}` : null,
      accountManagerId: store.accountManagerEmail ? `999_${store.accountManagerEmail}`: null,
      teamId: `999_${store.teamId}`,
    };
};

const constructCardDescriptor = (name: string) => {
  const sanitizedName = name.replaceAll(/[-|]/g, "").replaceAll("&", "and").
    replaceAll(/20|21|22|23|24/g, "").
    slice(0, 15);
  return `${sanitizedName} Gear`;
};

const expectedLogoUrlFor = (url: string | null, type: string, fundraiserId?: number): string => url || `${LOGO_BUCKET}/PROD-SVG/${fundraiserId || "null-logo"}_${type[0]}.svg`;

const toPrinterLogo = (url: string) => url.replace("PROD-SVG/", "PrinterLogos/").replace(".svg", ".png");

const toMagentoStore = async (store: StoreType) => {
  const {
    storeCode,
    domain,
    groupLeaderEmail,
    salesRepId,
    accountManagerId,
    teamId
  } = await isProductionReady(store);

  return {
    store_data: {
      name: storeCode,
      code: storeCode,
      enabled: 1
    },
    extension_attributes: {
      parent_store_id: 1,
      domain: domain,
      primary_color: store.logoPrimaryColor.split("|")[0],
      favicon: toPrinterLogo(expectedLogoUrlFor(store.logoDigitalUrl, "digital", store.fundraiserId)),
      header_logo: toPrinterLogo(expectedLogoUrlFor(store.logoWebHeaderUrl, "webHeader", store.fundraiserId)),
      hat_logo: toPrinterLogo(expectedLogoUrlFor(store.logoHatUrl, "hat", store.fundraiserId)),
      digital_logo: toPrinterLogo(expectedLogoUrlFor(store.logoDigitalUrl, "digital", store.fundraiserId)),
      embroidery_logo: toPrinterLogo(expectedLogoUrlFor(store.logoEmbroideryUrl, "embroidery", store.fundraiserId)),
      stripe_credit_card_descriptor: constructCardDescriptor(store.name)
    },
    variable_data: {
      points_percentage: 10,
      team_name: store.name || "",
      nces_id: "",
      org_id: store.organizationId,
      group_leader_email: groupLeaderEmail,
      state: store.state,
      program_id: "",
      sales_rep_id: salesRepId,
      account_manager_id: accountManagerId,
      fundraiser_start_date: store.startDate,
      fundraiser_end_date: store.endDate,
      zip_code: store.zip,
      city: store.city,
      fundraiser_id: store.fundraiserId,
      activity_type: store.activityType,
      organization_name: store.organizationLegalName,
      raise_id: store.fundraiserId,
      gl_fname: store.groupLeader.split(" ")[0],
      gl_lname: store.groupLeader.split(" ")[1],
      orgs_team_id: teamId,
      udu_id: store.groupLeaderUDID
    }
  };
};

const retrieveAuthToken = async () => {
  const magentoResp = await fetch(`${MAGENTO_WORKER_URL}/rest/default/V1/integration/admin/token`, {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify({ username: MAGENTO_USERNAME, password: MAGENTO_PASSWORD })
  });
  return await magentoResp.json();
};

const toMagentoCustomer = (storeCode: string, scopeId: string) => {
  return {
    customer: {
      group_id: 2,
      email: `${storeCode}@snap.store`,
      firstname: storeCode,
      lastname: "Manager Account",
      store_id: 1,
      website_id: 1,
      custom_attributes: [
        { attribute_code: "scope_id", value: scopeId },
        { attribute_code: "scope_code", value: storeCode }
      ]
    },
    password: "SnapManagers@321"
  };
};

const createStoreManager = async (authToken: string, storeCode: string, scopeId: string) => {
  const magentoCustomer = toMagentoCustomer(storeCode, scopeId);
  const magentoResp = await fetch(`${MAGENTO_WORKER_URL}/rest/default/V1/customers`, {
    method: "POST",
    headers: { Authorization: `Bearer ${authToken}`, "Content-Type": "application/json" },
    body: JSON.stringify(magentoCustomer)
  });
  return await magentoResp.json();
};

const createStoreScope = async (authToken: string, store: StoreType) => {
  const magentoStore = await toMagentoStore(store);
  const magentoResp = await fetch(`${MAGENTO_WORKER_URL}/rest/default/V1/snapraise-storescopeapi/createstorescope`, {
    method: "POST",
    headers: { Authorization: `Bearer ${authToken}`, "Content-Type": "application/json" },
    body: JSON.stringify(magentoStore)
  });
  return await magentoResp.json();
};

const createStore = async (store: StoreType) => {
  const authToken = await retrieveAuthToken();

  const { code: magentoStoreCode, scope_id: magentoStoreId, message: error } = await createStoreScope(authToken, store);
  const { email: magentoManagerEmail, message: warning } = await createStoreManager(authToken, magentoStoreCode, magentoStoreId);

  return { magentoStoreCode, magentoStoreId, magentoManagerEmail, error, warning };
};


const addTrackingNumbers = async (order: Prisma.Order) => {
  const authToken = await retrieveAuthToken();

  const trackingInfo = {
    netsuiteOrderId: order.netsuiteId,
    carrierName: (order.carrier).toUpperCase(),
    trackingNumber: order.trackingNumber
  };
  const magentoResp = await fetch(`${MAGENTO_BASE_URL}/rest/default/V1/shipment/update`, {
    method: "PUT",
    headers: {
      Authorization: `Bearer ${authToken}`,
      "Content-Type": "application/json"
    },
    body: JSON.stringify(trackingInfo)
  });
  const json = await magentoResp.json();
  if (json.success) {
    console.log("Tracking information added successfully");
  } else {
    console.error("Failed to add tracking information");
    throw new Error("Failed to add tracking information");
  }
};


export default { createStore, addTrackingNumbers, toDomain };
