/* eslint-disable functional/no-let */
import { Prisma } from "@prisma/client";
import { MarcoEventInput } from "../api/graphql/types";

const getOrderUpdateInput = (status: MarcoEventInput): Prisma.OrderUpdateInput => {
  let update: Prisma.OrderUpdateInput = {};

  if (status.shipments.length > 0) {
    const { shipping_carrier, tracking_number, search_link } = status.shipments[0];

    update = {
      ...update,
      carrier: shipping_carrier,
      trackingNumber: tracking_number,
      trackingUrl: search_link
    };
  }

  return update;
};

export default {
  getOrderUpdateInput
};
