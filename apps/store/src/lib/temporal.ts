import { Context } from "@temporalio/activity";

const getWorkflowUrl = (): string => {
  const { info } = Context.current();
  const { workflowId, runId } = info.workflowExecution;

  const temporalHost = {
    local: "http://localhost:6003",
    dev: "https://temporal.dev.snap.app",
    staging: "https://temporal.staging.snap.app",
    production: "https://temporal.snap.app"
  }[process.env.NODE_ENV as string] ?? "https://temporal.snap.app";

  return `${temporalHost}/namespaces/default/workflows/${workflowId}/${runId}/history`;
};

const getWorkflowId = (): string => {
  const { info } = Context.current();
  return info.workflowExecution.workflowId;
};

export default { getWorkflowUrl, getWorkflowId };
