/* eslint-disable functional/no-classes, functional/no-this-expressions, id-denylist, complexity, newline-per-chained-call */
const KLAVIYO_API_KEY = process.env.KLAVIYO_API_KEY;
const KLAVIYO_URL = "https://a.klaviyo.com/api";
const KLAVIYO_LIST_ID = "UyRKQS";

class KlaviyoTerminalError extends Error { constructor(q, h, r) { super(`Failed Request to (${r.status} ${q}); Headers: ${JSON.stringify(h)}; Result: ${JSON.stringify(r)}`); this.name = "KlaviyoTerminalError"; } }
class KlaviyoAPIError extends Error { constructor(errors) { super(errors.map(e => JSON.stringify(e)).join("; ")); this.name = "KlaviyoAPIError"; } }

const klaviyoFetch = async (url, method, body = undefined, acceptableStatuses = []) => {
  const result = await fetch(`${KLAVIYO_URL}${url}`, { method, headers: { ...klaviyoRequestHeaders() }, ...(body !== undefined && { body: JSON.stringify(body) }) });
  if (!result.ok && !acceptableStatuses.includes(result.status)) throw new KlaviyoTerminalError(url, klaviyoRequestHeaders(), result);
  if (result.status === 202) return { accepted: true };
  const response = await result.json();
  if (acceptableStatuses.includes(result.status)) return result;
  if (response.errors) throw new KlaviyoAPIError(response.errors);
  return response || null;
};

const toKlaviyoProfile = (store, id = null) => {
  const idParams = id ? { id } : {};
  const [groupLeaderFirstName, groupLeaderLastName] = store.groupLeader.split(" ");
  return {
    data: {
      type: "profile",
      ...idParams,
      attributes: {
        email: store.groupLeaderEmail,
        first_name: groupLeaderFirstName,
        last_name: groupLeaderLastName,
        properties: {
          MagentoStore: store.storeCode ?? `SNP${store.fundraiserEntityId}`,
          store_create_date: (new Date).toISOString().split("T")[0],
          StoreUrl: store.storeUrl,
          Logo: store.logoDigitalUrl,
          fundraiser_id: store.fundraiserId,
          fund_start_date: store.startDate,
          fund_end_date: store.endDate,
          "State/Region": store.state,
          "Zip Code": store.zip,
          ProgramName: store.name,
          "Activity Type": store.activityType,
          City: store.city,
          Organization: store.organizationName
        }
      }
    }
  };
};

const klaviyoRequestHeaders = () => {
  return {
    Authorization: `Klaviyo-API-Key ${KLAVIYO_API_KEY}`,
    Accept: "application/json",
    "Content-Type": "application/json",
    Revision: "2024-07-15"
  };
};

const findProfile = async (email) => {
  const profile = await klaviyoFetch(`/profiles/?filter=equals(email,"${email}")`, "GET");
  return profile?.data?.[0] || null;
};

const createProfile = async (store) => {
  const result = await klaviyoFetch("/profiles", "POST", toKlaviyoProfile(store), [409]);
  if (result.status === 409) return { exists: true };
  return result?.data || null;
};

const updateProfile = async (store) => {
  const result = await klaviyoFetch("/profiles", "PATCH", toKlaviyoProfile(store), [409]);
  if (result.status === 409) return { exists: true };
  return result?.data || null;
};

const createOrUpdateProfile = async (store) => {
  const result = await klaviyoFetch("/profile-import", "POST", toKlaviyoProfile(store), [409]);
  if (result.status === 409) return { exists: true };
  return result?.data || null;
};

const createSubscription = async (email: string, profileId: string, listId: string = KLAVIYO_LIST_ID) => {
  return await klaviyoFetch("/profile-subscription-bulk-create-jobs", "POST", {
    data: {
      type: "profile-subscription-bulk-create-job",
      attributes: { profiles: { data: [{ type: "profile", attributes: { subscriptions: { email: { marketing: { consent: "SUBSCRIBED" } } }, email }, id: profileId }] } },
      relationships: { list: { data: { type: "list", id: listId } } }
    }
  });
};

const findUserSubscription = async (email: string) => {
  const subscription = await klaviyoFetch(`/lists/${KLAVIYO_LIST_ID}/profiles/?filter=any(email,["${email}"])`, "GET");
  return subscription?.data?.[0] || null;
};

export default { findProfile, updateProfile, toKlaviyoProfile, createProfile, createSubscription, findUserSubscription, createOrUpdateProfile };
