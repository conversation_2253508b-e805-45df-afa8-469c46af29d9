import Prisma from "@prisma/client";
import MedusaRequest from "@store-monorepo/medusa-api";

const addTrackingNumbers = async (order: Prisma.Order) => {
  const trackingInfo = {
    netsuiteOrderId: order.netsuiteId,
    carrierName: (order.carrier).toUpperCase(),
    trackingNumber: order.trackingNumber,
    trackingUrl: order.trackingUrl,
    orderStatus: order.status
  };
  const json = await MedusaRequest.Order.addTrackingInfoToOrderFulfillment(order.orderId, trackingInfo);

  if (json.success) {
    console.log("Tracking information added successfully");
  } else {
    console.error("Failed to add tracking information");
    throw new Error("Failed to add tracking information");
  }
};

export default {
  addTrackingNumbers
};
