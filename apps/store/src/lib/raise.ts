/* eslint-disable functional/no-classes, functional/no-this-expressions, id-denylist */
import Sentry from "@store-monorepo/sentry";

import { StoreType } from "@store-monorepo/repo";

const RAISE_URL = process.env.RAISE_URL || "https://raise.snap.app";
const RAISE_API_KEY = process.env.RAISE_API_KEY || "";

type HTTP_METHOD = "GET" | "POST" | "PUT" | "DELETE";

class RaiseFetchError extends Error {
  response: Response;
  constructor(resp: Response) {
    super(`Unexpected Raise Response: ${resp.status} ${resp.statusText}`);
    this.name = "RaiseFetchError";
    this.response = resp;
  }
}

const raiseFetch = async (method: HTTP_METHOD, url: string, body = undefined) => {
  if (!RAISE_API_KEY) return;

  const resp = await fetch(`${RAISE_URL}/${url}`, {
    method,
    headers: {
      Bearer: RAISE_API_KEY,
      Accept: "application/json",
      "Content-Type": "application/json",
    },
    ...(body !== undefined && { body: JSON.stringify(body) })
  });

  if (!resp.ok) {
    const message = await resp.text();
    Sentry.captureException(new RaiseFetchError(resp));
    throw new Error(`HTTP error! status: ${resp.status}, message: ${message}`);
  }

  return await resp.json();
};

const updateStoreUrl = async (store: StoreType) => {
  return raiseFetch("PUT", `api/v3/snap_store/fundraisers/${store.fundraiserId!}/update_store_url`, {
    fundraiser: {
      store_url: store.storeUrl
    }
  });
};

export default { updateStoreUrl };
