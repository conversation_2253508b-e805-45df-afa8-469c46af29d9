import EasyPostClient, { Event, Shipment } from "@easypost/api";

const { EASYPOST_SECRET_KEY } = process.env;

const client = new EasyPostClient(EASYPOST_SECRET_KEY || "");

const getShipments = () => {
  return client.Shipment.all();
};

const getEvent = (id: Event["id"]): Promise<Event> => {
  return client.Event.retrieve(id);
};

const getShipment = (id: Shipment["id"]): Promise<Shipment> => {
  return client.Shipment.retrieve(id);
};

export default {
  getShipments,
  getEvent,
  getShipment,
};
