import EasyPostClient, { Event, Shipment } from "@easypost/api";
import { CARRIER, Prisma } from "@prisma/client";
import { EasypostEventInput } from "../api/graphql/types";

const { EASYPOST_SECRET_KEY } = process.env;

const client = new EasyPostClient(EASYPOST_SECRET_KEY || "");

const getShipments = () => {
  return client.Shipment.all();
};

const getEvent = (id: Event["id"]): Promise<Event> => {
  return client.Event.retrieve(id);
};

const getShipment = (id: Shipment["id"]): Promise<Shipment> => {
  return client.Shipment.retrieve(id);
};

const getOrderUpdateInput = (status: EasypostEventInput): Prisma.OrderUpdateInput => {
  const { trackingNumber, carrier, trackingUrl } = status;
  const update: Prisma.OrderUpdateInput = {
    carrier: carrier as CARRIER,
    trackingNumber: trackingNumber,
    trackingUrl: trackingUrl
  };
  return update;
};

export default {
  getShipments,
  getEvent,
  getShipment,
  getOrderUpdateInput
};
