/*  eslint-disable no-var, functional/immutable-data */
import { WebClient } from "@slack/web-api";
const SLACK_TOKEN = process.env.SLACK_TOKEN;
const CONVERSATION_ID = process.env.SLACK_NOTIFICATION_CHANNEL;

declare global { var slack: WebClient | undefined; }
const slack = global.slack || new WebClient(SLACK_TOKEN);

if (process.env.NODE_ENV !== "production") {
  global.slack = slack;
}

const postMessage = async (text: string) => {
  return await slack.chat.postMessage({ channel: CONVERSATION_ID, text: text, unfurl_links: false, unfurl_media: false });
};

export default { postMessage };
