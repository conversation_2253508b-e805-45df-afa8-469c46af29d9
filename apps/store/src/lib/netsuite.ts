/* eslint-disable max-lines */
import Prisma from "@prisma/client";
import Netsuite from "@store-monorepo/netsuite";

const orderUrl = (netsuiteId: string): string => {
  return `https://${process.env.NETSUITE_ACCOUNT_ID}.app.netsuite.com/app/accounting/transactions/salesord.nl?id=${netsuiteId}&whence=&e=T`;
};

const updateNetsuiteTracking = async (order: Prisma.Order) => {
  return Netsuite.SalesOrder.createItemFulfillment(order);
};

export default {
  orderUrl,
  updateNetsuiteTracking
};
