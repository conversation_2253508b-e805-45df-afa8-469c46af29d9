/* eslint-disable functional/no-classes, functional/no-this-expressions, id-denylist, complexity, no-param-reassign */
const GRAPHQL_URL = `${process.env.GRAPHQL_URL}/graphql`;
const COMMS_API_KEY = process.env.COMMS_API_KEY;
const COMMS_TEMPLATES = {
  store_build_ready: "store_build_ready",
  store_otf_recap_post_fundraiser: "store_otf_recap_post_fundraiser",
  store_order_delivered: "store_order_delivered",
  store_order_shipped: "store_order_shipped",
  raise_order_delivered: "raise_order_delivered",
  raise_order_shipped: "raise_order_shipped"
} as const;

type Contact = { to: string; attributes?: Record<string, string> };
type EmailVariable = Record<string, string | number>;

class CommsHTTPError extends Error { constructor(q, r) { super(`Failed Request ${r.status} (${q}); Result: ${JSON.stringify(r)}`); this.name = "CommsHTTPError"; } }
class CommsAPIError extends Error { constructor(errors) { super(errors.map(e => JSON.stringify(e)).join("; ")); this.name = "CommsAPIError"; } }

const COMMS_SEND_QUERY = `
  mutation CommsSend($contacts: [MessageContact]!, $templateName: String!, $templateAttributes: JSON!) {
    commsSend(contacts: $contacts, templateName: $templateName, templateAttributes: $templateAttributes) {
      details { contact date id status }
      id
    }
  }
`;

const sendEmail = async (templateName: keyof typeof COMMS_TEMPLATES, contacts: Contact[], templateAttributes: EmailVariable) => {
  const response = await fetch(GRAPHQL_URL, {
    method: "POST",
    headers: { "Content-Type": "application/json", "x-api-key": COMMS_API_KEY },
    body: JSON.stringify({ query: COMMS_SEND_QUERY, variables: { contacts, templateName, templateAttributes } }),
  });
  if (!response.ok) throw new CommsHTTPError(GRAPHQL_URL, response);
  const { data, errors } = await response.json();
  if (errors) throw new CommsAPIError(errors);
  return data.commsSend;
};

export default { sendEmail };
