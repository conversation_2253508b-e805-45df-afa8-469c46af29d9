const isColor = (color: string | null): boolean => {
  return color && /^#([0-9a-f]{2})([0-9a-f]{2})([0-9a-f]{2})$/i.test(color);
};

const isComplexColor = (colors: string | null): boolean => {
  return colors && colors?.split("|").length === 2 && colors?.split("|").every(k => isColor(k));
};

const isEmail = (email?: string | null): boolean => {
  return email && /^[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,}$/i.test(email);
};

const isExistingUrl = async (url: string): Promise<boolean> => {
  try {
    const resp = await fetch(url);
    return resp.ok;
  } catch {
    return false;
  }
};

const isUrl = (url: string): boolean => {
  try {
    const parsed = new URL(url);
    return parsed !== undefined;
  } catch {
    return false;
  }
};


export default { isColor, isComplexColor, isEmail, isExistingUrl, isUrl };
