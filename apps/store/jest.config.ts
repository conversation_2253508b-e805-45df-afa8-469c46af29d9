/* eslint-disable */
process.env.MARCO_WEBHOOK_KEY = "ThisIsTheSecretAuthKey";
process.env.NETSUITE_CUSTOMER_ID = "12345";
process.env.NETSUITE_ACCOUNT_ID = "test_app_id";
process.env.NETSUITE_APPLICATION_ID = "12345";
process.env.NETSUITE_CLIENT_KEY = "test_client_key";
process.env.NETSUITE_CLIENT_SECRET = "test_client_secret";
process.env.NETSUITE_TOKEN_KEY = "test_token_key";
process.env.NETSUITE_TOKEN_SECRET = "test_token_secret";
process.env.STORE_NETSUITE_API_KEY = "test_api_key"
process.env.ZENDESK_SUBDOMAIN = "example";
process.env.MAGENTO_BASE_URL = 'https://magento_base_url';
process.env.MAGENTO_WORKER_URL = 'https://magento_worker_url';
process.env.MAGENTO_USERNAME = 'me'
process.env.MAGENTO_PASSWORD = 'password'
export default {
  displayName: 'store',
  preset: '../../jest.preset.js',
  testEnvironment: 'node',
  transform: {
    '^.+\\.[tj]s$': ['ts-jest', { tsconfig: '<rootDir>/tsconfig.spec.json' }],
  },
  moduleFileExtensions: ['ts', 'js', 'html'],
  coverageDirectory: '../../coverage/apps/store',
};
