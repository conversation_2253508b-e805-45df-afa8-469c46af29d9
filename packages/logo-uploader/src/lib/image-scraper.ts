import Puppeteer, { <PERSON> } from "puppeteer";
import sharp from "sharp";
const inPuppeteerBrowser = async <out>(url: string, callback: (page: Page) => Promise<out>): Promise<out> => {
  const browser = await Puppeteer.launch({
    args: ["--no-sandbox"],
    headless: true,
    ignoreHTTPSErrors: false,
    waitForInitialPage: true
  });
  const page = await browser.newPage();
  await page.goto(url);
  const result = await callback(page);
  await browser.close();
  return result;
};

export const downloadSVGAsPNG = async (url: string, dpi = 300): Promise<Buffer> => {
  const svgBuff = await inPuppeteerBrowser<Buffer>(url, async (page: Page) => {
    /* istanbul ignore next */
    const [width, height] = await page.evaluate(() => {
      const svg = document.querySelector("svg");
      const box = svg!.getAttribute("viewBox")!.split(" ");
      return box.slice(2).map((v) => parseInt(v));
    });
    await page.setViewport({ width, height });
    return await page.screenshot({ type: "png", omitBackground: true });
  });

  // Prettier-ignore
  const file = sharp(svgBuff).withMetadata({
    density: dpi,
    icc: "src/color-profiles/AdobeRGB1998.icc"
  });

  return (url.includes("_d.svg") ? file.trim() : file).toBuffer();
};

export const openSVGAndDownloadPNG = async (svgString: string, dpi = 300) => {
  const browser = await Puppeteer.launch({
    args: ["--no-sandbox"],
    headless: true,
    ignoreHTTPSErrors: false,
    waitForInitialPage: true
  });
  const page = await browser.newPage();
  await page.setContent(svgString);
  const [width, height] = await page.evaluate(() => {
    const svg = document.querySelector("svg");

    const w = parseInt(svg!.getAttribute("width") || "");
    const h = parseInt(svg!.getAttribute("height") || "");
    if (!isNaN(w) && !isNaN(h)) {
      return [w, h];
    }

    const box = svg!.getAttribute("viewBox")!.split(" ");
    return box.slice(2).map((v) => parseInt(v));
  });
  await page.setViewport({ width, height });
  const png = await page.screenshot({ type: "png", omitBackground: true });
  await browser.close();
  return sharp(png).withMetadata({
    density: dpi,
    icc: "src/color-profiles/AdobeRGB1998.icc"
  }).
    toBuffer();
};

const createBackprintPNG = async (svgString: string, dpi=300) => {
  const png = await openSVGAndDownloadPNG(svgString);
  return sharp(png).withMetadata({
    density: dpi,
    icc: "src/color-profiles/AdobeRGB1998.icc"
  }).
    trim().
    toBuffer();
};

const ImageScraper = {
  downloadSVGAsPNG,
  openSVGAndDownloadPNG,
  createBackprintPNG
};

export default ImageScraper;
