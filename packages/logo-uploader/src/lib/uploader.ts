import { S3Event } from "aws-lambda";

import StorageClient from "./storage-adapter";
import ImageScraper, { openSVGAndDownloadPNG } from "./image-scraper";

const BASE_URL = process.env.S3_BASE_URL;

const folderName = (folder: string): string => (folder.includes("TEST") ? "MarcoLogos-TEST" : "MarcoLogos");

const fromS3Event = async (event: S3Event): Promise<void> => {
  const key = decodeURIComponent(event.Records[0].s3.object.key.replaceAll("+", " "));
  return fromS3Url(BASE_URL + key);
};

const s3Url = (url: string): string => {
  const [folder, filename] = url.split("/").slice(-2);
  const newFolder = folderName(folder);
  const newName = filename.replace(/\.svg$/, ".png");
  return `${BASE_URL}${newFolder}/${newName}`;
};

const fromS3Url = async (url: string): Promise<void> => {
  try {
    const png = await ImageScraper.downloadSVGAsPNG(url);
    const [folder, filename] = url.split("/").slice(-2);
    const newFolder = folderName(folder);
    const newKey = `${newFolder}/${filename.replace("svg", "png")}`;
    await StorageClient.putObject(newKey, png);
  } catch (e) {
    console.log("Failed to upload logo");
  }
};

const Uploader = {
  fromS3Event,
  fromS3Url,
  folderName,
  s3Url,
  openSVGAndDownloadPNG: ImageScraper.openSVGAndDownloadPNG,
  createBackprintPNG: ImageScraper.createBackprintPNG
};

export default Uploader;
