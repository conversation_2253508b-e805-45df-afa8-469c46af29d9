import S3, { S3Client, PutObjectCommandInput, PutObjectCommand } from "@aws-sdk/client-s3";

const S3_BUCKET = process.env.S3_BUCKET;

const getObject = async (key: string): Promise<S3.GetObjectCommandOutput> => {
  const s3Client = new S3Client({ apiVersion: "2006-03-01", region: "us-west-1" });
  const params = {
    Bucket: S3_BUCKET,
    Key: key
  };
  const command = new S3.GetObjectCommand(params);
  return s3Client.send(command);
};

const putObject = async (key: string, data: Buffer): Promise<S3.PutObjectCommandOutput> => {
  const s3Client = new S3Client({ apiVersion: "2006-03-01", region: "us-west-1" });
  const params: PutObjectCommandInput = {
    ACL: "public-read",
    Body: data,
    Bucket: S3_BUCKET,
    ContentType: "image/png",
    Key: key
  };
  const command = new PutObjectCommand(params);
  return s3Client.send(command);
};

export default () => ({
  putObject,
  getObject
});
