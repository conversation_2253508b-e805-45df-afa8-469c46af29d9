import { S3Event } from "aws-lambda";
import ImageScraper from "../lib/image-scraper";
import StorageClient from "../lib/storage-adapter";
import Upload from "../lib/uploader";

jest.mock("../lib/image-scraper");
jest.mock("../lib/storage-adapter");

describe("folderName", () => {
  it("returns correct folder name", () => {
    expect(Upload.folderName("VAnKBGbWGl")).toEqual("MarcoLogos");
    expect(Upload.folderName("VAnKBGbWGl-TEST")).toEqual("MarcoLogos-TEST");
  });
});

describe("fromS3Url", () => {
  it("downloads an svg image from BASE_URL + key provided, then uploads to S3", async () => {
    const str = "6DqUtRHH2w";
    const buffer = Buffer.from(str);

    (ImageScraper.downloadSVGAsPNG as jest.Mock).mockReturnValueOnce(buffer);
    await Upload.fromS3Url(`http://snapraiselogos.s3.us-west-1.amazonaws.com.test/MyFolder/${str}`);
    expect(ImageScraper.downloadSVGAsPNG).toHaveBeenCalledWith(`http://snapraiselogos.s3.us-west-1.amazonaws.com.test/MyFolder/${str}`);
    expect(StorageClient.putObject).toHaveBeenCalledWith(`MarcoLogos/${str}`, buffer);
  });
});

describe("fromS3Event", () => {
  it("given an AWS S3 event, it calls fromS3Event with the first record", async () => {
    const str = "hHv9Y84Q99";
    const testFile = Buffer.from(str);
    const testEvent = { Records: [{ s3: { object: { key: str } } }] } as unknown as S3Event;
    (ImageScraper.downloadSVGAsPNG as jest.Mock).mockReturnValueOnce(testFile);
    await Upload.fromS3Event(testEvent);
    expect(StorageClient.putObject).toHaveBeenCalledWith(`${Upload.folderName("UuwUwpbCgK")}/${str}`, testFile);
  });
});

describe("s3Url", () => {
  it("should format the S3 url", () => {
    expect(Upload.s3Url("https://store.snap.app/api/custom-logo/123456_d.svg")).toEqual("http://snapraiselogos.s3.us-west-1.amazonaws.com.test/MarcoLogos/123456_d.png");
  });
});
