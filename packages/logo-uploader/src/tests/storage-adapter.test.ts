import { GetObjectCommandInput, PutObjectCommandInput } from "@aws-sdk/client-s3";
import StorageClient from "../lib/storage-adapter";

const send = jest.fn();
jest.mock("@aws-sdk/client-s3", () => ({
  ...jest.requireActual("@aws-sdk/client-s3"),
  S3Client: jest.fn().mockImplementation(() => ({ send }))
}));

describe("S3Client", () => {
  beforeEach(() => {
    send.mockClear();
  });

  it("gets an S3 object with the provided key", async () => {
    const key = "X";
    const input: GetObjectCommandInput = {
      Bucket: "snapraiselogos",
      Key: key
    };

    const badInput = { ...input, Bucket: "uVxVeasHCB" };

    await StorageClient.getObject(key);

    const inputArgs = send.mock.calls[0][0].input;
    expect(inputArgs).toEqual(input);
    expect(inputArgs).not.toEqual(badInput);
  });

  it("uploads an S3 object with the provided key", async () => {
    const key = "C";
    const file = Buffer.from("z");
    const input: PutObjectCommandInput = {
      ACL: "public-read",
      Body: file,
      Bucket: "snapraiselogos",
      ContentType: "image/png",
      Key: key
    };

    const badInput = { ...input, Bucket: "RYeiqisXmI" };

    await StorageClient.putObject(key, file);

    const inputArgs = send.mock.calls[0][0].input;
    expect(inputArgs as PutObjectCommandInput).toEqual(input);
    expect(inputArgs).not.toEqual(badInput);
  });
});
