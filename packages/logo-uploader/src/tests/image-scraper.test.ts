import fs from "fs";
import http from "http";
import sharp from "sharp";

import ImageScraper from "../lib/image-scraper";

const server = (() => {
  return http.createServer(async (req, resp) => {
    const file = fs.readFileSync("packages/logo-uploader/src/tests/assets/tiny.svg");
    resp.writeHead(200, { "Content-Type": "application/xhtml+xml" });
    resp.end(file.toString());
  }).listen(1337, "localhost");
})();

const COLOR_PROFILE_FILE = "src/color-profiles/AdobeRGB1998.icc";

describe("ImageScraper", () => {
  afterAll(async () => {
    server.close();
  });
  jest.setTimeout(15000);
  describe("downloadSVGAsPNG", () => {
    it("downloads an svg image from a provided url and returns a png file buffer with enhanced DPI and AdobeRGB1998 color profile", async () => {
      const png = await ImageScraper.downloadSVGAsPNG("http://localhost:1337");
      const unenhancedPng = fs.readFileSync("packages/logo-uploader/src/tests/assets/72dpi.png");

      const enhancedPng = await sharp(unenhancedPng).withMetadata({
        density: 300,
        icc: COLOR_PROFILE_FILE
      }).
        toBuffer();

      expect(enhancedPng).not.toEqual(unenhancedPng);
      expect(enhancedPng).toEqual(png);
      expect(enhancedPng.length).toBeGreaterThan(unenhancedPng.length);
    });

    it("allows overriding DPI", async () => {
      const png = await ImageScraper.downloadSVGAsPNG("http://localhost:1337", 100);
      const unenhancedPng = fs.readFileSync("packages/logo-uploader/src/tests/assets/72dpi.png");

      const hundredDPIPng = await sharp(unenhancedPng).withMetadata({
        density: 100,
        icc: COLOR_PROFILE_FILE
      }).
        toBuffer();

      const enhancedDPIPng = await sharp(unenhancedPng).withMetadata({
        density: 300,
        icc: COLOR_PROFILE_FILE
      }).
        toBuffer();

      expect(enhancedDPIPng).not.toEqual(png);
      expect(hundredDPIPng).toEqual(png);
    });
  });
});
