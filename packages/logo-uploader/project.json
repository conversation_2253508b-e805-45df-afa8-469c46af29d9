{"name": "logo-uploader", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/logo-uploader/src", "projectType": "library", "tags": [], "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/packages/logo-uploader", "main": "packages/logo-uploader/src/index.ts", "tsConfig": "packages/logo-uploader/tsconfig.lib.json", "assets": ["packages/logo-uploader/*.md"]}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"]}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "packages/logo-uploader/jest.config.ts"}}}}