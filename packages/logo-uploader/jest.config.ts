/* eslint-disable */
process.env.S3_BUCKET = 'snapraiselogos';
process.env.S3_BASE_URL = 'http://snapraiselogos.s3.us-west-1.amazonaws.com.test/';

export default {
  displayName: 'logo-uploader',
  preset: '../../jest.preset.js',
  testEnvironment: 'node',
  transform: {
    '^.+\\.[tj]s$': ['ts-jest', { tsconfig: '<rootDir>/tsconfig.spec.json' }],
  },
  moduleFileExtensions: ['ts', 'js', 'html'],
  coverageDirectory: '../../coverage/packages/logo-uploader',
};
