/* eslint-disable complexity, max-lines, functional/immutable-data */
import { STATUS, STATE } from "@prisma/client";
import { ShippingProvider, Vendor, PrintAttributesInput, Color, Priority } from "@store-monorepo/graphql/types";

type HasNameOrEmail = ({custcol_recipient_name: string | undefined, custcol_recipient_email: string }
  | {custcol_recipient_name: string, custcol_recipient_email: string | undefined })

type NetsuiteItem = {
  item: string
  custcol_recipient_name: string | undefined
  custcol_recipient_email: string,
  custcol_sku: string,
  custcolname_text?: string | null
  custcolname_font_color?: string | null
  custcolname_style?: string | null
  custcolnumber_text?: string | null
  custcolnumber_color?: string | null
} & HasNameOrEmail

type Event = {
  eventType: string
  record: {
    id: string,
    sublists: {
      item: Record<string, NetsuiteItem>
    },
    fields: {
      custbody_fundraiser_id: string,
      custbody_shipment_workflow_stage: string,
      custbodyorder_confirmation?: string,
      custbody_marco_priority_level?: string,
      shipaddress: string,
      entityname: string,
      externalId?: string
    }
  }
}

type LineItem = {
  receiverName: string
  netsuiteId: string
  printAttributes?: object
}

const STORE_SALES_TAX = "1645";
const STORE_SHIPPING = "1644";
const STORE_DISCOUNT = "1664";
const STORE_POINTS = "14103";

const getPrintAttributes = (item: NetsuiteItem): PrintAttributesInput | undefined => {
  const {
    custcolname_text,
    custcolname_font_color,
    custcolname_style,
    custcolnumber_text,
    custcolnumber_color
  } = item;

  if (custcolname_text && custcolnumber_text) {
    return {
      name: {
        value: custcolname_text,
        font: custcolname_style,
        color: custcolname_font_color! as Color
      },
      number: {
        value: custcolnumber_text,
        color: custcolnumber_color! as Color
      }
    };
  }

  if (custcolname_text && !custcolnumber_text) {
    return {
      name: {
        value: custcolname_text,
        font: custcolname_style,
        color: custcolname_font_color!.toUpperCase() as Color
      }
    };
  }

  if (!custcolname_text && custcolnumber_text) {
    return {
      number: {
        value: custcolnumber_text,
        color: custcolnumber_color!.toUpperCase() as Color
      }
    };
  }

  return undefined;
};

const lineItems = (event: Event): LineItem[] => {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const { currentline, ...items } = event.record.sublists.item;
  return Object.values(items).filter((li => {
    return ![STORE_SALES_TAX,STORE_SHIPPING, STORE_DISCOUNT, STORE_POINTS].includes(li.custcol_sku);
  })).
    reduce((products: LineItem[], item: NetsuiteItem): LineItem[] => {
      if (item.item) {
        const printAttributes = getPrintAttributes(item);
        const lineItem = { receiverName: item.custcol_recipient_name || item.custcol_recipient_email, netsuiteId: item.item, printAttributes };

        return [...products, lineItem] as LineItem[];
      }
      return products;
    }, [] as LineItem[]);
};

const orderStatus = (event: Event) => {
  const statuses: Record<string,STATUS> = {
    1: STATUS.HOLD,
    2: STATUS.DESIGN,
    3: STATUS.HOLD,
    4: STATUS.HOLD,
    5: STATUS.HOLD,
    6: STATUS.HOLD,
    7: STATUS.HOLD,
    8: STATUS.HOLD,
    9: STATUS.HOLD,
    10: STATUS.SHIPPED,
    11: STATUS.HOLD
  };
  return statuses[event.record.fields.custbody_shipment_workflow_stage];
};

const parseAddress = (event: Event) => {
  const nsAddress = event.record.fields.shipaddress;
  const lines = nsAddress.split(/\r?\n/);
  const zipIndex = lines.findIndex(a => a.match(/[A-Z]{2}\s\d{5}(-\d{4})?/));
  const line2Index = lines.slice(0, zipIndex).findIndex(a => /^\s*(Apt[^a-z]|Apartment|Unit|Bldg|Floor|#\d|Suite|Ste[^a-z]|U\.)/i.test(a));
  const line1Index = (line2Index > 0 ? line2Index - 1 : zipIndex -1);
  const [shipTo, line2] = lines.slice(0, line1Index);
  const [city, state, zipCode] = lines[zipIndex].split(/\s([A-Z]{2})\s/);
  return { shipTo, line2, street: lines[line1Index], street2: lines[line2Index], city, state: state as STATE, zipCode };
};

// Will be used for creating reorders
const newOrder = (event: Event, logoUrl: string) => {
  const address = parseAddress(event);
  const externalId = event.record.fields.externalId;
  return {
    ...address,
    carrier: ShippingProvider.FedEx,
    netsuiteId: event.record.id,
    packingSlipId: event.record.id,
    packingSlipTitle: event.record.fields.entityname,
    fundraiserId: event.record.fields.custbody_fundraiser_id,
    products: lineItems(event).map((product: LineItem) => ({ ...product, logo: logoUrl })),
    priority: event.record.fields.custbody_marco_priority_level === "1" ? Priority.Rush : Priority.Normal,
    vendor: event.record.fields.custbodyorder_confirmation ? Vendor.Store : Vendor.Marco,
    ...{ confirmationId: `${event.record.fields.custbodyorder_confirmation}-REORDER-${event.record.id}` || undefined, orderId: externalId || undefined },
    orderId: event.record.fields.externalId || undefined
  };
};

export default {
  orderStatus,
  lineItems,
  parseAddress,
  newOrder,
  getPrintAttributes,
  STORE_SALES_TAX,
  STORE_SHIPPING
};
