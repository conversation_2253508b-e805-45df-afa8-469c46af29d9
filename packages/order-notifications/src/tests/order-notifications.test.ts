/* eslint-disable max-lines, max-lines-per-function, sonarjs/no-duplicate-string */
import { STATUS } from "@prisma/client";
import OrderNotifications from "../lib/order-notifications";

const mockEvent = (nsOrderStatus?: number) => ({
  eventType: "create",
  record: {
    id: "123",
    sublists: {
      item: {
        item_1: {
          custcol_recipient_name: "<PERSON>",
          custcol_recipient_email: "<EMAIL>",
          item: "1234",
          custcol_sku: "123"
        },
        item_2: {
          custcol_recipient_name: undefined,
          custcol_recipient_email: "<EMAIL>",
          item: "3456",
          custcol_sku: "234",
        },
        item_3: {
          custcol_recipient_name: undefined,
          custcol_recipient_email: "<EMAIL>",
          item: "3456",
          custcol_sku: OrderNotifications.STORE_SHIPPING,
        },
        item_4: {
          custcol_recipient_name: undefined,
          custcol_recipient_email: "<EMAIL>",
          item: "3456",
          custcol_sku: OrderNotifications.STORE_SALES_TAX,
        }
      }
    },
    fields: {
      custbody_shipment_workflow_stage: nsOrderStatus ? `${nsOrderStatus}`: "1",
      entityname: "Fun Draiser",
      custbody_fundraiser_id: "12345",
      shipaddress: "Some Dude\nLegit Board of Dudes\n123 Duder Way\nUnit D (as in Dude)\nDudeford CA 13375\nUnited States"
    }
  }
});
describe("OrderNotifications", () => {
  describe("lineItems", () => {
    it("translates Netsuite line items to store line items, ignoring shipping and sales tax", () => {
      const lineItems = OrderNotifications.lineItems(mockEvent(1));
      expect(lineItems).toEqual([{ receiverName: "Alex", netsuiteId: "1234" }, { receiverName: "<EMAIL>", netsuiteId: "3456" }]);
    });
  });

  describe("orderStatus", () => {
    it("translates a netsuite order status to DESIGN", () => {
      const status = OrderNotifications.orderStatus(mockEvent(2));
      expect(status).toBe(STATUS.DESIGN);
    });

    it("translates a netsuite order status to HOLD", () => {
      const statuses = [1,3,4,5,6,7,8,9,11].map(mockEvent).map(OrderNotifications.orderStatus);
      expect(statuses).toEqual(new Array(9).fill(STATUS.HOLD));
    });

    it("translates a netsuite order status to SHIPPED", () => {
      const status = OrderNotifications.orderStatus(mockEvent(10));
      expect(status).toBe(STATUS.SHIPPED);
    });
  });

  describe("parseAddress", () => {
    it("parses an address with second subject line and second street address line", () => {
      const event = mockEvent();
      const address = OrderNotifications.parseAddress(event);
      expect(address).toEqual({
        shipTo: "Some Dude",
        line2: "Legit Board of Dudes",
        street: "123 Duder Way",
        street2: "Unit D (as in Dude)",
        city: "Dudeford",
        state: "CA",
        zipCode: "13375"
      });
    });

    it("parses an address with second subject line and 1 street address line", () => {
      const event = mockEvent();
      event.record.fields.shipaddress = "Some Dude\nLegit Board of Dudes\n123 Duder Way\nDudeford CA 13375\nUnited States";
      const address = OrderNotifications.parseAddress(event);
      expect(address).toEqual({
        shipTo: "Some Dude",
        line2: "Legit Board of Dudes",
        street: "123 Duder Way",
        city: "Dudeford",
        state: "CA",
        zipCode: "13375"
      });
    });

    it("parses an address without second subject line and without second street address line", () => {
      const event = mockEvent();
      event.record.fields.shipaddress = "Some Dude\n123 Duder Way\nDudeford CA 13375\nUnited States";
      const address = OrderNotifications.parseAddress(event);
      expect(address).toEqual({
        shipTo: "Some Dude",
        street: "123 Duder Way",
        city: "Dudeford",
        state: "CA",
        zipCode: "13375"
      });
    });

    it("parses an address without second subject line and with second street address line", () => {
      const event = mockEvent();
      event.record.fields.shipaddress = "Some Dude\n123 Duder Way\nUnit D (as in Dude)\nDudeford CA 13375\nUnited States";
      const address = OrderNotifications.parseAddress(event);
      expect(address).toEqual({
        shipTo: "Some Dude",
        street: "123 Duder Way",
        street2: "Unit D (as in Dude)",
        city: "Dudeford",
        state: "CA",
        zipCode: "13375"
      });
    });
  });

  it("parses an address with carriage returns before the newline", () => {
    const event = mockEvent();
    event.record.fields.shipaddress = "Some Dude\r\n123 Duder Way\r\nDudeford CA 13375\r\nUnited States";
    const address = OrderNotifications.parseAddress(event);
    expect(address).toEqual({
      shipTo: "Some Dude",
      street: "123 Duder Way",
      city: "Dudeford",
      state: "CA",
      zipCode: "13375"
    });
  });

  describe("getPrintAttributes", () => {
    it("parse both name and number", () => {
      const event = mockEvent(1);
      const printAttributes = OrderNotifications.getPrintAttributes({
        ...event.record.sublists.item.item_1,
        custcolname_font_color: "BLACK",
        custcolname_style: "Varsity Team",
        custcolname_text: "GALES",
        custcolnumber_color: "BLACK",
        custcolnumber_text: "99",
      });
      expect(printAttributes).toEqual({
        name: { value: "GALES", font: "Varsity Team", color: "BLACK" },
        number: { value: "99", color: "BLACK" }
      });
    });

    it("parse name only", () => {
      const event = mockEvent(1);
      const printAttributes = OrderNotifications.getPrintAttributes({
        ...event.record.sublists.item.item_1,
        custcolname_font_color: "BLACK",
        custcolname_style: "Varsity Team",
        custcolname_text: "GALES",
        custcolnumber_color: null,
        custcolnumber_text: null,
      });
      expect(printAttributes).toEqual({
        name: { value: "GALES", font: "Varsity Team", color: "BLACK" }
      });
    });

    it("parse number only", () => {
      const event = mockEvent(1);
      const printAttributes = OrderNotifications.getPrintAttributes({
        ...event.record.sublists.item.item_1,
        custcolname_font_color: "",
        custcolname_style: "",
        custcolname_text: "",
        custcolnumber_color: "BLACK",
        custcolnumber_text: "99",
      });
      expect(printAttributes).toEqual({
        number: { value: "99", color: "BLACK" }
      });
    });

    it("parse empty attributes", () => {
      const event = mockEvent(1);
      const printAttributes = OrderNotifications.getPrintAttributes(event.record.sublists.item.item_1);
      expect(printAttributes).toEqual(undefined);
    });
  });
});
