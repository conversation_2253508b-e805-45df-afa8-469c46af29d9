{"name": "order-notifications", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/order-notifications/src", "projectType": "library", "tags": [], "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/packages/order-notifications", "main": "packages/order-notifications/src/index.ts", "tsConfig": "packages/order-notifications/tsconfig.lib.json", "assets": ["packages/order-notifications/*.md"]}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "packages/order-notifications/jest.config.ts"}}}}