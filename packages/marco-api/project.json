{"name": "marco-api", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/marco-api/src", "projectType": "library", "tags": [], "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/packages/marco-api", "main": "packages/marco-api/src/index.ts", "tsConfig": "packages/marco-api/tsconfig.lib.json", "assets": ["packages/marco-api/*.md"]}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"]}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "packages/marco-api/jest.config.ts"}}}}