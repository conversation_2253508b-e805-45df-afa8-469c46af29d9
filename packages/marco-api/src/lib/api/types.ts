type ShippingProvider = "UPS" | "USPS" | "FedEx" | "OSM" | "DHL";

type Vendor = "MARCO" | "STORE" | "RAISE" | "RAISE_MARCO";

type OrderItem = {
  customer_sku?: string;
  item_reference_number?: string;
  sku?: string;
  name?: string;
  description?: string;
  quantity: number;
  attributes?: {
    style: string;
    color: string;
    size?: string;
  };
  designs: Array<{
    placement: string;
    art_file: string;
    art_url: string;
    thumbnail_url?: string;
    underbase?: boolean;
    x_offset?: number;
    y_offset?: number;
    width?: number;
    height?: number;
  }>;
  custom_tags?: Array<{
    tag_code: string;
    tag_type: "hang tag" | "sticker";
    image: string;
  }>;
};

type MarcoOrder = {
  id?: number;
  type: "order";
  account_id: number;
  account_zip: string;
  purchase_order: string;
  ship_provider: ShippingProvider;
  ship_method?: string;
  tracking_number?: string;
  shipping_label_url?: string;
  packing_slip_url?: string;
  addtl_ship_docs_url?: string;
  ship_to: {
    first_name: string;
    last_name: string;
    company_name?: string;
    address: string;
    address_2?: string;
    city: string;
    state: string;
    zip_code: string;
    country: string;
    email?: string;
    telephone?: string;
  };
  ship_from?: {
    first_name?: string;
    last_name?: string;
    company_name?: string;
    address?: string;
    address_2?: string;
    city?: string;
    state?: string;
    zip_code?: string;
    country?: string;
  };
  order_notes?: string;
  order_reference_number?: string;
  production_priority: string;
  items: OrderItem[];
  inserts?: Array<{
    identifier: string;
    preview_url: string;
  }>;
};

type MarcoUpdateOrder = {
  ship_provider?: ShippingProvider;
  ship_method?: string;
  ship_to?: {
    first_name?: string;
    last_name?: string;
    company_name?: string;
    address?: string;
    address_2?: string;
    city?: string;
    state?: string;
    zip_code?: string;
    country?: string;
    email?: string;
    telephone?: string;
  };
};

type StockCheck = {
  id: number;
  sku_identifier: string;
  sku_gtin: string;
  location_id: number;
  location_name: string;
  quantity: number;
  amt_on_order: number;
  expected_etas: string[];
};

type Order = {
  id?: string;
  vendor: Vendor;
  carrier: ShippingProvider;
  shipTo: string;
  line2?: string;
  street: string;
  street2?: string;
  city: string;
  state: string;
  zipCode: string;
  packingSlipId: string;
  packingSlipTitle?: string;
  priority: string;
  netsuiteId: string;
  products: Product[];
};

type Product = {
  name: string;
  size: string;
  sku: string;
  logo: string;
  logoPositionX?: string;
  logoPositionY?: string;
  logoPositionZ?: string;
  receiverName?: string;
};

type OrderUpdate = {
  id: number;
  carrier: ShippingProvider;
  shipTo: string;
  street: string;
  city: string;
  state: string;
  zipCode: string;
  vendor: string;
};

export type { MarcoOrder, MarcoUpdateOrder, StockCheck, Order, OrderUpdate, Product, OrderItem, ShippingProvider };
