/* eslint-disable max-lines, capitalized-comments, no-warning-comments, etc/no-commented-out-code,  */
import { <PERSON><PERSON><PERSON><PERSON>, MarcoUpdateOrder, OrderUpdate, ShippingProvider } from "./types";

const ACCOUNT_ZIP = process.env.MARCO_ACCOUNT_ZIP || "";

const DEFAULT_SHIP_FROM = {
  company_name: "Snap! Mobile Inc",
  address: "201 W Howard L",
  city: "Austin",
  state: "TX",
  zip_code: "78753",
  country: "US"
};

interface Order {
  shipTo: string;
  shipToPhone?: string;
  shipToEmail?: string;
  line2?: string;
  street: string;
  street2?: string;
  zipCode: string;
  state: string;
  netsuiteId: string;
  city: string;
  products: Product[];
  priority: string,
  vendor: string
}

interface Product {
  receiverName: string;
  logo: string;
  backLogo?: string
  product: {
    sku: string;
    name: string;
    logoPosition: string;
    size: string;
  };
}

const getAccountId = (vendor: string) => {
  if (vendor === "MARCO") return process.env.MARCO_ACCOUNT_ID;
  if (vendor === "STORE") return process.env.MARCO_STORE_ACCOUNT_ID;
  return "";
};

const capitalize = (s: string) => {
  return s[0].toUpperCase() + s.slice(1).toLowerCase();
};

const logoPosition = (position: string) => {
  return position.split("_").map(str => capitalize(str.toLowerCase())).
    join(" ");
};

const mapOrderItems = (products: Product[]) => {
  return products.map((product) => {
    const backPrint = product.backLogo ? [
      {
        art_url: product.backLogo,
        thumbnail_url: product.backLogo,
        placement: "Back Center",
        art_file: ""

      }
    ] : [];
    return {
      name: product.product.name,
      quantity: 1,
      sku: product.product.sku,
      attributes: {
        style: "",
        color: ""
      },
      designs: [
        {
          art_url: product.logo,
          thumbnail_url: product.logo,
          placement: logoPosition(product.product.logoPosition),
          art_file: ""
        },
        ...backPrint
      ]
    };
  });
};

const isPostOfficeAddress = (address?: string): boolean => {
  return [
    "PO Box",
    "P.O. Box"
  ].some(po => address?.includes(po));
};

const shipmentCarrier = (order: Order): { ship_provider: ShippingProvider, ship_method?: string } => {
  if ([order.shipTo, order.line2].some(isPostOfficeAddress)) {
    return {
      ship_provider: "FedEx",
      ship_method: "SMART_POST"
    };
  }

  if (order.products.length < 10) {
    return {
      ship_provider: "USPS",
      ship_method: ""
    };
  }

  return {
    ship_provider: "FedEx",
    ship_method: "FEDEX_GROUND"
  };
};

const packingSlipUrl = (order: Order): string => {
  const host = {
    local: "http://localhost:3000",
    dev: "https://store.dev.snap.app",
    staging: "https://store.staging.snap.app",
  }[process.env.NODE_ENV as string] ?? "https://store.snap.app";

  return `${host}/api/packing-slip/${order.netsuiteId}.pdf`;
};

const toNewOrder = (order: Order): MarcoOrder => {
  const [firstName, lastName] = order.shipTo.split(" ");
  const { ship_provider, ship_method } = shipmentCarrier(order);
  const accountId = getAccountId(order.vendor);
  return {
    type: "order",
    account_id: Number(accountId),
    account_zip: ACCOUNT_ZIP,
    purchase_order: order.netsuiteId,
    ship_provider: ship_provider,
    ship_method: ship_method,
    tracking_number: "",
    shipping_label_url: "",
    packing_slip_url: packingSlipUrl(order),
    addtl_ship_docs_url: "",
    production_priority: order.priority.toLowerCase(),
    ship_to: {
      first_name: firstName,
      last_name: lastName,
      company_name: order.line2,
      address: order.street,
      address_2: order.street2,
      city: order.city,
      state: order.state,
      zip_code: order.zipCode,
      country: "USA",
      email: order.shipToEmail,
      telephone: order.shipToPhone
    },
    ship_from: DEFAULT_SHIP_FROM,
    order_notes: "",
    order_reference_number: "",
    items: mapOrderItems(order.products)
  };
};

const toUpdatedOrder = (order: OrderUpdate): MarcoUpdateOrder => {
  return {
    // TODO: Should we change provider on order update?
    // ship_provider: "USPS",
    ship_to: {
      first_name: order.shipTo,
      last_name: "",
      company_name: "",
      address: order.street,
      city: order.city,
      state: order.state,
      zip_code: order.zipCode
    }
  };
};

export default { toNewOrder, toUpdatedOrder };
