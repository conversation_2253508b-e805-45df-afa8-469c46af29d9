import { log } from "@store-monorepo/logger";
import Slack from "@store-monorepo/slack-api";

import HTTPAdapter from "../http-adapter";
import OrderSerializer from "./order-serializer";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>UpdateOrder, StockCheck, OrderUpdate } from "./types";

const DOMAIN = process.env.MARCO_API_URL || "https://marco-api.com";
const SLACK_ORDER_CREATION_NOTIFICATION_CHANNEL = process.env.SLACK_ORDER_CREATION_NOTIFICATION_CHANNEL || "";
const NODE_ENV = process.env.NODE_ENV || "production";

interface Order {
  id: string;
  shipTo: string;
  street: string;
  zipCode: string;
  state: string;
  netsuiteId: string;
  priority: string;
  city: string;
  vendor: string;
  products: Product[];
}

interface Product {
  receiverName: string;
  logo: string;
  product: {
    sku: string;
    name: string;
    logoPosition: string;
    size: string;
  };
}

interface CreateOrderResponse {
  status: string;
  message: string;
  order: number;
}

enum Vendor {
  MARCO = "MARCO",
  STORE = "STORE"
}

const createOrder = async (order: Order) => {
  const payload = OrderSerializer.toNewOrder(order);
  log("marco-api", "info", "createOrder", payload);
  return HTTPAdapter.post<MarcoOrder, CreateOrderResponse>(order.vendor, `${DOMAIN}/orders`, payload).
    catch(async error => {
      await Slack.postMessage({
        channel: SLACK_ORDER_CREATION_NOTIFICATION_CHANNEL,
        text: `*Marco Rejected Order ${order.netsuiteId} ${order.vendor}*`,
        attachments: [{
          color: NODE_ENV === "production" ? "danger" : "warning",
          fields: [
            { title: "Order ID", value: order.id, short: true },
            { title: "NetSuite SO ID", value: order.netsuiteId, short: true },
            { title: "Payload", value: Slack.codeBlock(JSON.stringify(payload, null, 2)) },
            { title: "Response", value: Slack.codeBlock(error.message) },
            { title: "Environment", value: NODE_ENV, short: true }
          ]
        }]
      });
      throw error;
    });
};

const orderStatus = async (orderId: string,vendor=Vendor.MARCO) => HTTPAdapter.get<unknown, MarcoOrder>(vendor, `${DOMAIN}/orders/${orderId}`);

const multipleOrderStatus = async (orderIds: string[],vendor=Vendor.MARCO) => HTTPAdapter.post<{
  orderIds: string[]
}, MarcoOrder[]>(vendor, `${DOMAIN}/orders`, { orderIds });

const updateOrder = async (order: OrderUpdate,vendor=Vendor.MARCO) => {
  const payload = OrderSerializer.toUpdatedOrder(order);
  log("marco-api", "info", "updateOrder", payload);
  return HTTPAdapter.put<MarcoUpdateOrder, MarcoOrder>(vendor, `${DOMAIN}/orders/${order.id}`, payload);
};

const cancelOrder = async (orderId: string,vendor=Vendor.MARCO) => HTTPAdapter.del<{
  orderId: string
}, MarcoOrder>(vendor, `${DOMAIN}/orders/${orderId}`);

const stockCheck = async (sku: string,vendor=Vendor.MARCO) => HTTPAdapter.get<{
  sku: string
}, StockCheck>(vendor, `${DOMAIN}/stocks/${sku}`);

export default {
  createOrder,
  orderStatus,
  multipleOrderStatus,
  updateOrder,
  cancelOrder,
  stockCheck
};
