/* eslint-disable functional/no-classes, functional/no-this-expressions, id-denylist */
import Sentry from "@store-monorepo/sentry";

type OptHeaders = {
  vendor: string;
  [key: string]: string;
};

type RequiredHeaders = {
  "Content-Type": string;
  "Authorization": string;
  [key: string]: string;
};

class FetchError extends Error {
  response: Response;
  constructor(resp: Response) {
    super(`Unexpected Marco Response: ${resp.status} ${resp.statusText}`);
    this.name = "FetchError";
    this.response = resp;
  }
}

const getApiKey = (vendor: string) => {
  if (vendor === "MARCO") return process.env.MARCO_API_KEY;
  if (vendor === "STORE") return process.env.MARCO_STORE_API_KEY;
  return "";
};

const constructHeaders = (headers: OptHeaders): { headers: RequiredHeaders } => {
  const { vendor, ...options } = headers;
  const apiKey = getApiKey(vendor);
  return {
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${apiKey}`,
      ...options
    }
  };
};

const performRequest = async <T>(requestFunc: () => Promise<Response>): Promise<T> => {
  const resp = await requestFunc();

  if (!resp.ok) {
    const message = await resp.text().then(_ => _);
    Sentry.captureException(new FetchError(resp));
    throw new Error(`HTTP error! status: ${resp.status}, message: ${message}`);
  }
  return await resp.json();
};

const post = async <Payload, Response>(vendor: string, endpoint: string, params: Payload): Promise<Response> => {
  return performRequest(() => fetch(endpoint, {
    method: "POST",
    body: JSON.stringify(params),
    ...constructHeaders({ vendor })
  })
  );
};

const put = async <Payload, Response>(vendor: string, endpoint: string, params?: Payload): Promise<Response> => {
  return performRequest(() => fetch(endpoint, {
    method: "PUT",
    body: JSON.stringify(params),
    ...constructHeaders({ vendor }),
  })
  );
};

const get = async <Params, Response>(vendor: string, endpoint: string, params?: Params): Promise<Response> => {
  const queryParams = params instanceof URLSearchParams ? new URLSearchParams(params).toString() : "";
  return performRequest(() => fetch(`${endpoint}?${queryParams}`, {
    method: "GET",
    ...constructHeaders({ vendor }),
  })
  );
};

const del = async <Params, Response>(vendor: string, endpoint: string, params?: Params): Promise<Response> => {
  const queryParams = params instanceof URLSearchParams ? new URLSearchParams(params).toString() : "";
  return performRequest(() => fetch(`${endpoint}?${queryParams}`, {
    method: "DELETE",
    ...constructHeaders({ vendor }),
  })
  );
};

export default { post, put, get, del };
