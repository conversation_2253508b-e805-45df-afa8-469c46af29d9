import HttpAdapter from "../lib/http-adapter";

global.fetch = jest.fn();

jest.mock("../lib/http-adapter");

describe("post", () => {
  const API_KEY = "test-api-key";
  const endpoint = "https://example.com/postEndpoint";
  const params = { key: "value" };

  beforeEach(() => {
    (HttpAdapter.post as jest.Mock).mockClear();
  });

  it("should be defined", () => {
    expect(HttpAdapter.post).toBeDefined();
  });

  it("should accept correct parameters", async () => {
    (HttpAdapter.post as jest.Mock).mockResolvedValue({ success: true });

    await HttpAdapter.post(API_KEY, endpoint, params);

    expect(HttpAdapter.post).toHaveBeenCalledWith(API_KEY, endpoint, params);
  });
});

describe("get", () => {
  const API_KEY = "test-api-key";
  const endpoint = "https://example.com/getEndpoint";
  const params = { key: "value" };

  beforeEach(() => {
    (HttpAdapter.get as jest.Mock).mockClear();
  });

  it("should be defined", () => {
    expect(HttpAdapter.get).toBeDefined();
  });

  it("should accept correct parameters", async () => {
    (HttpAdapter.get as jest.Mock).mockResolvedValue({ success: true });

    await HttpAdapter.get(API_KEY, endpoint, params);

    expect(HttpAdapter.get).toHaveBeenCalledWith(API_KEY, endpoint, params);
  });
});
