import OrderSerializer from "../lib/api/order-serializer";
import { OrderItem } from "../lib/api/types";

const ORDER = {
  vendor: "MARCO",
  shipTo: "Test",
  street: "123 Main St",
  city: "Anytown",
  state: "CA",
  shipToEmail: "<EMAIL>",
  shipToPhone: "(*************",
  zipCode: "12345",
  packingSlipId: "1234",
  packingSlipTitle: "",
  netsuiteId: "12345",
  products: []
};

const PRODUCT = {
  receiverName: "<PERSON>",
  product: {
    sku: "sku123",
    name: "T-shirt",
    logoPosition: "center_top",
    size: "XL"
  }
};

describe("toNewOrder", () => {
  it("should be USPS for less than 10 products", () => {
    const order = { ...ORDER, products: Array(5).fill(PRODUCT) };

    const marcoOrder = OrderSerializer.toNewOrder(order);

    expect(marcoOrder.ship_provider).toBe("USPS");
    expect(marcoOrder.ship_method).toBe("");
  });

  it("should be FedEx for 10+ products", () => {
    const order = { ...ORDER, products: Array(10).fill(PRODUCT) };

    const marcoOrder = OrderSerializer.toNewOrder(order);

    expect(marcoOrder.ship_provider).toBe("FedEx");
    expect(marcoOrder.ship_method).toBe("FEDEX_GROUND");
  });

  it("should be FedEx because of shipping address", () => {
    const order = { ...ORDER, shipTo: "PO Box 1729", products: Array(3).fill(PRODUCT) };

    const marcoOrder = OrderSerializer.toNewOrder(order);

    expect(marcoOrder.ship_provider).toBe("FedEx");
    expect(marcoOrder.ship_method).toBe("SMART_POST");
  });

  it("should contain the packing slip url", () => {
    const order = { ...ORDER, products: Array(3).fill(PRODUCT) };

    const marcoOrder = OrderSerializer.toNewOrder(order);

    expect(marcoOrder.packing_slip_url).toEqual(`https://store.snap.app/api/packing-slip/${order.netsuiteId}.pdf`);
  });

  it("should have thumbnail_url the same value of art_url", () => {
    const order = { ...ORDER, products: Array(10).fill(PRODUCT) };
    const marcoOrder = OrderSerializer.toNewOrder(order);

    marcoOrder.items.forEach((item: OrderItem) => {
      expect(item.designs[0].thumbnail_url).toBe(item.designs[0].art_url);
    });
  });

  it("should include email and phone number when present", () => {
    const order = { ...ORDER, products: Array(5).fill(PRODUCT) };

    const marcoOrder = OrderSerializer.toNewOrder(order);

    expect(marcoOrder.ship_to.email).toBe("<EMAIL>");
    expect(marcoOrder.ship_to.telephone).toBe("(*************");
  });
});
