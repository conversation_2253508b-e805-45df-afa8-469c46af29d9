import { OrderUpdate } from "../lib/api/types";
import Marco<PERSON><PERSON> from "../lib/api";

jest.mock("../lib/api");
jest.mock("../lib/http-adapter");

describe("createOrder", () => {
  const mockResponse = { success: true, orderId: 12345 };

  const newOrderParams = {
    id: "so_d0dd4520105b",
    vendor: "MARCO",
    carrier: "USPS",
    shipTo: "Test",
    street: "123 Main St",
    city: "Anytown",
    state: "CA",
    zipCode: "12345",
    packingSlipId: "1234",
    packingSlipTitle: "",
    netsuiteId: "12345",
    products: [],
  };

  beforeEach(() => {
    (MarcoAPI.createOrder as jest.Mock).mockClear();
  });

  it("should call post with correct parameters", async () => {
    (MarcoAPI.createOrder as jest.Mock).mockResolvedValue(mockResponse);
    await MarcoAPI.createOrder(newOrderParams);
    expect(MarcoAPI.createOrder).toHaveBeenCalledWith(newOrderParams);
  });

  it("should handle errors correctly", async () => {
    const errorMessage = "Network error";
    (MarcoAPI.createOrder as jest.Mock).mockRejectedValue(
      new Error(errorMessage)
    );
    await expect(MarcoAPI.createOrder(newOrderParams)).rejects.toThrow(
      errorMessage
    );
  });
});

describe("orderStatus", () => {
  const mockResponse = {
    success: true,
    status: "shipped",
    orderId: "order123",
  };
  const orderId = "order123";

  beforeEach(() => {
    (MarcoAPI.orderStatus as jest.Mock).mockClear();
  });

  it("should call get with correct parameters", async () => {
    (MarcoAPI.orderStatus as jest.Mock).mockResolvedValue(mockResponse);
    await MarcoAPI.orderStatus(orderId);
    expect(MarcoAPI.orderStatus).toHaveBeenCalledWith(orderId);
  });

  it("should handle errors correctly", async () => {
    const errorMessage = "Network error";
    (MarcoAPI.orderStatus as jest.Mock).mockRejectedValue(
      new Error(errorMessage)
    );
    await expect(MarcoAPI.orderStatus(orderId)).rejects.toThrow(errorMessage);
  });
});

describe("multipleOrderStatus", () => {
  const mockResponse = [
    { orderId: "order123", status: "shipped" },
    { orderId: "order456", status: "processing" },
  ];
  const orderIds = ["order123", "order456"];

  beforeEach(() => {
    (MarcoAPI.multipleOrderStatus as jest.Mock).mockClear();
  });
  it("should call post with correct parameters", async () => {
    (MarcoAPI.multipleOrderStatus as jest.Mock).mockResolvedValue(mockResponse);
    await MarcoAPI.multipleOrderStatus(orderIds);
    expect(MarcoAPI.multipleOrderStatus).toHaveBeenCalledWith(orderIds);
  });

  it("should handle errors correctly", async () => {
    const errorMessage = "Network error";
    (MarcoAPI.multipleOrderStatus as jest.Mock).mockRejectedValue(
      new Error(errorMessage)
    );
    await expect(MarcoAPI.multipleOrderStatus(orderIds)).rejects.toThrow(
      errorMessage
    );
  });
});

describe("updateOrder", () => {
  const mockResponse = { success: true };

  const updateOrderParams: OrderUpdate = {
    id: 12345,
    carrier: "USPS",
    shipTo: "Test",
    street: "456 Elm St",
    city: "Anytown",
    state: "CA",
    zipCode: "67890",
    vendor: "MARCO",
  };

  beforeEach(() => {
    (MarcoAPI.updateOrder as jest.Mock).mockClear();
  });

  it("should call post with correct parameters", async () => {
    (MarcoAPI.updateOrder as jest.Mock).mockResolvedValue(mockResponse);
    await MarcoAPI.updateOrder(updateOrderParams);
    expect(MarcoAPI.updateOrder).toHaveBeenCalledWith(updateOrderParams);
  });

  it("should handle errors correctly", async () => {
    const errorMessage = "Network error";
    (MarcoAPI.updateOrder as jest.Mock).mockRejectedValue(
      new Error(errorMessage)
    );
    await expect(MarcoAPI.updateOrder(updateOrderParams)).rejects.toThrow(
      errorMessage
    );
  });
});

describe("cancelOrder", () => {
  const mockResponse = { success: true };

  const orderId = "order123";

  beforeEach(() => {
    (MarcoAPI.cancelOrder as jest.Mock).mockClear();
  });

  it("should call post with correct parameters", async () => {
    (MarcoAPI.cancelOrder as jest.Mock).mockResolvedValue(mockResponse);
    await MarcoAPI.cancelOrder(orderId);
    expect(MarcoAPI.cancelOrder).toHaveBeenCalledWith(orderId);
  });

  it("should handle errors correctly", async () => {
    const errorMessage = "Network error";
    (MarcoAPI.cancelOrder as jest.Mock).mockRejectedValue(
      new Error(errorMessage)
    );
    await expect(MarcoAPI.cancelOrder(orderId)).rejects.toThrow(errorMessage);
  });
});

describe("stockCheck", () => {
  const mockResponse = { success: true, inStock: true, sku: "sku123" };
  const sku = "sku123";

  beforeEach(() => {
    (MarcoAPI.stockCheck as jest.Mock).mockClear();
  });

  it("should call get with correct parameters", async () => {
    (MarcoAPI.stockCheck as jest.Mock).mockResolvedValue(mockResponse);
    await MarcoAPI.stockCheck(sku);
    expect(MarcoAPI.stockCheck).toHaveBeenCalledWith(sku);
  });

  it("should handle errors correctly", async () => {
    const errorMessage = "Network error";
    (MarcoAPI.stockCheck as jest.Mock).mockRejectedValue(
      new Error(errorMessage)
    );
    await expect(MarcoAPI.stockCheck(sku)).rejects.toThrow(errorMessage);
  });
});
