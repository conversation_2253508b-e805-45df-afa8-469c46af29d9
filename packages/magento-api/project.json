{"name": "magento-api", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/magento-api/src", "projectType": "library", "tags": [], "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/packages/magento-api", "main": "packages/magento-api/src/index.ts", "tsConfig": "packages/magento-api/tsconfig.lib.json", "assets": ["packages/magento-api/*.md"]}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"]}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "packages/magento-api/jest.config.ts"}}}}