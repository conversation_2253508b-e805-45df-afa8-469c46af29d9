import HttpClient from "./http-client";

const updateTransaction = async (transactionId: number, {
  netsuiteInternalId,
  netsuiteExternalId,
}: {
  netsuiteInternalId: number;
  netsuiteExternalId: string;
}) => {
  if (!transactionId || !netsuiteInternalId || !netsuiteExternalId) {
    throw new Error("Missing required parameters");
  }
  const baseEndpoint = `/rest/V1/rewardpoints/updatetransaction?transactionID=${transactionId}&netSuiteInternalId=${netsuiteInternalId}&netSuiteExternalId=${encodeURIComponent(netsuiteExternalId)}`;
  return await HttpClient.post(baseEndpoint, {});
};

export default {
  update: updateTransaction
};
