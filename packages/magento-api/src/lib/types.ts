export enum MagentoTransactionType {
  Earned = "Earned",
  Redeem = "Redeem",
  Dashboard_Bought = "Dashboard_Bought",
  Bought = "Bought"
}

enum MagentoEventCode {
  OrderFailureRestore = "order_failure_restore"
}

type MagentoBaseTransaction = {
  transactionId: number;
  customerId: number;
  pointsDelta: number;
  netsuiteInternalId?: number;
  netsuiteExternalId?: string;
}

type MagentoConsumerPointsLiability = MagentoBaseTransaction & {
  transactionType: string;
  senderId?: number;
  receiverId?: number;
  transactionParentGroup?: string;
  isExpired?: boolean;
  pointsToUse?: number;
  parentTransactionId?: number;
  expirationDate?: Date;
  orderId?: number;
  customerBalanceId?: number;
  websiteId?: number;
  storeId?: number;
  pointsBalance?: number;
  eventCode?: string;
  eventData?: Record<string, unknown>;
  ruleId?: number;
  entityId?: number;
  createdAt?: Date;
  isNotificationSent?: boolean;
  expirationPeriod?: number;
  comment?: string;
  isNeedSendNotification?: boolean;
  organizationId?: number;
  paymentEntityId?: number;
  paymentMethod?: string;
  parentNetsuiteExternalId?: string;
  parentNetsuiteInternalId?: number;
  parentTransactionType?: string;
};

type MagentoPointsTransaction = {
  transactionId: number;
  netsuiteInternalId: number;
  netsuiteExternalId: string;
  transactionType?: string;
}

type MagentoRefundPointsLiability = {
  status: string,
  amount: number,
  netsuiteOrderId: number,
  orderId: number,
}

export default {};

export type { MagentoConsumerPointsLiability, MagentoEventCode, MagentoPointsTransaction, MagentoRefundPointsLiability };
