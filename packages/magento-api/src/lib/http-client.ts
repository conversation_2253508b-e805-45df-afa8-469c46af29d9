/* eslint-disable functional/no-classes, functional/no-this-expressions, id-denylist */
const { MAGENTO_BASE_URL, MAGENTO_API_TOKEN } = process.env;

interface FetchResponse {
  status: number;
  statusText: string;
  ok: boolean;
}

type HttpMethod = "GET" | "POST" | "PATCH" | "PUT"

class FetchError extends Error {
  response: FetchResponse;
  constructor(resp: FetchResponse) {
    super(`Unexpected Magento Response: ${resp.status} ${resp.statusText}`);
    this.name = "FetchError";
    this.response = resp;
  }
}

const performRequest = async (
  endpoint: string,
  method: HttpMethod = "GET",
  requestData: object = {},
) => {
  const httpResponse = await fetch(`${MAGENTO_BASE_URL}${endpoint}`, {
    method,
    headers: {
      Accept: "application/json",
      "Content-Type": "application/json",
      Authorization: `Bearer ${MAGENTO_API_TOKEN}`,
    },
    body: method.toUpperCase() === "GET" ? undefined : JSON.stringify(requestData),
  });

  if (!httpResponse.ok) {
    throw new FetchError(httpResponse);
  }

  return httpResponse.json();
};

export default {
  get: async (url: string) => performRequest(url, "GET"),
  post: async (url: string, data: object) => performRequest(url, "POST", data),
  patch: async (url: string, data: object) => performRequest(url, "PATCH", data),
  put: async (url: string, data: object) => performRequest(url, "PUT", data)
};
