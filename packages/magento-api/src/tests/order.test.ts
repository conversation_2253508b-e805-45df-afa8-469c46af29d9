/* eslint-disable max-lines-per-function,
functional/no-let, id-denylist, @typescript-eslint/no-explicit-any,sonarjs/no-duplicate-string */
import HttpClient from "../lib/http-client";
import Order from "../lib/order";

jest.mock("../lib/http-client");

describe("order", () => {
  describe("addNetsuiteId", () => {
    const mockPut = HttpClient.put as jest.MockedFunction<typeof HttpClient.post>;
    let consoleErrorSpy: jest.SpyInstance;

    beforeEach(() => {
      jest.clearAllMocks();
      consoleErrorSpy = jest.spyOn(console, "error").mockImplementation(() => {});
    });

    afterEach(() => {
      consoleErrorSpy.mockRestore();
    });

    it("should successfully update a transaction", async () => {
      const mockResponse = { status: 200, data: { success: true } };
      mockPut.mockResolvedValue(mockResponse);

      const result = await Order.addNetsuiteId("123", "234");

      expect(mockPut).toHaveBeenCalledWith(
        `/rest/V1/orders/${123}/netsuite-id`,
        { netsuiteOrderId: "234" }
      );
      expect(result).toEqual(mockResponse);
    });
  });
});
