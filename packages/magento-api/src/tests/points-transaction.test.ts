/* eslint-disable max-lines-per-function,
functional/no-let, id-denylist, @typescript-eslint/no-explicit-any,sonarjs/no-duplicate-string */
import HttpClient from "../lib/http-client";
import pointsTransaction from "../lib/points-transaction";

jest.mock("../lib/http-client");

describe("pointsTransaction", () => {
  describe("update", () => {
    const mockPost = HttpClient.post as jest.MockedFunction<typeof HttpClient.post>;
    let consoleErrorSpy: jest.SpyInstance;

    beforeEach(() => {
      jest.clearAllMocks();
      consoleErrorSpy = jest.spyOn(console, "error").mockImplementation(() => {});
    });

    afterEach(() => {
      consoleErrorSpy.mockRestore();
    });

    it("should successfully update a transaction", async () => {
      const mockResponse = { status: 200, data: { success: true } };
      mockPost.mockResolvedValue(mockResponse);

      const result = await pointsTransaction.update(123, {
        netsuiteInternalId: 456,
        netsuiteExternalId: "EXT789",
      });

      expect(mockPost).toHaveBeenCalledWith(
        "/rest/V1/rewardpoints/updatetransaction?transactionID=123&netSuiteInternalId=456&netSuiteExternalId=EXT789",
        {}
      );
      expect(result).toEqual(mockResponse);
    });

    it("should throw an error when missing transactionId", async () => {
      await expect(
        pointsTransaction.update(undefined as any, {
          netsuiteInternalId: 456,
          netsuiteExternalId: "EXT789",
        })
      ).rejects.toThrow("Missing required parameters");
    });

    it("should throw an error when missing netsuiteInternalId", async () => {
      await expect(
        pointsTransaction.update(123, {
          netsuiteInternalId: undefined as any,
          netsuiteExternalId: "EXT789",
        })
      ).rejects.toThrow("Missing required parameters");
    });

    it("should throw an error when missing netsuiteExternalId", async () => {
      await expect(
        pointsTransaction.update(123, {
          netsuiteInternalId: 456,
          netsuiteExternalId: undefined as any,
        })
      ).rejects.toThrow("Missing required parameters");
    });

    it("should correctly encode netsuiteExternalId", async () => {
      const mockResponse = { status: 200, data: { success: true } };
      mockPost.mockResolvedValue(mockResponse);

      await pointsTransaction.update(123, {
        netsuiteInternalId: 456,
        netsuiteExternalId: "EXT 789 & 123",
      });

      expect(mockPost).toHaveBeenCalledWith(
        "/rest/V1/rewardpoints/updatetransaction?transactionID=123&netSuiteInternalId=456&netSuiteExternalId=EXT%20789%20%26%20123",
        {}
      );
    });
  });
});
