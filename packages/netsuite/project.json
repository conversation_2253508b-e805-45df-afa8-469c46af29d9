{"name": "netsuite", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/netsuite/src", "projectType": "library", "tags": [], "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/packages/netsuite", "main": "packages/netsuite/src/index.ts", "tsConfig": "packages/netsuite/tsconfig.lib.json", "assets": ["packages/netsuite/*.md"]}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"]}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "packages/netsuite/jest.config.ts"}}}}