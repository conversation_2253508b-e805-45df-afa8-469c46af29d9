/* eslint-disable */
process.env.NETSUITE_CUSTOMER_ID = "12345";
process.env.NETSUITE_ACCOUNT_ID = "test_app_id";
process.env.NETSUITE_APPLICATION_ID = "12345";
process.env.NETSUITE_CLIENT_KEY = "test_client_key";
process.env.NETSUITE_CLIENT_SECRET = "test_client_secret";
process.env.NETSUITE_TOKEN_KEY = "test_token_key";
process.env.NETSUITE_TOKEN_SECRET = "test_token_secret";

export default {
  displayName: "@store-monorepo/netsuite",
  preset: "../../jest.preset.js",
  testEnvironment: "node",
  transform: {
    "^.+\\.[tj]s$": ["ts-jest", { tsconfig: "<rootDir>/tsconfig.spec.json" }],
  },
  moduleFileExtensions: ["ts", "js", "html"],
  coverageDirectory: "../../coverage/packages/netsuite",
};
