/* eslint-disable max-lines-per-function */
/* eslint-disable no-inline-comments */
/* eslint-disable max-lines */
import SalesOrder from "../lib/sales-order";
import HttpClient from "../lib/http-client";
import { NetsuiteStatus } from "../lib/types";
const { createItemFulfillment, create, updateShipmentWorkflowStage, unsetShippingMethod, getSalesOrder } = SalesOrder;
jest.mock("../lib/http-client");
jest.mock("fs");

describe("SalesOrder", () => {
  const mockOrder = (id?: string) => ({
    id,
    vendor: "MARCO",
    carrier: "UPS",
    shipTo: "John Hickle",
    street: "12083 Dorcas Tunnel",
    zipCode: "51940-7250",
    state: "AZ",
    status: "DESIGN",
    line2: "Cool Organization",
    street2: "Unit 1",
    city: "Beierton",
    packingSlipId: "cd6a6696-90ed-4424-a25a-947486229b83",
    packingSlipTitle: "ae52a6cf-95bb-4bd3-8204-f4b02446b304",
    netsuiteId: "082dc15c-f1e2-49ae-9dbc-a067933379fc",
    trackingNumber: null,
    products: [],
    fundraiserId: "191919",
    shipToEmail: "<EMAIL>",
    shipToPhone: "1234567890",
    billingStreet: "456 Billing St",
    billingStreet2: "Suite 7",
    billingCity: "Billtown",
    billingState: "NY",
    billingZipCode: "67890",
    trackingUrl: null
  });

  const mockSalesOrder = {
    id: "12345",
    item: {
      items: [
        {
          item: { id: "123" },
          line: 1,
          quantity: 2,
          rate: 50,
          description: "Item 1"
        },
        {
          item: { id: "1664" },
          line: 2,
          quantity: 2,
          rate: 50,
          description: "Discount"
        }
      ]
    }
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (HttpClient.get as jest.Mock).mockResolvedValue({ body: mockSalesOrder });
    (HttpClient.post as jest.Mock).mockResolvedValue({});
    (HttpClient.patch as jest.Mock).mockResolvedValue({});
  });

  describe("createItemFulfillment", () => {
    it("should create item fulfillment and update shipment workflow stage", async () => {
      const order = mockOrder();
      await createItemFulfillment(order);

      expect(HttpClient.patch).toHaveBeenCalledWith(`/salesorder/${order.netsuiteId}`, { shipMethod: null });
      expect(HttpClient.get).toHaveBeenCalledWith(`/salesorder/${order.netsuiteId}?expandSubResources=true`);
      expect(HttpClient.post).toHaveBeenCalledWith(`/salesorder/${order.netsuiteId}/!transform/itemfulfillment`, expect.objectContaining({
        createdFrom: mockSalesOrder.id,
        item: { items: expect.any(Array) },
        package: {
          items: [{
            packageTrackingNumber: order.trackingNumber,
            packageWeight: 1.0
          }]
        },
        custbody_nm_shipstation_ship_trkng_url: order.trackingUrl,
        generateIntegratedShippingLabel: false,
        memo: "Item fulfillment created via Marco Fine Arts"
      }));
      expect(HttpClient.patch).toHaveBeenCalledWith(`/salesorder/${order.netsuiteId}`, {
        custbody_shipment_workflow_stage: 10 // Shipped
      });
    });
  });

  describe("create", () => {
    it("should create order and return NetSuite ID", async () => {
      const mockResponse = {
        headers: {
          location: "https://account-id.netsuite.com/salesorder/12345"
        }
      };
      (HttpClient.post as jest.Mock).mockResolvedValue(mockResponse);

      const netsuiteOrder = {
        entity: { id: 1 },
        email: "<EMAIL>",
        custbody_order_type: 1,
        custbody_shipment_workflow_stage: 1,
        custbody_primary_contact_name: "1",
        custbody_primary_contact_email: "1",
        custbody_primary_contact_phone: "1",
        custbody_packing_slip_id: "1",
        custbody_packing_slip_title: "1",
        custbody_carrier: "1",
        custbody_vendor: "1",
        item: { items: [] },
        billingaddress: {
          addr1: "1",
          addr2: "1",
          city: "1",
          state: "1",
          zip: "1",
          country: "1",
          addressee: "1"
        },
        shippingaddress: {
          addr1: "1",
          addr2: "1",
          city: "1",
          state: "1",
          zip: "1",
          country: "1",
          addressee: "1"
        }
      };

      const result = await create(netsuiteOrder);

      expect(HttpClient.post).toHaveBeenCalledWith("/salesorder", netsuiteOrder);
      expect(result).toBe(12345);
    });

    it("should return null and log error on failure", async () => {
      const mockError = new Error("Failed to create order");
      (HttpClient.post as jest.Mock).mockRejectedValue(mockError);

      console.error = jest.fn();

      const netsuiteOrder = {
        entity: { id: 2 },
        email: "<EMAIL>",
        custbody_order_type: 2,
        custbody_shipment_workflow_stage: 2,
        custbody_primary_contact_name: "2",
        custbody_primary_contact_email: "2",
        custbody_primary_contact_phone: "2",
        custbody_packing_slip_id: "2",
        custbody_packing_slip_title: "2",
        custbody_carrier: "2",
        custbody_vendor: "2",
        item: { items: [] },
        billingaddress: {
          addr1: "2",
          addr2: "2",
          city: "2",
          state: "2",
          zip: "2",
          country: "2",
          addressee: "2"
        },
        shippingaddress: {
          addr1: "2",
          addr2: "2",
          city: "2",
          state: "2",
          zip: "2",
          country: "2",
          addressee: "2"
        }
      };
      const result = await create(netsuiteOrder);

      expect(HttpClient.post).toHaveBeenCalledWith("/salesorder", netsuiteOrder);
      expect(result).toBeNull();
      expect(console.error).toHaveBeenCalledWith("Error Details:", mockError);
    });
  });

  describe("updateShipmentWorkflowStage", () => {
    it("should update shipment workflow stage", async () => {
      const order = mockOrder();
      await updateShipmentWorkflowStage(order, NetsuiteStatus.Shipped);

      expect(HttpClient.patch).toHaveBeenCalledWith(`/salesorder/${order.netsuiteId}`, {
        custbody_shipment_workflow_stage: 10
      });
    });
  });

  describe("unsetShippingMethod", () => {
    it("should unset shipping method", async () => {
      const order = mockOrder();
      await unsetShippingMethod(order.netsuiteId);

      expect(HttpClient.patch).toHaveBeenCalledWith(`/salesorder/${order.netsuiteId}`, { shipMethod: null });
    });
  });

  describe("getSalesOrder", () => {
    it("should fetch and return sales order data with expanded subresources", async () => {
      const order = mockOrder();
      const result = await getSalesOrder(order.netsuiteId);

      expect(HttpClient.get).toHaveBeenCalledWith(`/salesorder/${order.netsuiteId}?expandSubResources=true`);
      expect(result).toEqual(mockSalesOrder);
    });
  });
});
