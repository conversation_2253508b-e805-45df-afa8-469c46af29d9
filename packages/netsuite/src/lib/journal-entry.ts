import HttpClient from "./http-client";
import { NetsuiteJournalEntry, NetsuiteJournalEntryId } from "./types";
import Helpers from "./helpers";

const createJournalEntry = async (record: NetsuiteJournalEntry): Promise<NetsuiteJournalEntryId | null> => {
  try {
    const journalEntryResponse = await HttpClient.post(`/journalentry`, record);
    return Helpers.extractNetsuiteIdFromUrl(journalEntryResponse.headers.location);
  } catch (error) {
    console.log(error.message);
    return null;
  }
};

export default {
  create: createJournalEntry
};
