const config = {
  ACCOUNTS: {
    POINTS_LIABILITY: 1114,
    POINTS_DEFERRED: 1531,
    STRIPE: 1543,
    PAYPAL: 1544,
    REFUND_LIABILITY: 917,
    REFUND_DEFERRED: 1008
  },
  SUBSIDIARY_ID: 1,
  USD_CURRENCY_ID: 1,
  CLASS_ID: 19,
  DEPARTMENT_ID: 30,
  ORDER_TYPE: 7,
  CUSTOMER_ID: parseInt(process.env.NETSUITE_CUSTOMER_ID),
  PAYMENT_METHOD: {
    STRIPE: 10,
    PAYPAL: 9
  },
  WORKFLOW_STAGE: {
    NEW_ORDER: 2,
  },
  CUSTOM_FORM: {
    ORDER: 126,
    DEPOSIT: 134
  },
  SHIPPING: 0.00,
  DISCOUNT_ITEM_ID: 1664,
  POINTS_ITEM_ID: 14103,
  TAX_ITEM_ID: 1645,
  GIFT_CARD_ITEM_ID: process.env.NODE_ENV === "production" ? 24103 : 14623,
  SHIPPING_ITEM_ID: 1644,
  SHIPPING_METHOD: {
    FEDEX: 1468,
    USPS: 13908
  }
} as const;

export default config;
