/* eslint-disable max-len */

export enum NetsuiteStatus {
  Packed = "Packed",
  Shipped = "Shipped",
  MarcoCreated = "MarcoCreated",
  <PERSON><PERSON>ej<PERSON><PERSON> = "MarcoRejected",
  Rejected = "Rejected",
  Design = "Design"
}

const netsuiteOrderWorkflowStatus = {
  Design: 2,
  Packed: 8,
  Shipped: 10,
  <PERSON>Created: 12,
  <PERSON>Rejected: 13,
  Rejected: 14
} as const;

type NetsuiteWorkflowStatus = typeof netsuiteOrderWorkflowStatus[keyof typeof netsuiteOrderWorkflowStatus];

type NetsuiteOrderAddress = {
  addr1: string;
  addr2: string;
  city: string;
  state: string;
  zip: string;
  country: string;
  addressee: string;
}

type NetsuiteLineItem = {
  item: { id: number },
  quantity: number,
  rate: number,
  description?: string,
  line?: number,
  taxAmount?: number;
  discountAmount?: number;
  custcol_sku?: string;
  custcolcust_col_logourl?: string;
  custcolname_text?: string
  custcolname_style?: string;
  custcolname_font_color?: string;
  custcolnumber_text?: string
  custcolnumber_color?: string;
}

type NetsuiteSalesOrder = {
  id: string,
  item: {
    items: NetsuiteLineItem[]
  }
}

type NetsuiteOrderProducts = {
  items: NetsuiteLineItem[];
}

type NetsuiteCustomerId = {
  id: number;
}

type NetsuiteOrder = {
  entity: NetsuiteCustomerId;
  email: string;
  subsidiary?: { id: number };
  class?: { id: number };
  department?: { id: number };
  custbody_order_type: number;
  custbody_shipment_workflow_stage: number;
  custbody_primary_contact_name: string;
  custbody_primary_contact_email: string;
  custbody_primary_contact_phone: string;
  custbodytxn_id?: string;
  custbodydesign_details_store?: string;
  custbody_fundraiser_id?: string;
  custbodycustbodypayment_method?: string;
  custbodyorder_confirmation?: string;
  externalId?: string;
  item: NetsuiteOrderProducts;
  shippingaddress: NetsuiteOrderAddress;
  billingaddress: NetsuiteOrderAddress;
}

type NetsuiteCustomerDeposit = {
  customer: {id: number},
  currency: {id: number},
  payment: number,
  externalId: string,
  department?: {id: number},
  account?: {id: number},
  class?: {id: number},
  custbodyorg_id?: number,
  salesOrder?: {externalId: string}
  custbodytxn_id?: string
  paymentOption?: {id: number},
  custbodycustbodypayment_method?: number,
  custbodypayment_method?: number,
  customForm?: {id: number},
  custbodyactive_points?: number,
  custbodytotal_points?: number,
}

type NetsuiteCustomerDepositId = number;

type NetsuiteOrderId = number;
type NetsuiteJournalEntryLineItem = {
  account: {id: number},
  debit?: number,
  credit?: number,
  memo: string,
}
type NetsuiteJournalEntry = {
  subsidiary: {id: number},
  externalId: string,
  line: {
    items: NetsuiteJournalEntryLineItem[]
  };
  memo: string;
}

type NetsuiteJournalEntryId = number;


export default {};

export type { NetsuiteWorkflowStatus, NetsuiteOrderAddress, NetsuiteOrder, NetsuiteCustomerDeposit, NetsuiteCustomerDepositId, NetsuiteOrderId, NetsuiteJournalEntry, NetsuiteJournalEntryId, NetsuiteSalesOrder, NetsuiteLineItem };
