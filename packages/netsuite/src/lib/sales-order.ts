import HttpClient from "./http-client";
import { NetsuiteOrder, NetsuiteStatus, NetsuiteSalesOrder, NetsuiteOrderId } from "./types";
import Config from "./config";
import Helpers from "./helpers";

const orderWorkflowStatus = {
  Design: 2,
  Picking: 6,
  Packed: 8,
  Shipped: 10,
  <PERSON>Created: 12,
  <PERSON><PERSON>ej<PERSON>ted: 13,
  Rejected: 14
};

export const updateShipmentWorkflowStage = async (order: Order, status: NetsuiteStatus) => {
  return HttpClient.patch(`/salesorder/${order.netsuiteId}`, {
    custbody_shipment_workflow_stage: orderWorkflowStatus[status]
  });
};

// Unsets the default ShipStation ship method, which otherwise prevents adding tracking numbers through the REST API
const unsetShippingMethod = async (id: string) => HttpClient.patch(`/salesorder/${id}`, { shipMethod: null });

const getSalesOrder = async (id: string): Promise<NetsuiteSalesOrder> => {
  const orderResponse = await HttpClient.get(`/salesorder/${id}?expandSubResources=true`);
  return orderResponse.body;
};

interface Order {
  netsuiteId: string;
  trackingUrl: string | null;
  trackingNumber: string | null;
}

const createItemFulfillment = async (order: Order) => {
  await unsetShippingMethod(order.netsuiteId);
  const salesOrder = await getSalesOrder(order.netsuiteId);
  const body = {
    shipStatus: "C",
    createdFrom: salesOrder.id,
    item: { items: salesOrder.item.items.filter(item => `${item.item.id}` !== `${Config.DISCOUNT_ITEM_ID}`).map((item) => ({
      orderLine: item.line,
      quantity: item.quantity,
      quantityFulfilled: item.quantity,
      rate: item.rate,
      description: item.description
    })) },
    package: {
      items: [{
        packageTrackingNumber: order.trackingNumber,
        packageWeight: 1.0
      }]
    },
    custbody_nm_shipstation_ship_trkng_url: order.trackingUrl,
    generateIntegratedShippingLabel: false,
    memo: "Item fulfillment created via Marco Fine Arts"
  };

  await HttpClient.post(`/salesorder/${order.netsuiteId}/!transform/itemfulfillment`, body);
  await updateShipmentWorkflowStage(order, NetsuiteStatus.Shipped);
};


const createOrder = async (netsuiteOrder: NetsuiteOrder): Promise<NetsuiteOrderId | null> => {
  try {
    const orderResponse = await HttpClient.post("/salesorder", netsuiteOrder);
    return Helpers.extractNetsuiteIdFromUrl(orderResponse.headers.location);
  } catch (error) {
    console.error("Error Details:", error);
    return null;
  }
};

const getClosedSalesOrders = async (limit: number) => {
  const q = `
  SELECT TOP ${limit} id, tranId, lastmodifieddate
  FROM transaction
  WHERE recordtype = 'salesorder' AND status = 'H'
  ORDER BY lastmodifieddate DESC
  `;
  return HttpClient.query({ q });
};


const SalesOrder = {
  create: createOrder,
  createItemFulfillment,
  getClosedSalesOrders,
  getSalesOrder,
  updateShipmentWorkflowStage,
  unsetShippingMethod,
  orderWorkflowStatus
};

export default SalesOrder;
