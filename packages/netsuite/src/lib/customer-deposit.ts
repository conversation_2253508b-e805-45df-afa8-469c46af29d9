import HttpClient from "./http-client";
import { NetsuiteCustomerDepositId } from "./types";
import Helpers from "./helpers";

const createCustomerDeposit = async (transaction): Promise<NetsuiteCustomerDepositId | null> => {
  try {
    const customerDepositResponse = await HttpClient.post(`/customerdeposit`, transaction);
    return Helpers.extractNetsuiteIdFromUrl(customerDepositResponse.headers.location);
  } catch (error) {
    console.log(error.message);
    return null;
  }
};


export default {
  create: createCustomerDeposit
};
