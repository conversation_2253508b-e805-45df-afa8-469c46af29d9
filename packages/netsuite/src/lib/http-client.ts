/* eslint-disable max-statements */
/* eslint-disable functional/no-return-void */
/* eslint-disable functional/immutable-data */
/* eslint-disable functional/no-classes, functional/no-this-expressions, id-denylist */
const { createHmac } = require("node:crypto");
const OAuth = require("oauth-1.0a");

const ACCOUNT_ID = process.env.NETSUITE_ACCOUNT_ID;
const BASE_URL = `https://${ACCOUNT_ID.toLowerCase()}.suitetalk.api.netsuite.com/services/rest/record/v1`;
const SUITEQL_URL =`https://${ACCOUNT_ID.toLowerCase()}.suitetalk.api.netsuite.com/services/rest/query/v1/suiteql`;
const NETSUITE_TOKEN_CONFIG = {
  key: process.env.NETSUITE_TOKEN_KEY,
  secret: process.env.NETSUITE_TOKEN_SECRET,
};

const oauth = OAuth({
  consumer: {
    key: process.env.NETSUITE_CLIENT_KEY,
    secret: process.env.NETSUITE_CLIENT_SECRET,
  },
  realm: ACCOUNT_ID?.split("-").join("_").
    toUpperCase(),
  signature_method: "HMAC-SHA256",
  parameter_separator: ",",
  nonce_length: 11,
  hash_function(base_string: string, key: string) {
    return createHmac("sha256", key).update(base_string).
      digest("base64");
  },
});

interface FetchResponse {
  status: number;
  statusText: string;
  ok: boolean;
}

class FetchError extends Error {
  response: FetchResponse;
  constructor(resp: FetchResponse) {
    super(`Unexpected NetSuite Response: ${resp.status} ${resp.statusText}`);
    this.name = "FetchError";
    this.response = resp;
  }
}

type HttpMethod = "GET" | "POST" | "PATCH"

const performRequest = async (baseUrl: string, path, method: HttpMethod = "GET", data = {}) => {
  const request_data = {
    url: `${baseUrl}${path}`,
    method: method.toUpperCase(),
    data,
    includeBodyHash: true
  };

  const oauthHeaders = oauth.toHeader(oauth.authorize(request_data, NETSUITE_TOKEN_CONFIG));

  const headers = {
    ...oauthHeaders,
    Accept: "*/*",
    "Content-Type": "application/json",
    ...(baseUrl === SUITEQL_URL && { Prefer: "transient" })
  };

  const response = await fetch(request_data.url, {
    method: request_data.method,
    headers: { ...oauthHeaders, ...headers },
    body: method.toUpperCase() !== "GET" ? JSON.stringify(request_data.data) : undefined,
  });

  const responseHeaders: Record<string, string> = {};
  response.headers.forEach((value, key) => {
    responseHeaders[key] = value;
  });

  const isJSON = response.headers.get("content-type")?.includes("json");
  if (response.status === 204) return { body: {}, headers: responseHeaders };
  if (!response.ok || !isJSON) throw new FetchError(response);
  const body = await response.json();
  return { body, headers: responseHeaders };
};

export default {
  get: async (path: string) => performRequest(BASE_URL, path),
  post: async (path: string, data: object) => performRequest(BASE_URL,path, "POST", data),
  patch: async (path: string, data: object) => performRequest(BASE_URL, path, "PATCH", data),
  query: async (data: object) => performRequest(SUITEQL_URL, "", "POST", data)
};
