/* eslint-disable functional/no-return-void, sonarjs/cognitive-complexity, @typescript-eslint/no-explicit-any, */
import * as Sentry from "@sentry/node";

const ApolloServerPlugin = () => {
  return {
    async requestDidStart() {
      return {
        async didEncounterErrors(rc: any) {
          Sentry.withScope((scope) => {
            scope.addEventProcessor((event): null => {
              const request = rc?.context?.req ? rc.context.req : rc.request;
              if (request) {
                Sentry.addRequestDataToEvent(event, request);
              }
              return null;
            });

            const user = rc.context?.user || rc.contextValue?.user;

            if (user) {
              scope.setUser({
                id: user.sub,
                email: user.email,
              });
            }

            scope.setTags({
              graphql: rc.operation?.operation || "parse_err",
              graphqlName:
                (rc.operationName as string) ||
                (rc.request.operationName as string),
            });

            rc.errors.forEach((error: any) => {
              if (error.path || error.name !== "GraphQLError") {
                scope.setExtras({
                  path: error.path,
                });
                Sentry.captureException(error);
              } else {
                scope.setExtras({});
                Sentry.captureMessage(`GraphQLWrongQuery: ${error.message}`);
              }
            });
          });
        },
      };
    },
  };
};

export default ApolloServerPlugin;
