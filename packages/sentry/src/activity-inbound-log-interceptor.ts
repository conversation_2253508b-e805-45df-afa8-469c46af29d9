/* eslint-disable functional/no-classes, functional/no-this-expressions, functional/no-let, max-statements, id-denylist */
import * as Sentry from "@sentry/node";
import { ActivityExecuteInput, Next, ActivityInboundCallsInterceptor } from "@temporalio/worker";
import { Context } from "@temporalio/activity";

class ActivityInboundLogInterceptor {
  ctx: Context;

  constructor(ctx: Context) {
    this.ctx = ctx;
  }

  async execute(
    input: ActivityExecuteInput,
    next: Next<ActivityInboundCallsInterceptor, "execute">
  ) {
    let error: unknown | undefined;

    try {
      return await next(input);
    } catch (err: unknown) {
      error = err;
      throw err;
    } finally {
      if (error) {
        Sentry.captureException(error);
      }
    }
  }
}

export default ActivityInboundLogInterceptor;
