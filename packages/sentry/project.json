{"name": "sentry", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/sentry/src", "projectType": "library", "tags": [], "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/packages/sentry", "main": "packages/sentry/src/index.ts", "tsConfig": "packages/sentry/tsconfig.lib.json", "assets": ["packages/sentry/*.md"]}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"]}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "packages/sentry/jest.config.ts"}}}}