/* eslint-disable import/no-unused-modules, functional/no-return-void, check-file/folder-match-with-fex */
import { LOGO_TYPE, StoreType } from "@store-monorepo/repo";

import { parseFileExtension, subject } from "../src/tickets/raise-logo-design";

const STORE: StoreType = {
  id: "store_7b000000000e",
  fundraiserId: 227457,
  fundraiserEntityId: 611532,
  activityType: "softball",
  startDate: new Date("2024-11-01T00:00:00.000Z"),
  endDate: new Date("2024-11-29T00:00:00.000Z"),
  city: "Br***k",
  state: "minnesota",
  zip: "55428",
  slug: "mn-spice-softball-2024",
  status: "approved",
  teamSize: 12,
  name: "MN***all",
  fanStore: true,
  incentiveStore: true,
  hasParticipants: false,
  organizationId: 81433,
  organizationLegalName: "P***y",
  organizationName: "Pi***y",
  salesRep: "J* A*",
  salesRepEmail: "j***<EMAIL>",
  salesRepUDID: "udu_clm127bz7002u0000000t6ap6",
  groupLeader: "T* M*",
  groupLeaderEmail: "m*t@d**9.org",
  groupLeaderUDID: "udu_clr6xsndv01ae0000004cwo",
  logoPrimaryColor: "#0C0C10|#2F3032",
  logoSecondaryColor: "#ED1C24|#B42232",
  logoDigitalUrl: "/uploads/tmp/1727501932-32-0027-6548/de49645d-9fb8-4f00-a6b0-c22b88f50cf7_d.svg",
  logoEmbroideryUrl: "/uploads/tmp/1727501932-32-0026-7512/de49645d-9fb8-4f00-a6b0-c22b88f50cf7_e.svg",
  logoHatUrl: "/uploads/tmp/1727501932-32-0025-3329/de49645d-9fb8-4f00-a6b0-c22b88f50cf7_h.svg",
  logoWebHeaderUrl: "/uploads/tmp/1727501932-32-0028-1569/de49645d-9fb8-4f00-a6b0-c22b88f50cf7_w.svg",
  logo: "74",
  previousLogo: false,
  logoScript: "MN SPICE | SOFTBALL",
  emailLogo: null,
  fundraiserLogo: "Screenshot_2024-09-27_at_10.38.12_PM.png",
  fundraiserLogoId: 2027913,
  raiseCreatedAt: new Date("2024-09-28T05:38:53.000Z"),
  raiseUpdatedAt: new Date("2024-09-28T05:38:53.000Z"),
  logoUpdatedAt: new Date("2024-09-28T05:38:54.000Z"),
  createdAt: new Date("2024-09-28T05:38:55.406Z"),
  updatedAt: new Date("2024-09-28T05:38:55.406Z"),
  teamId: "team_2a9874aa2c42000000001b",
  groupId: 133761,
  groupName: "softball",

  storeUrl: null,
  accountManager: null,
  accountManagerEmail: null,
  accountManagerUDID: null,
  fundraiserPrimaryColor: null,
  fundraiserSecondaryColor: null,
  logoDigitalVerifiedAt: null,
  logoEmbroideryVerifiedAt: null,
  logoHatVerifiedAt: null,
  logoWebHeaderVerifiedAt: null,
  magentoStoreId: null,
  magentoStoreCode: null,
  magentoManagerEmail: null,
  builtAt: null,

  logoType: LOGO_TYPE.TEAM,
  logoNotes: "Hoover above logo Soccer below logo",
  deactivatedAt: null,
  pointsPercentage: 10,
  storeCode: null,
  partnerId: null
};

describe("RaiseLogoDesign", () => {
  describe("parseFileExtension", () => {
    it("should return the file extension", () => {
      expect(parseFileExtension(null)).toEqual("png");
      expect(parseFileExtension(undefined)).toEqual("png");
      expect(parseFileExtension("")).toEqual("png");

      expect(parseFileExtension("normal_community_HS.png")).toEqual("png");
      expect(parseFileExtension("normal_community_HS.jpg")).toEqual("jpg");
      expect(parseFileExtension("normal_community_HS.jpeg")).toEqual("jpeg");
      expect(parseFileExtension("normal_community_HS.gif")).toEqual("gif");
    });
  });

  describe("subject", () => {
    it("should handle the incentive store", () => {
      const store: StoreType = { ...STORE, fanStore: false, incentiveStore: true };
      expect(subject(store)).toEqual(`Incentive Order|${STORE.fundraiserId}`);
    });

    it("should handle the team logo", () => {
      const store: StoreType = { ...STORE, logoType: LOGO_TYPE.TEAM };
      expect(subject(store)).toEqual(`Team Logo Request|${STORE.fundraiserId}`);
    });

    it("should handle the template logo", () => {
      const store: StoreType = { ...STORE, logoType: LOGO_TYPE.TEMPLATE };
      expect(subject(store)).toEqual(`Template Logo Request|${STORE.fundraiserId}`);
    });
  });
});
