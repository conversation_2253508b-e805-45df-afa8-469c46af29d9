import Helper from "../src/helper";

describe("Helper", () => {
  describe("toDateString", () => {
    it("should return the date as the string", () => {
      const today = (new Date()).toISOString().split("T")[0];

      expect(Helper.toDateString()).toEqual(today);
      expect(Helper.toDateString(null)).toEqual(today);
      expect(Helper.toDateString(new Date())).toEqual(today);

      expect(Helper.toDateString("2024-10-08T15:25:07.000Z")).toEqual("2024-10-08");
      expect(Helper.toDateString(new Date("2024-10-08T15:25:07.000Z"))).toEqual("2024-10-08");
    });
  });
});
