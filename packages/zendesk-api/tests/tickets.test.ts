/* eslint-disable max-lines, max-lines-per-function, sonarjs/no-duplicate-string */
import nock from "nock";

import Ticket from "../src/tickets";
import { CreateTicketInput, Fundraiser, Order } from "../src/types";

const ZENDESK_SUBDOMAIN = process.env.ZENDESK_SUBDOMAIN;

const FUNDRAISER: Fundraiser = {
  id: "191919",
  name: "Glenview Band - 2023"
};

const ORDER: Order = {
  id: "so_6af182be37d7",
  fundraiserId: null,
  scheduleAt: new Date("2025-03-14T15:42:27.000Z"),
  vendor: "STORE",
  carrier: "USPS",
  shipTo: "D**e F**t",
  shipToEmail: "<EMAIL>",
  shipToPhone: "7203336265",
  line2: null,
  street: "address_street1",
  street2: "",
  city: "Denver",
  state: "CO",
  zipCode: "000000",
  billingStreet: "address_billingStreet",
  billingStreet2: "",
  billingCity: "Denver",
  billingState: "CO",
  billingZipCode: "000000",
  packingSlipId: "29823430",
  packingSlipTitle: "",
  trackingNumber: null,
  trackingUrl: null,
  netsuiteId: "29823430",
  externalId: null,
  createdAt: new Date("2025-03-13T15:43:22.096Z"),
  updatedAt: new Date("2025-03-13T15:43:22.096Z"),
  status: "DESIGN",
  baseSubtotal: 14500,
  shippingCost: null,
  taxAmount: 625,
  discountAmount: 0,
  giftCardAmount: null,
  orderId: "34340",
  confirmationId: "SNAP000029134",
  shippingReceivedAt: null,
  products: [
    {
      id: "cm87iqigf007xf6x7mq4e20cr",
      orderId: "so_6af182be37d7",
      productId: "pdct_bc2d761ad9a6",
      logo: "https://snapraiselogos.s3.us-west-1.amazonaws.com/MarcoLogos/1173220_SpartaRobotica_DigitalPrint_RGB_2500x2500_100721.png",
      receiverName: "Danielle Frost",
      printAttributes: null,
      backLogo: null,
      createdAt: new Date("2025-03-13T15:43:22.096Z"),
      updatedAt: new Date("2025-03-13T15:43:22.096Z"),
      amount: 5200,
      product: {
        id: "pdct_bc2d761ad9a6",
        name: "Champion Adult Powerblend Pullover Hooded Sweatshirt",
        sku: "S700-FTG_Light_Steel_L",
        netsuiteId: "21625",
        magentoSku: "MRC-S700-FTG_Light_Steel_L",
        color: "Light Steel",
        logoPosition: "FRONT_CENTER",
        size: "L",
        createdAt: new Date("2024-10-29T04:23:46.819Z"),
        updatedAt: new Date("2024-10-29T04:23:46.819Z")
      }
    }
  ],
  priority: null,
  source: null
};

const TICKET = (uri: string, requestBody: { ticket?: CreateTicketInput }): unknown => ({
  url: `https://${ZENDESK_SUBDOMAIN}.zendesk.com/api/v2/tickets/2.json`,
  id: 2,
  via: { channel: "api" },
  created_at: "2024-07-30T19:36:14Z",
  updated_at: "2024-07-30T19:36:14Z",
  generated_timestamp: 0,
  type: null,
  subject: requestBody.ticket?.subject,
  raw_subject: requestBody.ticket?.subject,
  description: requestBody.ticket?.description,
  comment: requestBody.ticket?.comment,
  priority: null,
  status: "new",
  requester_id: 27836185376411,
  submitter_id: 27836185376411,
  group_id: 27836201395611,
  has_incidents: false,
  is_public: true,
  due_at: null,
  tags: requestBody.ticket?.tags,
  custom_status_id: 27836201351451,
  ticket_form_id: 27836215818779,
  brand_id: 27836201308955,
  allow_channelback: false,
  allow_attachments: true,
  from_messaging_channel: false
});

describe("Zendesk API", () => {
  beforeEach(() => {
    nock(`https://${ZENDESK_SUBDOMAIN}.zendesk.com`).post("/api/v2/tickets.json").
      reply(201, TICKET);
  });

  describe("createCorruptedDesignFileTicket", () => {
    it("should create a ticket", async () => {
      const logoUrl = "http://snapraiselogos.s3.us-west-1.amazonaws.com.test/MyFolder/UuwUwpbCgK.png";
      const netsuiteId = "34514";
      const netsuiteUrl = `https://4766534.app.netsuite.com/app/accounting/transactions/salesord.nl?id=${netsuiteId}&whence=`;

      const ticket_created = await Ticket.createCorruptedDesignFileTicket(ORDER, {
        fundraiser: FUNDRAISER,
        missingLogoUrls: [logoUrl],
        netsuiteId,
        netsuiteUrl
      });

      expect(ticket_created).toBeDefined();
      expect(ticket_created?.subject).toEqual(`Marco Order Missing Design Files | SO${netsuiteId}`);
      expect(ticket_created?.tags).toContain("missing_design_file");
      expect(ticket_created?.description?.includes(String(FUNDRAISER.id))).toBe(true);
      expect(ticket_created?.description?.includes(logoUrl)).toBe(true);
    });

    it("should create a ticket w/o fundraiser", async () => {
      const logoUrl = "http://snapraiselogos.s3.us-west-1.amazonaws.com.test/MyFolder/UuwUwpbCgK.png";
      const netsuiteId = "34514";
      const netsuiteUrl = `https://4766534.app.netsuite.com/app/accounting/transactions/salesord.nl?id=${netsuiteId}&whence=`;

      const ticket_created = await Ticket.createCorruptedDesignFileTicket(ORDER, {
        missingLogoUrls: [logoUrl],
        netsuiteId,
        netsuiteUrl
      });

      expect(ticket_created).toBeDefined();
      expect(ticket_created?.subject).toEqual(`Marco Order Missing Design Files | SO${netsuiteId}`);
      expect(ticket_created?.tags).toContain("missing_design_file");
      expect(ticket_created?.description?.includes(String(FUNDRAISER.id))).toBe(false);
      expect(ticket_created?.description?.includes(logoUrl)).toBe(true);
    });
  });

  describe("createFailedOrderHandlingTicket", () => {
    it("should create a ticket", async () => {
      const logoUrl = "http://snapraiselogos.s3.us-west-1.amazonaws.com.test/MyFolder/UuwUwpbCgK.png";
      const missingLogoUrls = Array(3).fill(logoUrl);
      const netsuiteId = "34514";
      const netsuiteUrl = `https://4766534.app.netsuite.com/app/accounting/transactions/salesord.nl?id=${netsuiteId}&whence=`;

      const ticket_created = await Ticket.createFailedOrderHandlingTicket(ORDER, {
        fundraiser: FUNDRAISER,
        missingLogoUrls,
        netsuiteId,
        netsuiteUrl
      });

      expect(ticket_created).toBeDefined();
      expect(ticket_created?.subject).toEqual(`Rejected Order | SO${netsuiteId}`);
      expect(ticket_created?.tags).toContain("rejected_marco_order");
      expect(ticket_created?.description?.includes(String(FUNDRAISER.id))).toBe(true);
      expect(ticket_created?.description?.includes(logoUrl)).toBe(true);
    });

    it("should create a ticket w/o fundraiser", async () => {
      const logoUrl = "http://snapraiselogos.s3.us-west-1.amazonaws.com.test/MyFolder/UuwUwpbCgK.png";
      const missingLogoUrls = Array(3).fill(logoUrl);
      const netsuiteId = "34514";
      const netsuiteUrl = `https://4766534.app.netsuite.com/app/accounting/transactions/salesord.nl?id=${netsuiteId}&whence=`;

      const ticket_created = await Ticket.createFailedOrderHandlingTicket(ORDER, { missingLogoUrls, netsuiteId, netsuiteUrl });

      expect(ticket_created).toBeDefined();
      expect(ticket_created?.subject).toEqual(`Rejected Order | SO${netsuiteId}`);
      expect(ticket_created?.tags).toContain("rejected_marco_order");
      expect(ticket_created?.description?.includes(String(FUNDRAISER.id))).toBe(false);
      expect(ticket_created?.description?.includes(logoUrl)).toBe(true);
    });
  });
});
