{"name": "zendesk-api", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/zendesk-api/src", "projectType": "library", "tags": [], "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/packages/zendesk-api", "main": "packages/zendesk-api/src/index.ts", "tsConfig": "packages/zendesk-api/tsconfig.lib.json", "assets": ["packages/zendesk-api/*.md"]}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"]}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "packages/zendesk-api/jest.config.ts"}}}}