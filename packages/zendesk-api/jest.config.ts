/* eslint-disable */
process.env.ZENDESK_USERNAME = "<EMAIL>";
process.env.ZENDESK_TOKEN = "01234567890ABCDEF";
process.env.ZENDESK_SUBDOMAIN = "example";

export default {
  displayName: 'zendesk-api',
  preset: '../../jest.preset.js',
  testEnvironment: 'node',
  transform: {
    '^.+\\.[tj]s$': ['ts-jest', { tsconfig: '<rootDir>/tsconfig.spec.json' }],
  },
  moduleFileExtensions: ['ts', 'js', 'html'],
  coverageDirectory: '../../coverage/packages/zendesk-api',
};
