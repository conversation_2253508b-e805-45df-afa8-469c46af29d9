/* eslint-disable capitalized-comments, no-inline-comments */
import { Ticket } from "node-zendesk/dist/types/clients/core/tickets";
import Sentry from "@store-monorepo/sentry";

import { CreateTicketInput, UpdateTicketInput } from "./types";
import client from "./zendesk";

import upsertRaiseLogoDesignTicket from "./tickets/raise-logo-design";
import createCorruptedDesignFileTicket from "./tickets/corrupted-design-file";
import createFailedOrderHandlingTicket from "./tickets/failed-order-handling";

const ZENDESK_SUBDOMAIN = process.env.ZENDESK_SUBDOMAIN || "";

const ticketUrl = (id: number) => `https://${ZENDESK_SUBDOMAIN}.zendesk.com/agent/tickets/${id}`;

const createTicket = async (ticket: CreateTicketInput): Promise<Ticket | undefined> => {
  try {
    const resp = await client.tickets.create({ ticket });
    return resp.result;
  } catch (error) {
    console.error(error);
    Sentry.captureException(error);
    return undefined;
  }
};

const updateTicket = async (id: number, ticket: UpdateTicketInput): Promise<Ticket | undefined> => {
  try {
    const resp = await client.tickets.update(id, { ticket });
    return resp.result;
  } catch (error) {
    console.error(error);
    Sentry.captureException(error);
    return undefined;
  }
};

const getTicket = async (id: number): Promise<Ticket | undefined> => {
  try {
    const resp = await client.tickets.show(id);
    return resp.result;
  } catch (error) {
    console.error(error);
    Sentry.captureException(error);
    return undefined;
  }
};

const getTickets = async (ids: number[]): Promise<Ticket[] | undefined> => {
  try {
    const resp = await client.tickets.showMany(ids) as unknown as {result: Ticket[]};
    return resp.result;
  } catch (error) {
    console.error(error);
    Sentry.captureException(error);
    return [];
  }
};

export { Ticket };

export default {
  ticketUrl,
  createTicket,
  updateTicket,
  getTicket,
  getTickets,
  createCorruptedDesignFileTicket,
  createFailedOrderHandlingTicket,
  upsertRaiseLogoDesignTicket
};
