import { createClient } from "node-zendesk";

const ZENDESK_USERNAME = process.env.ZENDESK_USERNAME || "";
const ZENDESK_TOKEN = process.env.ZENDESK_TOKEN || "";
const ZENDESK_SUBDOMAIN = process.env.ZENDESK_SUBDOMAIN || "";

const TicketGroup = {
  Warehouse: parseInt(process.env.ZENDESK_GROUP_WAREHOUSE || "0") || 0
};

const CustomFields: Record<string, number | undefined> = process.env.NODE_ENV === "production" ? {
  FundraiserLaunchDate: 6631226243995
}: {};

const zendesk = createClient({
  username: ZENDESK_USERNAME,
  token: ZENDESK_TOKEN,
  subdomain: ZENDESK_SUBDOMAIN
});

export { TicketGroup, CustomFields };

export default zendesk;
