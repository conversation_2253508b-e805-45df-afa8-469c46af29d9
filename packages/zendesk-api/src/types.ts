import { CustomField } from "node-zendesk/dist/types/clients/core/tickets";
import { Order as OrderInput, OrdersOnProducts, Product } from "@prisma/client";

type Order = OrderInput & { products: (OrdersOnProducts & { product: Product })[] };

type CreateTicketInput = {
  subject: string;
  description?: string;
  comment?: { body: string };
  tags?: string[];
  group_id?: number;
  assignee_email?: string;
  custom_fields?: Array<CustomField>;
}

type UpdateTicketInput = {
  subject?: string;
  description?: string;
  comment?: { body: string };
  tags?: string[];
  group_id?: number;
  assignee_email?: string;
  custom_fields?: Array<CustomField>;
}

type Fundraiser = {
  id: string;
  name?: string;
}

type CorruptedDesignFilePayload = {
  fundraiser?: Fundraiser;
  missingLogoUrls: string[];
  netsuiteId: string;
  netsuiteUrl: string;
}

type FailedOrderHandlingPayload = {
  fundraiser?: Fundraiser;
  missingLogoUrls: string[];
  netsuiteId: string;
  netsuiteUrl: string;
}

type UpdateTicketMode = "UPDATE_ALL" | "UPDATE_CUSTOM_FIELDS_ONLY";

export default {};

export type {
  CorruptedDesignFilePayload,
  CreateTicketInput,
  FailedOrderHandlingPayload,
  Fundraiser,
  Order,
  UpdateTicketInput,
  UpdateTicketMode,
};
