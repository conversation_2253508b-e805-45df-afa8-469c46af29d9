/* eslint-disable max-len, max-lines, complexity, unicorn/filename-case */
import Handlebars from "handlebars";
import { CustomField, Ticket } from "node-zendesk/dist/types/clients/core/tickets";
import { on as featureOn } from "@store-monorepo/splitio-client";

import Helper from "../helper";

import Repo, {
  StoreType,
  ZENDESK_TICKET_STATUS,
  ZendeskTicketType,
  LOGO_TYPE
} from "@store-monorepo/repo";

import Tickets from "../tickets";
import { CustomFields } from "../zendesk";
import { UpdateTicketMode } from "../types";

const raiseDesignTicketTemplate = Handlebars.compile(`
{{#if customLogo }}
{{customLogo}}
{{/if}}

{{#if pngs }}
**PNGs**
  {{#each pngs}}
  - {{this}}
  {{/each}}
{{/if}}

{{#if svgs }}
**SVGs**
  {{#each svgs}}
  - {{this}}
  {{/each}}
{{/if}}

{{#if logoNotes }}
**Custom Message from Rep** - {{logoNotes}}
{{/if}}

{{#if logoScript}}
**OTK Logo Text** - {{logoScript}}
{{/if}}

**Primary Color** - {{logoPrimaryColor}}

**Secondary Color** - {{logoSecondaryColor}}

**Store URL** - {{storeUrl}}

**Team Name** - {{name}}

**ORGs Team ID** - {{teamId}}

{{#if relatedTickets}}
**Closed Ticket(s)**
  {{#each relatedTickets}}
  - https://snap-raise.zendesk.com/agent/tickets/{{this}}
  {{/each}}
{{/if}}
`);

const LOGO_SUFFIX = "999";
const LOGO_PATTERNS = ["hat", "digital", "embroidery", "web header"];
const LOGO_SUPPORTED_EXTENSIONS = ["png", "jpg", "jpeg", "gif"];

const S3_BASE_URL = process.env.S3_BASE_URL || "http://snapraiselogos.s3.us-west-1.amazonaws.com/";

const ZENDESK_CLOSED_STATUSES: ZENDESK_TICKET_STATUS[] = [ZENDESK_TICKET_STATUS.CLOSED, ZENDESK_TICKET_STATUS.SOLVED];

const parseFileExtension = (fundraiserLogo?: string | null): string => {
  return LOGO_SUPPORTED_EXTENSIONS.find(ext => fundraiserLogo?.toLowerCase()?.endsWith(ext)) || "png";
};

const subject = (store: StoreType): string => {
  const titles: Array<[boolean, string]> = [
    [(!store.fanStore && store.incentiveStore), "Incentive Order"],
    [(store.logoType === LOGO_TYPE.TEAM), "Team Logo Request"],
    [(store.logoType === LOGO_TYPE.TEMPLATE), "Template Logo Request"],
  ] as const;

  const prefix: string = titles.find(([condition]) => condition)?.[1] || "Template Logo Request";

  return `${prefix}|${store.fundraiserId}`;
};

const logos = async (store: StoreType) => {
  const fundraiserId = (await Repo.Store.originalStoreWithLogo(store))?.fundraiserId;
  if (!fundraiserId) return {};

  if (store.logoType === LOGO_TYPE.TEAM) {
    const ext = parseFileExtension(store.fundraiserLogo);
    return {
      customLogo: `${S3_BASE_URL}PROD-PNG/${fundraiserId}-${LOGO_SUFFIX}.${ext}`
    };
  }
  return {
    pngs: LOGO_PATTERNS.map(pattern => `${S3_BASE_URL}PROD-PNG/${fundraiserId}_${pattern[0]}.png`),
    svgs: LOGO_PATTERNS.map(pattern => `${S3_BASE_URL}PROD-SVG/${fundraiserId}_${pattern[0]}.svg`)
  };
};

const description = async (store: StoreType, relatedTickets: number[]): Promise<string> => {
  return raiseDesignTicketTemplate({
    ...await logos(store),
    relatedTickets,
    logoNotes: store.logoNotes,
    logoScript: store.logoScript,
    logoPrimaryColor: store.logoPrimaryColor,
    logoSecondaryColor: store.logoSecondaryColor,
    storeUrl: store.storeUrl || "N/A",
    name: store.name,
    teamId: store.teamId
  }).trim();
};

const splitStateFields = async (
  store: StoreType,
  currentTicket: ZendeskTicketType | undefined,
  hasClosedTickets: boolean
) => {
  const tags = [
    ...(store.logoType === LOGO_TYPE.TEAM ? ["teamlogo"] : ["templatelogo"]),
    ...(currentTicket || (!currentTicket && hasClosedTickets) ? ["logo_change_update"] : []),
    ...((!store.fanStore && store.incentiveStore) ? ["incentive_order"] : [])
  ];

  return await featureOn("STOR-3996-store-creation-zendesk-tickets", { fundraiserId: store.fundraiserId as number })
    ? {
      tags: tags
    }
    : {
      assignee_email: "<EMAIL>"
    };
};

const upsertRaiseLogoDesignTicket = async (
  store: StoreType,
  tickets: ZendeskTicketType[],
  mode?: UpdateTicketMode
): Promise<Ticket | undefined> => {
  const openTicket = tickets?.
    find(ticket => !ZENDESK_CLOSED_STATUSES.includes(ticket.status));

  const relatedTickets = tickets?.
    filter(ticket => ZENDESK_CLOSED_STATUSES.includes(ticket.status))?.
    map(ticket => ticket.zendeskId);

  const split_state_fields = await splitStateFields(store, openTicket, !!relatedTickets?.length);

  const custom_fields = [
    { id: CustomFields.FundraiserLaunchDate, value: Helper.toDateString(store.startDate) }
  ].filter(({ id }) => !!id) as Array<CustomField>;

  if (mode === "UPDATE_CUSTOM_FIELDS_ONLY") {
    return openTicket
      ? await Tickets.updateTicket(openTicket.zendeskId, {
        custom_fields
      })
      : undefined;
  }

  return !openTicket
    ? await Tickets.createTicket({
      subject: subject(store),
      description: await description(store, relatedTickets),
      ...split_state_fields,
      custom_fields
    })
    : await Tickets.updateTicket(openTicket.zendeskId, {
      subject: subject(store),
      comment: { body: await description(store, relatedTickets) },
      ...split_state_fields,
      custom_fields
    });
};

export { parseFileExtension, subject };

export default upsertRaiseLogoDesignTicket;
