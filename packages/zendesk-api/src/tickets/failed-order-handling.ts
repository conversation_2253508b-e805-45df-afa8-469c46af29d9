import { Ticket } from "node-zendesk/dist/types/clients/core/tickets";
import Handlebars from "handlebars";

import { FailedOrderHandlingPayload, Order } from "../types";
import { TicketGroup } from "../zendesk";
import Tickets from "../tickets";

const failedOrderHandlingTemplate = Handlebars.compile(`
{{{netsuiteUrl}}}

{{#if vendor}}
**Vendor:** {{vendor}}
{{/if}}

{{#if fundraiser}}
**FundID:** {{fundraiser.id}}

**Campaign name:** {{fundraiser.name}}
{{/if}}

{{#if missingLogoUrls }}
**Missing URLs:**
  {{#each missingLogoUrls}}
  - {{this}}
  {{/each}}
{{/if}}
`);

const createFailedOrderHandlingTicket = async (order: Order, payload: FailedOrderHandlingPayload): Promise<Ticket | undefined> => {
  if(order.vendor === "STORE") {
    return Tickets.createTicket({
      subject: `Store Order | SO${payload.netsuiteId}`,
      description: failedOrderHandlingTemplate({ ...payload, vendor: order.vendor }),
      tags: ["rejected_marco_order"],
      group_id: TicketGroup.Warehouse
    });
  }

  return Tickets.createTicket({
    subject: `Rejected Order | SO${payload.netsuiteId}`,
    description: failedOrderHandlingTemplate(payload),
    tags: ["rejected_marco_order"],
    group_id: TicketGroup.Warehouse
  });
};

export default createFailedOrderHandlingTicket;
