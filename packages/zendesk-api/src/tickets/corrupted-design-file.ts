import Handlebars from "handlebars";
import { Ticket } from "node-zendesk/dist/types/clients/core/tickets";

import { CorruptedDesignFilePayload, Order } from "../types";
import { TicketGroup } from "../zendesk";
import Tickets from "../tickets";

const corruptedDesignFileTemplate = Handlebars.compile(`
{{ netsuiteUrl }}

{{#if fundraiser}}
The file is missing for {{fundraiser.id}}
{{/if}}

{{#if missingLogoUrls }}
**Missing URLs:**
  {{#each missingLogoUrls}}
  - {{this}}
  {{/each}}
{{/if}}
`);

const createCorruptedDesignFileTicket = async (order: Order, payload: CorruptedDesignFilePayload): Promise<Ticket | undefined> => {
  return Tickets.createTicket({
    subject: `Marco Order Missing Design Files | SO${payload.netsuiteId}`,
    description: corruptedDesignFileTemplate(payload),
    tags: ["missing_design_file"],
    group_id: TicketGroup.Warehouse
  });
};

export default createCorruptedDesignFileTicket;
