type ShippingProvider = "UPS" | "USPS" | "FedEx" | "OSM" | "DHL";

type Vendor = "MARCO" | "STORE" | "RAISE_MARCO" | "RAISE"
type Order = {
  id?: string;
  vendor: Vendor;
  carrier: ShippingProvider;
  shipTo: string;
  street: string;
  city: string;
  state: string;
  zipCode: string;
  packingSlipId: string;
  packingSlipTitle?: string;
  scheduleAt?: string;
  netsuiteId: string;
  products: Product[];
};

type Product = {
  name: string;
  size: string;
  sku: string;
  logo: string;
  logoPosition: string;
  receiverName?: string;
};

type OrderUpdate = {
  id: number;
  carrier: ShippingProvider;
  shipTo: string;
  street: string;
  city: string;
  state: string;
  zipCode: string;
};

// eslint-disable-next-line import/prefer-default-export
export type { Order, Product, OrderUpdate, ShippingProvider, Vendor };
