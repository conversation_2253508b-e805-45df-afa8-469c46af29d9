import <PERSON><PERSON><PERSON> from "@store-monorepo/marco-api";
import { OrderUpdate } from "./types";
export { type Order } from "./types";

const Adapter = {
  MARCO: MarcoAPI,
  STORE: MarcoAPI,
  RAISE_MARCO: MarcoAPI,
  RAISE: MarcoAPI
};

type SERVICE = keyof typeof Adapter;

interface Order {
  shipTo: string;
  street: string;
  zipCode: string;
  state: string;
  netsuiteId: string;
  city: string;
  products: Product[]
}

interface Product {
  receiverName: string;
  logo: string;
  product: {
    sku: string;
    name: string;
    logoPosition: string;
    size: string;
  }
}
const createOrder = async (service: SERVICE, params: Order) => {
  return await Adapter[service].createOrder(params);
};

const orderStatus = async (service: SERVICE, orderId: string) => {
  return await Adapter[service].orderStatus(orderId);
};

const updateOrder = async (service: SERVICE, params: OrderUpdate) => {
  return await Adapter[service].updateOrder(params);
};

const cancelOrder = async (service: SERVICE, orderId: string) => {
  return await Adapter[service].cancelOrder(orderId);
};

const stockCheck = async (service: SERVICE, sku: string) => {
  return await Adapter[service].stockCheck(sku);
};

export default {
  orderStatus,
  updateOrder,
  cancelOrder,
  stockCheck,
  createOrder
};
