import Marco<PERSON><PERSON> from "@store-monorepo/marco-api";
import { STATE } from "@prisma/client";
import orderModule from "../lib/fulfillment-adapter";
import { OrderUpdate } from "../lib/fulfillment-adapter/types";
import { ShippingProvider, Vendor } from "../../../../apps/store/src/api/graphql/types";
jest.mock("@store-monorepo/marco-api");

const mockOrder = (id?: string) => ({
  id,
  vendor: Vendor.Marco,
  carrier: ShippingProvider.Ups,
  shipTo: "John <PERSON>ckle",
  street: "12083 Dorcas Tunnel",
  zipCode: "51940-7250",
  state: STATE.AZ,
  city: "Beierton",
  packingSlipId: "cd6a6696-90ed-4424-a25a-947486229b83",
  packingSlipTitle: "ae52a6cf-95bb-4bd3-8204-f4b02446b304",
  netsuiteId: "082dc15c-f1e2-49ae-9dbc-a067933379fc",
  products: [{
    receiverName: "<PERSON>rkin",
    logo: "https://adept-perennial.com/",
    product: {
      sku: "833f730e-ecb1-4177-9540-2b2b8293473e",
      name: "distribution",
      logoPosition: "FRONT_CENTER",
      size: "M",
      netsuiteId: "977f0cd2-5e82-4332-9705-2a0c61f019ff"
    }
  }]
});
describe("Order functions", () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  test("createOrder calls MarcoAPI.createOrder", async () => {
    const payload = mockOrder();

    await orderModule.createOrder("MARCO", payload);
    expect(MarcoAPI.createOrder).toHaveBeenCalled();
  });

  test("orderStatus calls MarcoAPI.orderStatus", async () => {
    await orderModule.orderStatus("MARCO", "12345");
    expect(MarcoAPI.orderStatus).toHaveBeenCalled();
  });

  test("updateOrder calls MarcoAPI.updateOrder", async () => {
    const updateOrderParams: OrderUpdate = {
      id: 1245,
      carrier: "UPS",
      shipTo: "Test",
      street: "456 Elm St",
      city: "Anytown",
      state: "CA",
      zipCode: "67890"
    };
    await orderModule.updateOrder("MARCO", updateOrderParams);
    expect(MarcoAPI.updateOrder).toHaveBeenCalled();
  });

  test("cancelOrder calls MarcoAPI.cancelOrder", async () => {
    await orderModule.cancelOrder("MARCO", "12345");
    expect(MarcoAPI.cancelOrder).toHaveBeenCalled();
  });

  test("stockCheck calls MarcoAPI.stockCheck", async () => {
    await orderModule.stockCheck("MARCO", "12345");
    expect(MarcoAPI.stockCheck).toHaveBeenCalled();
  });
});
