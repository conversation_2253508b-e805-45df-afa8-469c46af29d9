{"name": "order-fulfillment", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/order-fulfillment/src", "projectType": "library", "tags": [], "targets": {"build": {"executor": "nx:run-commands", "options": {"command": "npx tsc -p packages/order-fulfillment/tsconfig.lib.json && npx resolve-tspaths -p packages/order-fulfillment/tsconfig.json"}, "outputs": ["{workspaceRoot}/dist/apps/store"]}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"]}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "packages/order-fulfillment/jest.config.ts"}}}}