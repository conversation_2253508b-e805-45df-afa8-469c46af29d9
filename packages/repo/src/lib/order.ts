/* eslint-disable id-denylist */
import { Prisma, Order, OrdersOnProducts, Product } from "@prisma/client";
import { JsonArray } from "@prisma/client/runtime/library";
import RepoAdapter from "./repo-adapter";

const findById = async (id: string, include = false) => RepoAdapter.order.findUnique({
  where: { id },
  ...(include ? { include: { products: { include: { product: true } } } } : {})
});

const findBy = async (where: Prisma.OrderWhereUniqueInput, include = false) => {
  return RepoAdapter.order.findUnique({
    where,
    ...(include ? { include: { products: { include: { product: true } } } } : {})
  });
};

const findFirst = async (where: Prisma.OrderWhereInput) => {
  return RepoAdapter.order.findFirst({
    where,
    include: { products: { include: { product: true } } }
  });
};

const findMany = async (where: Prisma.OrderWhereInput, include = false) => {
  return RepoAdapter.order.findMany({
    where,
    ...(include ? { include: { products: true } } : {})
  });
};

const create = async (data: Prisma.OrderCreateInput) => {
  return RepoAdapter.order.create({
    data,
    include: {
      products: {
        include: {
          product: true
        }
      }
    }
  });
};

const update = async (id: string, data: Prisma.OrderUpdateInput) => {
  return RepoAdapter.order.update({ data, where: { id } });
};

const updateWhere = async (where: Prisma.OrderWhereUniqueInput, data: Prisma.OrderUpdateInput) => {
  return RepoAdapter.order.update({ data, where });
};

const updateMany = async (where: Prisma.OrderWhereInput, data: Prisma.OrderUpdateInput) => {
  return RepoAdapter.order.updateMany({ data, where });
};

const deleteById = async (id: string) => RepoAdapter.order.delete({ where: { id } });

const flattenOrder = (order: Order & { products: (OrdersOnProducts & { product: Product })[] }) => {
  return {
    ...order,
    products: order.products.map(p => ({
      logo: p.logo,
      printAttributes: p.printAttributes as JsonArray,
      receiverName: p.receiverName,
      sku: p.product.sku,
      name: p.product.logoPosition,
      size: p.product.size,
      netsuiteId: p.product.netsuiteId
    }))
  };
};

const orderModule = { findById, findBy, findFirst, create, update, deleteById, updateWhere, flattenOrder, updateMany, findMany };
export default orderModule;
