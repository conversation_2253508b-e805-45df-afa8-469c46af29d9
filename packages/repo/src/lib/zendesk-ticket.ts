/* eslint-disable id-denylist, import/group-exports, max-lines-per-function, max-lines, functional/no-loop-statements, functional/no-let */
import { ZendeskTicket, ZENDESK_TICKET_STATUS } from "@prisma/client";
import RepoAdapter from "./repo-adapter";

export type NewZendeskTicket = Omit<ZendeskTicket, "id" | "createdAt" | "updatedAt">;

export type ZendeskTicketType = ZendeskTicket;

const toZendeskStatus = (status?: string): ZENDESK_TICKET_STATUS => {
  return (status?.toUpperCase() || "NEW") as ZENDESK_TICKET_STATUS;
};

const fromStore = async (storeId: string) => {
  return await RepoAdapter.zendeskTicket.findMany({
    where: { storeId },
  });
};

const upsert = async (attrs: NewZendeskTicket) => {
  return await RepoAdapter.zendeskTicket.upsert({
    where: { zendeskId: attrs.zendeskId },
    update: { ...attrs, updatedAt: new Date() },
    create: { ...attrs },
  });
};

const updateStatuses = async (updates: Array<[number, ZENDESK_TICKET_STATUS]>) => {
  return await RepoAdapter.$transaction(updates.map(
    ([zendeskId, status]) => RepoAdapter.zendeskTicket.update({
      where: { zendeskId },
      data: { status, updatedAt: new Date() },
    }))
  );
};

export { ZENDESK_TICKET_STATUS };

export default { upsert, fromStore, updateStatuses, toZendeskStatus };
