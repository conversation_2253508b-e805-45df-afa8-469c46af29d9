/* eslint-disable complexity, id-denylist, import/group-exports, max-lines-per-function, max-len, max-lines, functional/no-loop-statements, functional/no-let, functional/immutable-data, etc/no-commented-out-code */
import { Store, LOGO_TYPE } from "@prisma/client";
import { on as featureOn } from "@store-monorepo/splitio-client";
import { Types } from "@store-monorepo/graphql";

import RepoAdapter from "./repo-adapter";

const LOGO_BUCKET = "https://snapraiselogos.s3.amazonaws.com";

export type NewStore = Omit<Store,
  "id" | "createdAt" | "updatedAt" | "logoDigitalVerifiedAt" | "logoEmbroideryVerifiedAt" |
  "logoHatVerifiedAt" | "logoWebHeaderVerifiedAt" | "magentoStoreId" | "magentoStoreCode" |
  "magentoManagerEmail" | "builtAt" | "deactivatedAt" | "schoolId" | "schoolName"
>;

export type StoreType = Store;

export interface RaiseFundraiser {
  account_manager_email: string
  account_manager_udid: string
  account_manager: string
  activity_type: string
  city: string
  created_at: string
  email_logo: string
  end_date: string
  fan_store: boolean
  inc_store: boolean
  group_id: number
  group_name: string
  store_url: string
  has_participants: boolean
  fundraiser_logo_id: number
  fundraiser_logo: string
  fundraiser_primary_color: string
  fundraiser_secondary_color: string
  group_leader_email: string
  group_leader_udid: string
  group_leader: string
  id: number
  logo_digital_url: string
  logo_embroidery_url: string
  logo_hat_url: string
  logo_web_header_url: string
  logo_primary_color: string
  logo_script: string
  logo_secondary_color: string
  logo_updated_at: string
  logo: string
  name: string
  organization_id: number
  organization_legal_name: string
  organization_name: string
  previous_logo: boolean
  raise_entity_id: number
  sales_rep_email: string
  sales_rep_udid: string
  sales_rep: string
  slug: string
  start_date: string
  state: string
  status: string
  team_id: string
  team_size: number
  updated_at: string
  zip: string
  logo_notes: string
  partner_id: number
}

const findByDomain = async (domain: string) => {
  return await RepoAdapter.store.findFirst({ where: { storeUrl: { startsWith: `https://${domain}.` } } });
};

const findStoreByFundraiserId = async (fundraiserId: number) => {
  return await RepoAdapter.store.findFirst({ where: { fundraiserId } });
};

const create = async (attrs: NewStore) => {
  return await RepoAdapter.store.create({
    data: attrs
  });
};

const computeNewAndExistingStores = (attrs: NewStore, existingStore?: Store): NewStore => {
  // A. It's a new store. We need to compute url and create it
  if (!existingStore) {
    return { ...attrs, storeUrl: computeStoreUrl(attrs) };
  }

  // B. It's not a new store. We need to merge incoming and existing data
  // B0. We already have a store but url is null in existing and incoming data
  if (!attrs.storeUrl && !existingStore.storeUrl) {
    return { ...attrs, storeUrl: computeStoreUrl(attrs) };
  }

  // B1. We already built store but Raise doesn't know the url yet
  if (!attrs.storeUrl && existingStore.storeUrl) {
    return { ...attrs, storeUrl: existingStore.storeUrl };
  }

  // B2. We already built store but Raise has another url
  // Why? How to determinate the correct url? Try to fetch both?
  // if (attrs.storeUrl && existingStore.storeUrl && attrs.storeUrl !== existingStore.storeUrl && existingStore.builtAt) {
  //   return { ...attrs, storeUrl: existingStore.storeUrl };
  // }

  // BN. Otherwise
  return attrs;
};

const upsert = async (attrs: NewStore, existingStore?: Store) => {
  if (await featureOn("STOR-4133-use-existing-store-in-upseting")) {
    const data = computeNewAndExistingStores(attrs, existingStore);

    return await RepoAdapter.store.upsert({
      where: { fundraiserId: attrs.fundraiserId! },
      update: { ...data, updatedAt: new Date(Date.now()) },
      create: { ...data }
    });
  }

  return await RepoAdapter.store.upsert({
    where: { fundraiserId: attrs.fundraiserId! },
    update: { ...attrs, storeUrl: computeStoreUrl(attrs), updatedAt: new Date(Date.now()) },
    create: { ...attrs, storeUrl: computeStoreUrl(attrs) }
  });
};

const computeLogoType = (logo: string): LOGO_TYPE => {
  return (!isNaN(parseInt(logo)) && parseInt(logo) >= 50 && parseInt(logo) <= 82)
    ? LOGO_TYPE.TEMPLATE
    : LOGO_TYPE.TEAM;
};

const fromRaise = (raiseFundraiser: RaiseFundraiser): NewStore => {
  return {
    fundraiserId: parseNumber(raiseFundraiser.id),
    fundraiserEntityId: parseNumber(raiseFundraiser.raise_entity_id),
    activityType: raiseFundraiser.activity_type,
    startDate: parseDate(raiseFundraiser.start_date),
    endDate: parseDate(raiseFundraiser.end_date),
    city: raiseFundraiser.city,
    state: raiseFundraiser.state || "n/a",
    zip: raiseFundraiser.zip,
    slug: raiseFundraiser.slug,
    status: raiseFundraiser.status,
    teamSize: parseNumber(raiseFundraiser.team_size),
    name: raiseFundraiser.name,
    incentiveStore: parseBoolean(raiseFundraiser.inc_store),
    fanStore: parseBoolean(raiseFundraiser.fan_store),
    storeUrl: raiseFundraiser.store_url,
    hasParticipants: parseBoolean(raiseFundraiser.has_participants),
    organizationId: parseNumber(raiseFundraiser.organization_id),
    organizationLegalName: raiseFundraiser.organization_legal_name,
    organizationName: raiseFundraiser.organization_name,
    salesRep: raiseFundraiser.sales_rep,
    salesRepEmail: raiseFundraiser.sales_rep_email,
    salesRepUDID: raiseFundraiser.sales_rep_udid,
    accountManager: raiseFundraiser.account_manager,
    accountManagerEmail: raiseFundraiser.account_manager_email,
    accountManagerUDID: raiseFundraiser.account_manager_udid,
    groupLeader: raiseFundraiser.group_leader,
    groupLeaderEmail: raiseFundraiser.group_leader_email,
    groupLeaderUDID: raiseFundraiser.group_leader_udid,
    fundraiserPrimaryColor: raiseFundraiser.fundraiser_primary_color,
    fundraiserSecondaryColor: raiseFundraiser.fundraiser_secondary_color,
    logoPrimaryColor: raiseFundraiser.logo_primary_color,
    logoSecondaryColor: raiseFundraiser.logo_secondary_color,
    logoDigitalUrl: raiseFundraiser.logo_digital_url,
    logoEmbroideryUrl: raiseFundraiser.logo_embroidery_url,
    logoHatUrl: raiseFundraiser.logo_hat_url,
    logoWebHeaderUrl: raiseFundraiser.logo_web_header_url,
    logo: raiseFundraiser.logo,
    previousLogo: raiseFundraiser.previous_logo || false,
    logoScript: raiseFundraiser.logo_script,
    emailLogo: raiseFundraiser.email_logo,
    fundraiserLogo: raiseFundraiser.fundraiser_logo,
    fundraiserLogoId: parseNumber(raiseFundraiser.fundraiser_logo_id),
    raiseCreatedAt: parseDate(raiseFundraiser.created_at),
    raiseUpdatedAt: parseDate(raiseFundraiser.updated_at),
    logoUpdatedAt: parseDate(raiseFundraiser.logo_updated_at),
    teamId: raiseFundraiser.team_id,
    groupId: parseNumber(raiseFundraiser.group_id),
    groupName: raiseFundraiser.group_name,
    logoType: computeLogoType(raiseFundraiser.logo),
    logoNotes: raiseFundraiser.logo_notes,
    pointsPercentage: 10,
    storeCode: null,
    partnerId: parseNumber(raiseFundraiser.partner_id),
  };
};

const parseBoolean = (value: string | null | boolean): boolean => {
  return ["true", "1"].includes(`${value}`.toLowerCase());
};

const parseNumber = (value: number | string | null): number | null => {
  if (typeof value === "number" && Number.isInteger(value)) return value;
  if (!value) return null;
  const number = parseInt(`${value}`, 10);
  return isNaN(number) ? null : number;
};

const parseDate = (dateString: string | null) => {
  if (!dateString || isNaN(Date.parse(dateString))) return new Date(0);
  return new Date(dateString);
};

const computeStoreUrl = (attrs: NewStore): string | null => {
  if (attrs.storeUrl) return attrs.storeUrl;
  if (attrs.fundraiserEntityId) return `https://${attrs.fundraiserEntityId}.snap.store`;
  return null;
};

const toPrinterLogo = (url: string | null) => `${url}`.replace("PROD-SVG/", "PrinterLogos/").replace(".svg", ".png");

const expectedLogoUrlFor = (type: string, store: Store): string => {
  const logoUrlKey = `logo${type.charAt(0).toUpperCase()}${type.slice(1)}Url` as keyof LogoKeys;
  if (store[logoUrlKey]) return store[logoUrlKey] as string;
  return `${LOGO_BUCKET}/PROD-SVG/${store.fundraiserId || "null-logo"}_${type[0]}.svg`;
};

interface LogoVerifiedAtKeys {
  logoDigitalVerifiedAt?: Date | null;
  logoEmbroideryVerifiedAt?: Date | null;
  logoHatVerifiedAt?: Date | null;
  logoWebHeaderVerifiedAt?: Date | null;
}

interface LogoURLKeys {
  logoDigitalUrl?: string;
  logoEmbroideryUrl?: string;
  logoHatUrl?: string;
  logoWebHeaderUrl?: string;
}

type LogoKeys = LogoVerifiedAtKeys & LogoURLKeys;

type LogoVerificationResult = Promise<[boolean, StoreType, LogoKeys | null]>;

const expectedLogos = (store: Store): LogoKeys => {
  return {
    logoDigitalUrl: expectedLogoUrlFor("digital", store),
    logoEmbroideryUrl: expectedLogoUrlFor("embroidery", store),
    logoHatUrl: expectedLogoUrlFor("hat", store),
    logoWebHeaderUrl: expectedLogoUrlFor("webHeader", store)
  };
};

const fetchLogo = async (url: string | null) => {
  if (!url || !url.length) return { ok: false };
  try {
    return await fetch(url);
  } catch {
    return { ok: false };
  }
};

const verifyLogos = async (store: Store): Promise<LogoVerificationResult> => {
  const logoUrls = expectedLogos(store);
  for (const [name, url] of Object.entries(logoUrls)) {
    const verificationDate = await verifyLogo(store.id, toPrinterLogo(url));
    const verifiedAtKey = `${name.replace("Url", "VerifiedAt")}` as keyof LogoVerifiedAtKeys;
    logoUrls[verifiedAtKey] = verificationDate;
    logoUrls[name as keyof LogoURLKeys] = url;
  }
  const updatedStore = await RepoAdapter.store.update({ where: { id: store.id }, data: logoUrls });
  const verified = Object.entries(logoUrls).every(([k, v]) => !k.includes("Verified") || v !== null);
  return [verified, updatedStore, logoUrls];
};

const verifyLogo = async (id: string, url: string | null) => {
  const response = await fetchLogo(url);
  if (!response.ok) return null;
  return new Date();
};

const originalStore = async (store: Store) => {
  return await RepoAdapter.store.findFirst({
    where: { teamId: store.teamId, id: { not: store.id }, storeUrl: { not: null }, builtAt: { not: null } },
    orderBy: { fundraiserId: "asc" }
  });
};

const originalStoreWithLogo = async (store: Store) => {
  if (!store.previousLogo) return store;

  return RepoAdapter.store.findFirst({
    where: { teamId: store.teamId, previousLogo: false, id: { not: store.id } },
    orderBy: { fundraiserId: "desc" }
  });
};

const updateBuiltStore = async (store: Store, previousStore?: Store) => {
  const updateFields = previousStore ? previousStore : store;
  return await RepoAdapter.store.update({
    where: { id: store.id! },
    data: {
      storeUrl: updateFields.storeUrl,
      magentoStoreId: updateFields.magentoStoreId,
      magentoStoreCode: updateFields.magentoStoreCode,
      magentoManagerEmail: updateFields.magentoManagerEmail,
      builtAt: new Date()
    }
  });
};

const updateStoreSchool = async(store: Store, school: Types.Org) => {
  return await RepoAdapter.store.update({
    where: { id: store.id! },
    data: {
      schoolId: school.id,
      schoolName: school.name
    }
  });
};

export default {
  count: RepoAdapter.store.count,
  create,
  findByDomain,
  findMany: RepoAdapter.store.findMany,
  findStoreByFundraiserId,
  fromRaise,
  originalStore,
  originalStoreWithLogo,
  updateBuiltStore,
  updateStoreSchool,
  updateMany: RepoAdapter.store.updateMany,
  upsert,
  verifyLogos,
};
