import { Prisma } from "@prisma/client";
import RepoAdapter from "./repo-adapter";

const findById = async (id: string) => RepoAdapter.product.findUnique({ where: { id } });

const findMany = async (where: Prisma.ProductWhereInput) => RepoAdapter.product.findMany({ where });

const create = async (data: Prisma.ProductCreateInput) => await RepoAdapter.product.create({ data });

const update = async (id: string, data: Prisma.ProductUpdateInput) => {
  return RepoAdapter.product.update({ data, where: { id } });
};

const updateWhere = async (where: Prisma.ProductWhereUniqueInput, data: Prisma.OrderUpdateInput) => {
  return RepoAdapter.product.update({ data, where });
};

const deleteById = async (id: string) => RepoAdapter.product.delete({ where: { id } });


const orderModule = { findById, findMany, create, update, deleteById, updateWhere };
export default orderModule;
