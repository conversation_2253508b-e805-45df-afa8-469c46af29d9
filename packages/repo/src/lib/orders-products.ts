/* eslint-disable id-denylist */
import { Prisma } from "@prisma/client";
import RepoAdapter from "./repo-adapter";

const findById = async (id: string) => RepoAdapter.ordersOnProducts.findUnique({ where: { id } });

const findBy = async (where: Prisma.OrdersOnProductsWhereInput) => {
  return RepoAdapter.ordersOnProducts.findMany({ where, include: { product: true } });
};

const create = async (data: Prisma.OrdersOnProductsCreateInput) => await RepoAdapter.ordersOnProducts.create({ data });

const update = async (id: string, data: Prisma.OrdersOnProductsUpdateInput) => {
  return RepoAdapter.ordersOnProducts.update({ data, where: { id } });
};

const updateWhere = async (where: Prisma.OrdersOnProductsWhereUniqueInput, data: Prisma.OrdersOnProductsUpdateInput) => {
  return RepoAdapter.ordersOnProducts.update({ data, where });
};

const deleteById = async (id: string) => RepoAdapter.ordersOnProducts.delete({ where: { id } });

const orderModule = { findById, findBy, create, update, deleteById, updateWhere };
export default orderModule;
