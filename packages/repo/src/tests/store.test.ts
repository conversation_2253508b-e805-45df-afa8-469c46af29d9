/* eslint-disable max-lines, max-lines-per-function */
import { Store } from "@prisma/client";

import StoreRepo, { NewStore } from "../lib/store";
import RepoAdapter from "../lib/repo-adapter";

const mockStore = (update: Partial<Store>): Store => ({
  id: "store_64b...64",
  createdAt: new Date("2025-03-19T18:48:12.901Z"),
  updatedAt: new Date("2025-03-26T15:35:18.086Z"),
  logoDigitalVerifiedAt: null,
  logoEmbroideryVerifiedAt: null,
  logoHatVerifiedAt: null,
  logoWebHeaderVerifiedAt: null,
  magentoStoreId: null,
  magentoStoreCode: null,
  magentoManagerEmail: null,
  builtAt: null,
  deactivatedAt: null,
  ...mockNewAttrs(update)
});

const mockNewAttrs = (update: Partial<Store>): NewStore => ({
  fundraiserId: 111111,
  fundraiserEntityId: 222222,
  activityType: "other",
  startDate: new Date("2025-03-28T00:00:00.000Z"),
  endDate: new Date("2025-04-25T00:00:00.000Z"),
  city: "Smyrna",
  state: "georgia",
  zip: "30080",
  slug: "impact-prom-donation-drive-2025",
  status: "deleted",
  teamSize: 60,
  name: "Test 2025",
  incentiveStore: true,
  fanStore: true,
  storeUrl: null,
  hasParticipants: true,
  organizationId: 11111,
  organizationLegalName: "Ca...ol",
  organizationName: "Cam...ool",
  salesRep: "LaDa...d",
  salesRepEmail: "<EMAIL>",
  salesRepUDID: "udu_clxf...i1",
  accountManager: null,
  accountManagerEmail: null,
  accountManagerUDID: null,
  groupLeader: "Sa...os",
  groupLeaderEmail: "<EMAIL>",
  groupLeaderUDID: "udu_cm8g9...d4pr",
  fundraiserPrimaryColor: "#0504AA",
  fundraiserSecondaryColor: "#fff",
  logoPrimaryColor: "#6DCFF6|#3B7BB0",
  logoSecondaryColor: "#FFFFFF|#E4E8FF",
  logoDigitalUrl: null,
  logoEmbroideryUrl: null,
  logoHatUrl: null,
  logoWebHeaderUrl: null,
  logo: "Custom Logo",
  previousLogo: false,
  logoScript: "",
  emailLogo: null,
  fundraiserLogo: "*.jpg",
  fundraiserLogoId: 2177647,
  raiseCreatedAt: new Date("2025-03-19T18:48:07.000Z"),
  raiseUpdatedAt: new Date("2025-03-26T15:35:17.000Z"),
  logoUpdatedAt: new Date("2025-03-19T18:49:20.000Z"),
  teamId: "team_27d1e...a8c9",
  groupId: 141250,
  groupName: "Imp...om ",
  logoType: "TEAM",
  logoNotes: "",
  pointsPercentage: 10,
  storeCode: null,
  partnerId: null,
  ...update
});

const featureOn = jest.fn();

jest.mock("@prisma/client");

jest.mock("@store-monorepo/splitio-client", () => {
  return {
    on: jest.fn().mockImplementation(() => featureOn())
  };
});

jest.mock("../lib/repo-adapter", () => {
  const mockedUpsert = jest.fn();
  return {
    store: {
      upsert: mockedUpsert
    }
  };
});


describe("StoreRepo", () => {
  const now = Date.now();

  beforeEach(() => {
    Date.now = jest.fn(() => now);
  });

  describe("upsert", () => {
    it("should use a computed url [OFF]", async () => {
      const attrs = mockNewAttrs({ storeUrl: null });

      const create = { ...attrs, storeUrl: `https://${attrs.fundraiserEntityId}.snap.store` };

      featureOn.mockResolvedValue(false);
      await StoreRepo.upsert(attrs);

      expect(RepoAdapter.store.upsert).toHaveBeenCalledWith({
        where: { fundraiserId: attrs.fundraiserId! },
        update: { ...create, updatedAt: new Date(now) },
        create
      });
    });

    it("should use a incoming url [OFF]", async () => {
      const attrs = mockNewAttrs({ storeUrl: "https://cool.snap.store" });

      const create = { ...attrs, storeUrl: attrs.storeUrl };

      featureOn.mockResolvedValue(false);
      await StoreRepo.upsert(attrs);

      expect(RepoAdapter.store.upsert).toHaveBeenCalledWith({
        where: { fundraiserId: attrs.fundraiserId! },
        update: { ...create, updatedAt: new Date(now) },
        create
      });
    });

    it("should create a new store with computed url [ON]", async () => {
      const attrs = mockNewAttrs({ storeUrl: null });

      const create = { ...attrs, storeUrl: `https://${attrs.fundraiserEntityId}.snap.store` };

      featureOn.mockResolvedValue(true);
      await StoreRepo.upsert(attrs);

      expect(RepoAdapter.store.upsert).toHaveBeenCalledWith({
        where: { fundraiserId: attrs.fundraiserId! },
        update: { ...create, updatedAt: new Date(now) },
        create
      });
    });

    it("should create a new store with passed url [ON]", async () => {
      const passedUrl = "https://191919.snap.store";
      const attrs = mockNewAttrs({ storeUrl: passedUrl });

      const create = { ...attrs, storeUrl: passedUrl };

      featureOn.mockResolvedValue(true);
      await StoreRepo.upsert(attrs);

      expect(RepoAdapter.store.upsert).toHaveBeenCalledWith({
        where: { fundraiserId: attrs.fundraiserId! },
        update: { ...create, updatedAt: new Date(now) },
        create
      });
    });

    it("should update an existing store with computed url [ON]", async () => {
      const attrs = mockNewAttrs({ storeUrl: null });
      const existingStore = mockStore({ storeUrl: null });

      const create = { ...attrs, storeUrl: `https://${attrs.fundraiserEntityId}.snap.store` };

      featureOn.mockResolvedValue(true);
      await StoreRepo.upsert(attrs, existingStore);

      expect(RepoAdapter.store.upsert).toHaveBeenCalledWith({
        where: { fundraiserId: attrs.fundraiserId! },
        update: { ...create, updatedAt: new Date(now) },
        create
      });
    });

    it("should not update an existing store with null url [ON]", async () => {
      const attrs = mockNewAttrs({ storeUrl: null });
      const existingStore = mockStore({ storeUrl: "https://existing.snap.store" });

      const create = { ...attrs, storeUrl: existingStore.storeUrl };

      featureOn.mockResolvedValue(true);
      await StoreRepo.upsert(attrs, existingStore);

      expect(RepoAdapter.store.upsert).toHaveBeenCalledWith({
        where: { fundraiserId: attrs.fundraiserId! },
        update: { ...create, updatedAt: new Date(now) },
        create
      });
    });

    it("should reuse incoming url even if url already exists [ON] ?", async () => {
      const attrs = mockNewAttrs({ storeUrl: "https://incoming.snap.store" });
      const existingStore = mockStore({ storeUrl: "https://existing.snap.store" });

      const create = { ...attrs, storeUrl: "https://incoming.snap.store" };

      featureOn.mockResolvedValue(true);
      await StoreRepo.upsert(attrs, existingStore);

      expect(RepoAdapter.store.upsert).toHaveBeenCalledWith({
        where: { fundraiserId: attrs.fundraiserId! },
        update: { ...create, updatedAt: new Date(now) },
        create
      });
    });
  });
});
