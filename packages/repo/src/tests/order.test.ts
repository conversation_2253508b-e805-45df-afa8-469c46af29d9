import { Prisma, VENDOR, STAT<PERSON>, CARRIER } from "@prisma/client";
import OrderRepo from "../lib/order";
import RepoAdapter from "../lib/repo-adapter";

const mockOrder = (id?: string): Prisma.OrderCreateInput | Prisma.OrderUpdateInput => ({
  id,
  vendor: VENDOR.MARCO,
  carrier: CARRIER.UPS,
  shipTo: "John Hickle",
  street: "12083 Dorcas Tunnel",
  zipCode: "51940-7250",
  state: STATE.AK,
  city: "Beierton",
  packingSlipId: "77ac1832-b36f-409f-b86b-0c1c54f64db5",
  packingSlipTitle: "981dc9b8-8268-44de-8dd8-92ef62592987",
  netsuiteId: "d83ea82c-4cd3-4398-8a79-2f83105f2117"
});

jest.mock("@prisma/client");

// Since you're mocking @prisma/client, you need to manually set up the mock for RepoAdapter.order
// Assuming RepoAdapter.order directly uses Prisma client's order model
jest.mock("../lib/repo-adapter", () => {
  const mockedFindUnique = jest.fn();
  const mockedCreate = jest.fn();
  const mockedUpdate = jest.fn();
  const mockedDelete = jest.fn();
  return {
    order: {
      findUnique: mockedFindUnique,
      create: mockedCreate,
      update: mockedUpdate,
      delete: mockedDelete
      // Add other methods as needed
    }
  };
});

describe("OrderRepo", () => {
  describe("findById", () => {
    it("finds a unique order by id", async () => {
      const id = "e5ffc22e-8b45-4ec8-9396-e1ea40b259f4";

      await OrderRepo.findById(id);
      expect(RepoAdapter.order.findUnique).toHaveBeenCalledWith({ where: { id } });
    });
  });

  describe("create", () => {
    it("creates an order", async () => {
      const order = mockOrder();

      await OrderRepo.create(order as Prisma.OrderCreateInput);
      expect(RepoAdapter.order.create).toHaveBeenCalledWith({ data: order, include: { products: { include: { product: true } } } });
    });
  });

  describe("update", () => {
    it("updates an order by id", async () => {
      const id = "b3e9a070-d5dc-446a-9387-f88fec28a52e";
      const order = mockOrder(id);

      await OrderRepo.update(id, order as Prisma.OrderUpdateInput);
      expect(RepoAdapter.order.update).toHaveBeenCalledWith({ data: order, where: { id } });
    });
  });

  describe("updateWhere", () => {
    it("updates an order with the supplied where clause", async () => {
      const order = mockOrder();

      await OrderRepo.updateWhere({ netsuiteId: order.netsuiteId } as Prisma.OrderWhereUniqueInput, order as Prisma.OrderUpdateInput);
      expect(RepoAdapter.order.update).toHaveBeenCalledWith({ data: order, where: { netsuiteId: order.netsuiteId } });
    });
  });

  describe("deleteById", () => {
    it("finds a unique order by id", async () => {
      const id = "0f95cb5a-f54a-4ff1-b2aa-8a1bac9cf387";

      await OrderRepo.deleteById(id);
      expect(RepoAdapter.order.delete).toHaveBeenCalledWith({ where: { id } });
    });
  });
});
