import { LOGO_TYPE, VENDOR } from "@prisma/client";
import Order from "./lib/order";
import OrdersOnProducts from "./lib/orders-products";
import Product from "./lib/product";
import Store, { RaiseFundraiser, NewStore, StoreType } from "./lib/store";
import DeadLetterOrder, { NewDeadLetterOrder } from "./lib/dead-letter-order";
import ZendeskTicket, { NewZendeskTicket, ZENDESK_TICKET_STATUS, ZendeskTicketType } from "./lib/zendesk-ticket";
import RepoAdapter from "./lib/repo-adapter";

export {
  RaiseFundraiser,
  NewStore,
  NewDeadLetterOrder,
  StoreType,
  NewZendeskTicket,
  ZENDESK_TICKET_STATUS,
  LOGO_TYPE,
  ZendeskTicketType,
  VENDOR,
  RepoAdapter as prisma
};

export default {
  Order,
  OrdersOnProducts,
  Product,
  Store,
  DeadLetterOrder,
  ZendeskTicket
} as {
  Order: typeof Order;
  OrdersOnProducts: typeof OrdersOnProducts;
  Product: typeof Product;
  Store: typeof Store;
  DeadLetterOrder: typeof DeadLetterOrder;
  ZendeskTicket: typeof ZendeskTicket;
};
