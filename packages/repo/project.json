{"name": "repo", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/repo/src", "projectType": "library", "tags": [], "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/packages/repo", "main": "packages/repo/src/index.ts", "tsConfig": "packages/repo/tsconfig.lib.json", "assets": ["packages/repo/*.md"]}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"]}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "packages/repo/jest.config.ts"}}}}