/* eslint-disable import/max-dependencies */
import createOrder from "./endpoint/create-order.mutation";
import donorsDonationStats from "./endpoint/donors-donation-stats.query";
import donorsFundraiserPayables from "./endpoint/donors-fundraiser-payables.query";
import marcoWebhook from "./endpoint/marco-webhook.mutation";
import me from "./endpoint/me.query";
import netsuiteWebhook from "./endpoint/netsuite-webhook.mutation";
import orgId from "./endpoint/org-id.query";
import orgPayable from "./endpoint/org-payable.query";
import publicFundraiserData from "./endpoint/public-fundraiser-data.query";
import restartOrderWorkflow from "./endpoint/restart-order-workflow.mutation";

import easypostWebhook from "./endpoint/easypost-webhook.mutation";
import * as Types from "./types";

export { Types };
export default {
  createOrder,
  donorsDonationStats,
  donorsFundraiserPayables,
  marcoWebhook,
  me,
  netsuiteWebhook,
  orgId,
  orgPayable,
  publicFundraiserData,
  restartOrderWorkflow,
  easypostWebhook,
};
