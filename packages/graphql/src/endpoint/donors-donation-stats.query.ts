import { gql } from "graphql-request";

import request from "../request";
import type { DonorsDonationStatsResponse } from "../types";

const DonorsDonationStatsDocument = gql`
  query DonorsDonationStats($fundraiserId: ID!) {
    donorsDonationStats(where: {
      fundraiserId: $fundraiserId
    }) {
      count
      totalAmountCents
    }
  }`;

const donorsDonationStats = async (fundraiserId: string): Promise<DonorsDonationStatsResponse | undefined> => {
  const resp = await request<{donorsDonationStats: DonorsDonationStatsResponse}>(DonorsDonationStatsDocument, { fundraiserId });
  return ("donorsDonationStats" in resp) ? resp.donorsDonationStats : undefined;
};

export default donorsDonationStats;
