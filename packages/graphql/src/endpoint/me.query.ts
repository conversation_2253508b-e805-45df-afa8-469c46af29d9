import { gql } from "graphql-request";

import request from "../request";
import { UserWithPermissions } from "../types";

const MeDocument = gql`
  query Me {
    me {
      apps
      email
      firstName
      id
      lastName
      permissions
    }
  }
`;

const me = async (token?: string): Promise<UserWithPermissions | undefined> => {
  const resp = await request<{ me: UserWithPermissions }>(
    MeDocument,
    {},
    token ? { Authorization: `Bearer ${token}` } : {}
  );

  return ("me" in resp) ? resp.me : undefined;
};

export default me;
