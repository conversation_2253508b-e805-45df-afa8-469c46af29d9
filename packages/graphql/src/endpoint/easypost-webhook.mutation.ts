import { gql } from "graphql-request";

import request from "../request";
import type { EasypostEventInput, EasypostWebhookResponse } from "../types";

const EasypostWebhookDocument = gql`
  mutation EasypostWebhook($status: EasypostEventInput!) {
    easypostWebhook(status: $status) {
      success
      error
    }
  }
`;

const easypostWebhook = async (status: EasypostEventInput): Promise<EasypostWebhookResponse> => {
  const resp = await request<{ easypostWebhook: EasypostWebhookResponse }>(EasypostWebhookDocument, { status });
  return ("easypostWebhook" in resp)
    ? resp.easypostWebhook
    : { success: false, error: resp.error?.message };
};

export default easypostWebhook;
