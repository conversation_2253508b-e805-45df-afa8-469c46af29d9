import { gql } from "graphql-request";

import request from "../request";
import type { MarcoEventInput, MarcoWebhookResponse } from "../types";

const MarcoWebhookDocument = gql`
  mutation MarcoWebhook($status: MarcoEventInput!) {
    marcoWebhook(status: $status) {
      success
      error
    }
  }
`;

const marcoWebhook = async (status: MarcoEventInput): Promise<MarcoWebhookResponse> => {
  const resp = await request<{ marcoWebhook: MarcoWebhookResponse }>(MarcoWebhookDocument, { status });
  return ("marcoWebhook" in resp)
    ? resp.marcoWebhook
    : { success: false, error: resp.error?.message };
};

export default marcoWebhook;
