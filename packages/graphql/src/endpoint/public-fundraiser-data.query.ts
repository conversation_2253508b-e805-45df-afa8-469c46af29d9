import { gql } from "graphql-request";

import request from "../request";
import type { PublicFundraiserData } from "../types";

const PublicFundraiserDataDocument = gql`
  query PublicFundraiserData($fundraiserId: Int!) {
    publicFundraiserData(fundraiserId: $fundraiserId) {
      id
      primaryColor
      logo
      name
      goal
      description
      programLeader
      personalMessage
      status
      storeUrl
    }
  }
`;

const publicFundraiserData = async (fundraiserId: number): Promise<PublicFundraiserData|undefined> => {
  const resp = await request<{publicFundraiserData: PublicFundraiserData}>(PublicFundraiserDataDocument, { fundraiserId });
  return ("publicFundraiserData" in resp)? resp.publicFundraiserData : undefined;
};

export default publicFundraiserData;
