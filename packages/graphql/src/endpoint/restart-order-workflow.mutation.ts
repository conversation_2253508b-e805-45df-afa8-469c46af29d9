import { gql } from "graphql-request";

import request from "../request";
import { OrderResult, OrderStatus } from "../types";

const RestartOrderWorkflowDocument = gql`
  mutation restartOrderWorkflow($netsuiteId: String!) {
    restartOrderWorkflow(netsuiteId: $netsuiteId) {
      status
      netsuiteId
      errors
    }
  }
`;

const restartOrderWorkflow = async (netsuiteId: string): Promise<OrderResult> => {
  const resp = await request<{ restartOrderWorkflow: OrderResult }>(RestartOrderWorkflowDocument, { netsuiteId });
  return ("restartOrderWorkflow" in resp)
    ? resp.restartOrderWorkflow
    : { status: OrderStatus.Failure, errors: [resp?.error.message ?? "Unexpected error"]};
};

export default restartOrderWorkflow;
