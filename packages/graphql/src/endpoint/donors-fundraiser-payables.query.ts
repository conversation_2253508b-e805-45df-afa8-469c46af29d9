import { gql } from "graphql-request";

import request from "../request";
import type { DonorFundraiserPayablesInput, DonorFundraiserPayable } from "../types";

const DonorsFundraiserPayablesDocument = gql`
  query DonorsFundraiserPayables($input: DonorFundraiserPayablesInput!) {
    donorsFundraiserPayables(input: $input) {
      ...on DonorDonation {
        __typename
        deletedAt
        fundraiserId
        donor {
          email
          firstName
          lastName
        }
      }
      ...on DonorFundraiserPurchase {
        __typename
        deletedAt
        donor {
          email
          firstName
          lastName
        }
      }
      ...on DonorOfflineDonation {
        __typename
        deletedAt
        fundraiserId
        donor {
          email
          firstName
          lastName
        }
      }
    }
  }
`;

const donorsFundraiserPayables = async (input: DonorFundraiserPayablesInput): Promise<DonorFundraiserPayable[]|undefined> => {
  const resp = await request<{donorsFundraiserPayables: DonorFundraiserPayable[]}>(DonorsFundraiserPayablesDocument, { input });
  return ("donorsFundraiserPayables" in resp)? resp.donorsFundraiserPayables : undefined;
};

export default donorsFundraiserPayables;
