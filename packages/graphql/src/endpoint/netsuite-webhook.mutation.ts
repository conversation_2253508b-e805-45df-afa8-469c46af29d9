import { gql } from "graphql-request";

import request from "../request";
import type { NetsuiteEventInput, NetsuiteWebhookResponse } from "../types";

const NetsuiteWebhookDocument = gql`
  mutation NetsuiteWebhook($netsuiteEvent: NetsuiteEventInput!) {
    netsuiteWebhook(netsuiteEvent: $netsuiteEvent) {
      success
      error
    }
  }
`;

const netsuiteWebhook = async (netsuiteEvent: NetsuiteEventInput): Promise<NetsuiteWebhookResponse> => {
  const resp = await request<{ netsuiteWebhook: NetsuiteWebhookResponse }>(NetsuiteWebhookDocument, { netsuiteEvent });
  return ("netsuiteWebhook" in resp)
    ? resp.netsuiteWebhook
    : { success: false, error: resp.error?.message };
};

export default netsuiteWebhook;
