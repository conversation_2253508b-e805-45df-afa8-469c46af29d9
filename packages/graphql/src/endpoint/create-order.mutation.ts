import { gql } from "graphql-request";

import request from "../request";
import { type OrderInput, type OrderResult, OrderStatus } from "../types";

const CreateOrderDocument = gql`
  mutation CreateOrder($order: OrderInput!, $restartWorkflow: Boolean=false) {
    createStoreOrder(order: $order, restartWorkflow: $restartWorkflow) {
      status
      errors
    }
  }
`;

const createOrder = async (order: OrderInput, restartWorkflow: boolean = false): Promise<OrderResult> => {
  const resp = await request<{ createStoreOrder: OrderResult }>(CreateOrderDocument, { order, restartWorkflow });
  return ("createStoreOrder" in resp)
    ? resp.createStoreOrder
    : { status: OrderStatus.Failure, errors: resp.error?.message ? [resp.error.message] : [] };
};

export default createOrder;
