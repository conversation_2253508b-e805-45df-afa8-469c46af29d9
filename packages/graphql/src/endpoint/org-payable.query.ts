import { gql } from "graphql-request";

import request from "../request";
import type { Org } from '../types';

const ORG_PAYABLE_QUERY = gql`
  query OrgPayable($campaignId: ID!) {
    orgPayable(campaignId: $campaignId) {
      id
      name
      type
      parent {
        id
        name
        type
      }
    }
  }
`;

export const orgPayable = async (campaignId: string): Promise<Org[]> => {
  const resp = await request<{ orgPayable: Org[] }>(ORG_PAYABLE_QUERY, { campaignId });
  return ("orgPayable" in resp) ? resp.orgPayable : [];
};

export default orgPayable;
