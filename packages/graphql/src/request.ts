/* eslint-disable @typescript-eslint/no-explicit-any */
import { request as graphql, RequestDocument, Variables } from "graphql-request";
import { GraphQLClientRequestHeaders } from "graphql-request/src/types";
import Sentry from "@store-monorepo/sentry";
import Logger from "@store-monorepo/logger";
const defaultRequestHeaders: GraphQLClientRequestHeaders = {
  "x-api-key": `${process.env.GRAPHQL_API_KEY}`
};
type Response = {
  success?: boolean
  error?: string
  errors?: string[]
  status?: string
}
const request = async <T>(document: RequestDocument, variables: Variables, requestHeaders: GraphQLClientRequestHeaders = defaultRequestHeaders) => {
  try {
    const resp = await graphql<T>({
      url: `${process.env.GRAPHQL_URL}`,
      document,
      variables,
      requestHeaders
    });
    const error = (resp as Response).error || (resp as Response).errors?.join(",");
    if (error) {
      Logger.error(error);
      Sentry.captureException(error);
    }

    return resp;
  } catch (error: any) {
    Logger.error(error.message);
    Sentry.captureException(error);
    return { error: error as Error };
  }
};

export default request;
