import Graphql from "../src/index";

import { OrderInput, OrderStatus, OrderResult, Vendor, ShippingProvider } from "../src/types";

type CreateOrderMutationResponse = { createStoreOrder: OrderResult };

const graphqlRequestMock = jest.fn();

jest.mock("graphql-request", () => {
  return {
    ...jest.requireActual("graphql-request"),
    request: jest.fn().mockImplementation(() => graphqlRequestMock())
  };
});

const ORDER: OrderInput = {
  carrier: ShippingProvider.FedEx,
  city: "New York",
  fundraiserId: "fundraiser_123",
  line2: "Apt 4B",
  netsuiteId: "netsuite_123",
  packingSlipId: "packingSlip_123",
  packingSlipTitle: "Packing Slip Title",
  products: [
    { receiverName: "Test User", logo: "", netsuiteId: "123" },
    { receiverName: "Test User2", logo: "", netsuiteId: "234" }
  ],
  scheduleAt: "2023-10-01",
  shipTo: "John Doe",
  state: "NY",
  street: "123 Main St",
  street2: "Suite 100",
  vendor: Vendor.Marco,
  zipCode: "10001"
};

describe("createOrder", () => {
  it("should return the success result", async () => {
    graphqlRequestMock.mockImplementation((): CreateOrderMutationResponse => ({
      createStoreOrder: {
        status: OrderStatus.Success,
        errors: []
      }
    }));

    const result = await Graphql.createOrder(ORDER);

    expect(result).toBeDefined();
    expect(result.status).toBe(OrderStatus.Success);
    expect(result.errors).toHaveLength(0);
  });

  it("should return the error result", async () => {
    graphqlRequestMock.mockImplementation((): CreateOrderMutationResponse => { throw new Error("Unexpected Error"); });

    const result = await Graphql.createOrder(ORDER);

    expect(result).toBeDefined();
    expect(result.status).toBe(OrderStatus.Failure);
    expect(result.errors).toEqual(["Unexpected Error"]);
  });
});
