import GraphQl from "../src/index";
import { type Marco<PERSON>ventInput, MarcoOrderStatus, MarcoWebhookResponse } from "../src/types";

type MarcoWebhookMutationResponse = { marcoWebhook: MarcoWebhookResponse };

const graphqlRequestMock = jest.fn();

jest.mock("graphql-request", () => {
  return {
    ...jest.requireActual("graphql-request"),
    request: jest.fn().mockImplementation(() => graphqlRequestMock())
  };
});

const STATUS: MarcoEventInput = {
  id: 8116900,
  purchase_order: "",
  status: MarcoOrderStatus.Shipped,
};

describe("marcoWebhook", () => {
  it("should return the success hook result", async () => {
    graphqlRequestMock.mockImplementation(
      (): MarcoWebhookMutationResponse => ({
        marcoWebhook: {
          success: true,
          error: null
        },
      })
    );

    const result = await GraphQl.marcoWebhook(STATUS);

    expect(result).toBeDefined();
    expect(result.success).toBe(true);
  });

  it("should return the error hook result", async () => {
    graphqlRequestMock.mockImplementation(
      (): MarcoWebhookMutationResponse => ({
        marcoWebhook: {
          success: false,
          error: "Unexpected error."
        },
      })
    );

    const result = await GraphQl.marcoWebhook(STATUS);

    expect(result).toBeDefined();
    expect(result.success).toBe(false);
  });
});
