import GraphQl from "../src/index";
import { UserWithPermissions } from "../src/types";

type MeQueryResponse = { me: UserWithPermissions };

const graphqlRequestMock = jest.fn();

jest.mock("graphql-request", () => {
  return {
    ...jest.requireActual("graphql-request"),
    request: jest.fn().mockImplementation(() => graphqlRequestMock()),
  };
});

describe("me", () => {
  it("should return user with permissions", async () => {
    graphqlRequestMock.mockImplementation(
      (): MeQueryResponse => ({
        me: {
          apps: ["raise", "spend", "drive"],
          email: "<EMAIL>",
          firstName: "Super",
          id: "udp_clpjnqijh003gcoz3nws095m3",
          lastName: "Admin",
          permissions: ["user:read", "user:write"],
        },
      })
    );

    const me = await GraphQl.me();

    expect(me).toBeDefined();
    expect(me?.email).toEqual("<EMAIL>");
    expect(me?.permissions).toBeInstanceOf(Array);
  });
});
