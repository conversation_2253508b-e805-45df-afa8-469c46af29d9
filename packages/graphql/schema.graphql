schema {
  query: Query
  mutation: Mutation
}
scalar BigInt
scalar Buffer
scalar Date
scalar DateTime
scalar DateType
scalar Decimal
"An ISO 8601-encoded datetime"
scalar ISO8601DateTime
"The `Int32` scalar type represents non-fractional signed whole numeric values. Int32 can represent values between -(2^31) and 2^31 - 1. "
scalar Int32
"The `Int64` scalar type represents non-fractional signed whole numeric values. Int64 can represent values between -(2^63) and 2^63 - 1. "
scalar Int64
scalar JSON
scalar JSONObject
scalar NonEmptyString
scalar Time
"A `Timestamp` represents a point in time independent of any time zone or calendar, represented as seconds and fractions of seconds at nanosecond resolution in UTC Epoch time."
scalar Timestamp
scalar UUID
""
scalar Uint64
scalar Upload
scalar Void
scalar udid
"Account balance information"
type AccountBalanceReturn {
  "Available balance in cents"
  available: Int
  "Pending balance in cents"
  pending: Int
  type: String
}
type AccountManager implements DriveUser {
  email: String
  firstName: String
  lastName: String
  phone: String
}
type AccountTransactionsPaginatedOutput {
  nextCursor: String
  transactions: [Transaction]!
}
type ActiveChannelInviteLink {
  code: String!
  revokedAt: Date
  revokedBy: String
  url: String!
}
"See CampaignMembership for details"
type AdminCampaign {
  id: ID!
}
type AdminGroupRosterType {
  archived: Boolean!
  email: String!
  groupId: String!
  groupName: String!
  groupRosterId: String!
  inviteId: String
  orgName: String!
  participantName: String!
  rosterId: String!
  seasonId: String!
  seasonName: String!
  status: String!
  userId: String!
}
type AdminInvoices {
  amount: Int!
  authorizedAt: String
  balanceDue: Int!
  budgetItem: String!
  creditAmount: Int!
  description: String!
  discountAmount: Int
  dueDate: String!
  id: String!
  isOptional: Boolean!
  note: String
  paidDate: String
  paymentMethod: String
  paymentScheduleInvoice: String
  penaltyAmount: Int
  refundAmount: Int
  rosterName: String!
  status: String!
}
type AlumniDonorSchoolDetails {
  orgId: String
  schoolAddress: String
  schoolName: String
}
type ApiKey {
  createdAt: Date!
  expiresAt: Date
  id: String!
  label: String!
  maxRps: Int!
  name: String!
  permissions: [ApiKeyPermission!]!
  secret: String!
  user: User!
}
type ApiKeyPermission {
  permission: Permission!
}
type ApprovalDetailType {
  amount: Int
  dateReviewed: String
  dateSubmitted: String!
  from: String
  group: String
  id: String!
  memo: String
  note: String
  reason: String
  recipient: String
  shippingAddress: SpendDebitCardShippingType
  submittedBy: String!
  to: String
  transferType: String
}
type ApprovalType {
  amount: Int
  approverIds: [String!]
  createdAt: String!
  group: String
  id: String!
  numberOfApprovals: Int
  numberOfApprovers: Int
  receipent: String
  status: String!
  submittedBy: String!
  title: String!
  transferType: String
}
type ApproveFundraiserApprovalSubmissionData {
  isSsoApprover: Boolean!
  submitterName: String!
}
type AuditLogEntry {
  createdAt: DateTime!
  id: String!
  payload: JSON!
  service: AuditLogService!
  source: AuditLogSource!
  type: AuditLogType!
  userId: String
}
type AuditLogList {
  list: [AuditLogEntry]!
}
type Auth implements Tokens {
  accessToken: String
  refreshToken: String
}
type AuthChallenge implements Tokens {
  accessToken: String
  challenges: [UserChallenge]
  refreshToken: String
}
type AuthenticationFlow {
  name: String
  redirectUrl: String
}
type AuthorizedUser {
  email: String!
  firstName: String!
  lastName: String!
  phoneNumber: String!
}
type AuthorizedUsersAdded {
  usersAdd: [AuthorizedUser!]!
}
type Award {
  award: String
  firstName: String
  fullName: String
  id: ID!
  lastName: String
  school: String
  years: String
}
type Brand {
  label: String
  value: Int
}
type BudgetTransaction {
  id: UUID!
  status: Status
}
type BulkRewards {
  id: Int!
  incentiveId: Int
  size: String
  tier: Int
}
"@deprecated: will be merged with DriveCampaign instead This type is for Drive's Campaign Management. Users must have the Drive's permission to access any data. We are working on migrating this type to DriveCampaign."
type Campaign {
  "Account Manager of Campaign"
  accountManager: AccountManager
  "Activity type of campaign. e.g. 'football', 'baseball', etc."
  activityType: String
  approvedFunds: approvedFunds
  "URL for Generated Report of (Active) Campaign"
  campaignActiveReportUrl: String
  "City + State string of campaign. e.g. 'Dallas, TX'"
  campaignTerritory: String
  coachJoinCode: String
  "Donations statistics of campaign. All values will be 0 if campaign is 'upcoming'"
  donations: Donations
  "Participant email delivery info of campaigns"
  emails: EmailsInfo
  "End date of fundraiser"
  endDate: DateTime
  "Entity ID of campaign. This ID used to obtain financial/payment info"
  entityId: Int
  "Date when campaign is finalized"
  finalizedDate: DateTime
  fundraiserId: ID
  "Shipping status for gear items. Values are 'delivered','pre_transit','in_transit','failure','available_for_pickup','unknown','out_for_delivery', 'return_to_sender','cancelled','error'"
  gearStatus: String
  "Group Leader"
  groupLeader: GroupLeader
  "Deal ID of campaign in Hubspot."
  hubspotId: String
  id: Int
  "Cover image of campaign"
  image: String
  "Initial goal amount to fundraise in cents"
  initialGoalCents: Int
  "Link to invite participants to the campaign"
  inviteParticipantUrl: String
  "Unique join-code of campaign"
  joinCode: Int
  "URL for the fundraiser KYC Form."
  kycFormUrl: String
  "Campaign kyc status"
  kycStatus: CampaignKycStatusResult
  "Mailing address of campaign."
  mailingAddress: String
  "MDM Contact of Campaign"
  mdm: MDMDetails
  name: String
  origin: String
  "Statistics info of campaign participants"
  participants: ParticipantsInfo
  "Last withdrawal payment status. Values are 'Initializing', 'Processing', 'Transferred', 'Cut', 'Deposited', 'Expired' or 'Failed'"
  paymentStatus: String
  "Payment type when campaign finalized. Values are 'Check to Office', 'Check to Salesrep', 'Check to Fundraiser', or 'Direct Deposit'."
  paymentType: String
  "Base64 generated CSV file for preload emails report of event"
  preloadReport: String
  "Download URL for Final Receipt (PDF) of settled campaigns"
  receiptDownloadUrl: String
  "Settlement Details for Closed Campaigns"
  settlementDetails: SettlementDetails
  "Settlement Confirmation Status for Closed Campaigns. Values are 'UNSUBMITTED', 'IN_REVIEW' and 'VERIFIED'"
  settlementStatus: String
  slug: String
  "Start date of fundraiser"
  startDate: DateTime
  status: String
  "Size of team/group of campaign"
  teamSize: Int
  "Not in-use. Deprecated"
  testEmails: String
  "Total amount raised in cents"
  totalRaisedCents: Int
  "The total raised combined this organization has previously ran campaigns"
  totalRaisedHistory: Int
  "Tracking link for shipping gear items."
  trackingLink: String
}
type CampaignDates {
  endDateTime: String!
  isAllDay: Boolean
  startDateTime: String!
}
"Resigned campaigns"
type CampaignHistoryList {
  CampaignHistoryEndDate: String
  CampaignHistorySlug: String
  CampaignHistoryStartDate: String
  campaignName: String
  campaignTotalRaised: Int
}
type CampaignKyc {
  status: CampaignKycStatusEnum
}
type CampaignKycStatusResult {
  data: CampaignKycStatus
  type: CampaignKycType
}
type CampaignList {
  count: Int
  list: [Campaign]!
  offset: Int
}
type CampaignPersonList {
  id: Int!
}
type CampaignPersonListData {
  email: String!
  id: Int
}
type CampaignPersonListEntries {
  invalidEntries: [InvalidCampaignPersonListData]
  validEntries: [CampaignPersonListData]
}
type CampaignPersonListEntryDelete {
  id: Int!
}
type CampaignSearchFilter {
  dateCreated: DateTime
  dateUpdated: DateTime
  "Collection (key/values) of saved search criteria."
  filterCriteria: JSON
  "Name of the saved search filter."
  filterName: String
  "ID of saved search filters."
  id: Int
}
type CampaignSmsDataResponse {
  invalidEntries: [InvalidCampaignSmsInviteData!]!
  status: CampaignSmsInviteStatus
  validEntries: [CampaignSmsInviteData!]!
}
type CampaignSmsInviteData {
  id: Int!
  phoneNumber: String!
}
type CampaignSmsInviteDeleteResponse {
  status: String!
}
"Owned by vault Extended by wallet Card issued by vault via Stripe"
type Card {
  cardNumber: String
  cardType: VaultCardType
  cardholderId: String
  cvv: String
  expirationMonth: Int
  expirationYear: Int
  gatewayId: String
  id: String!
  last4: String
  metadata: VaultCardMetadata
  spendingLimitAmount: Int
  spendingLimitInterval: VaultSpendingLimitInterval
  status: VaultCardStatus
}
type CarouselItem {
  id: Int
  image: String
  media_type: String
  url: String
}
type Champion {
  division: Division
  id: ID!
  school: String
  years: String
}
type Channel {
  chatProviderId: String!
  chatType: ChannelTypesEnum!
  createdAt: Date!
  description: String
  id: ID!
  isReadOnly: Boolean
  name: String!
  status: ChannelStatusEnum!
  updatedAt: Date
}
type ChannelAddModeratorsError implements Error {
  message: String!
}
type ChannelAddUsersError implements Error {
  message: String!
}
type ChannelCreateError implements Error {
  message: String!
}
type ChannelInvite {
  contact: String!
  id: ID!
  method: ContactMethodEnum!
  role: ChannelRolesEnum!
  status: ChannelInviteStatusEnum!
}
type ChannelInviteAcceptError implements Error {
  message: String!
}
type ChannelInviteAcceptedContacts {
  contact: String!
  id: ID!
  method: ContactMethodEnum!
  role: ChannelRolesEnum!
}
type ChannelInviteFailedContacts {
  contact: String!
  error: String
  method: ContactMethodEnum!
  role: ChannelRolesEnum!
}
type ChannelInviteLink {
  code: String!
  revokedAt: Date
  revokedBy: String
}
type ChannelInviteLinkRevokeError implements Error {
  message: String!
}
type ChannelInviteLinks {
  activeInviteLink: ActiveChannelInviteLink!
}
type ChannelInviteMembers {
  acceptedContacts: [ChannelInviteAcceptedContacts!]!
  failedContacts: [ChannelInviteFailedContacts!]!
}
type ChannelInviteMembersError implements Error {
  message: String!
}
type ChannelInviteResendError implements Error {
  message: String!
}
type ChannelInvites {
  count: Int
  invites: [ChannelInvite!]!
}
type ChannelJoinError implements Error {
  message: String!
}
type ChannelMember {
  createdAt: Date
  role: String!
  user: UserPublic
  userId: String!
}
type ChannelMembers {
  channelMembers: [ChannelMember!]!
  members: [UserPublic!]! @deprecated(reason: "Use channelMembers instead")
}
type ChannelNotFound implements Error {
  message: String!
}
type ChannelOrg {
  orgId: ID!
}
type ChannelOrgError implements Error {
  message: String!
}
type ChannelRemoveMembersError implements Error {
  message: String!
}
type ChannelUpdateError implements Error {
  message: String!
}
type ChannelUpdateMembersError implements Error {
  message: String!
}
type ChannelUpsertUsersError implements Error {
  message: String!
}
type Channels {
  channels: [Channel!]!
  myMemberships: [UserChannelMembership!]!
}
type ChatToken {
  accessToken: String!
}
type Check {
  amount: Decimal!
  campaignId: Int!
  checkNumber: String!
  created: Timestamp!
  destination: Account!
  effective: Timestamp!
  expiration: Timestamp!
  payee: Payee!
  status: CheckStatus!
  transactionId: UUID!
}
type Cheer {
  anonymous: Boolean
  createdAt: DateTime
  donorMessage: String
  donorName: String
  firstName: String
  id: Int
  images: [String]
  lastName: String
  subtotalCents: Int
}
"@deprecated"
type CheerWallData {
  createdAt: DateTime
  donorMessage: String
  donorName: String
  id: String
  images: [String]
  participant: ParticipantData
  subtotalCents: Int
}
type CombinedPermissions {
  permissions: [String]
}
type CommonCalendar {
  activity: String
  bus_count: Int
  comments: String
  confirmed: String
  driver_name: String
  driver_phone: String
  end_time: String
  event_date: String
  facility: String
  g_s: String
  gender: String
  home_field: String
  id: Int
  levels: String
  opponent: String
  place: String
  season_id: Int
  sports_code: String
  sports_description: String
  sports_group: String
  sports_name: String
  start_time: String
  vehicle_id: String
  vehicle_type: String
}
type CommonGrid {
  activity: String
  bus_count: Int
  comments: String
  confirmed: String
  driver_name: String
  driver_phone: String
  end_time: String
  event_date: String
  facility: String
  g_s: String
  gender: String
  home_field: String
  id: Int
  levels: String
  place: String
  season_id: Int
  sports_code: String
  sports_description: String
  sports_group: String
  sports_name: String
  start_time: String
  vehicle_id: String
  vehicle_type: String
}
type CommonSchedules {
  activity: String
  bus_count: Int
  bus_departure_location: String
  bus_early_dismissal_time: String
  bus_estimated_return_time: String
  bus_time: String
  cancellation_status: String
  comments: String
  confirmed: String
  driver_name: String
  driver_phone: String
  end_time: String
  event_date: String
  facility: String
  g_s: String
  gender: String
  home_field: String
  id: Int
  levels: String
  opponent: String
  place: String
  season_id: Int
  sports_code: String
  sports_description: String
  sports_group: String
  sports_name: String
  start_time: String
  vehicle_id: String
  vehicle_type: String
}
type CommonSheet {
  activity: String
  bus_count: Int
  comments: String
  confirmed: String
  driver_name: String
  driver_phone: String
  end_time: String
  event_date: String
  facility: String
  fee: Float
  g_s: String
  gender: String
  home_field: String
  id: Int
  levels: String
  place: String
  season_id: Int
  sports_code: String
  sports_description: String
  sports_group: String
  sports_name: String
  start_time: String
  vehicle_id: String
  vehicle_type: String
}
type CommsContactResponse {
  contact: String
  date: String
  id: String
  status: RequestStatus
}
type CommsHtmlResponse {
  errors: [String]
  html: String
  mjml: String
}
type CommsMessageResponse {
  consumerTemplateId: String!
  contacts: [MessageContactResponse!]!
  createdAt: Date!
  id: ID!
  sendAt: Date!
}
type CommsRegisterResponse {
  attributes: [String]
  createdAt: Date
  emailProvider: SupportedEmailProviders
  from: String
  fromName: String
  id: ID!
  mjml: String
  name: String
  service: SnapService!
  smsProvider: SupportedSmsProviders
  subject: String
  testData: JSON!
  text: String
  transport: MessageTransportType!
  type: MessageType!
}
type CommsSendResponse {
  details: [CommsContactResponse!]
  id: ID!
  rejectedContacts: [String!]!
}
type CommsTemplateResponse {
  attributes: [String]
  createdAt: Date
  emailProvider: SupportedEmailProviders
  errors: [String]
  from: String
  fromName: String
  html: String
  id: ID!
  mjml: String
  name: String
  service: SnapService!
  smsProvider: SupportedSmsProviders
  subject: String
  testData: JSON!
  text: String
  transport: MessageTransportType!
  type: MessageType!
  unsubscribeGroupId: Int
  unsubscribeGroups: [Int]
}
type CommsTestResponse {
  message: String
}
type CommsUnsubscribeGroup {
  description: String
  id: Int!
  name: String
  unsubscribes: Int
}
type CommsUnsubscribeGroupsResponse {
  groups: [CommsUnsubscribeGroup!]
}
type Conference {
  active: Boolean
  color: String
  conferenceSports(id: ID): [ConferenceSport!]
  documents(docType: String): [LeagueKeeperDocument!]
  facebookUrl: String
  id: ID!
  instagramUrl: String
  levels: [LeagueKeeperLevel!]
  link1Label: String
  link1Url: String
  link2Label: String
  link2Url: String
  logoUrl: String
  name: String
  schools: [School!]
  shortName: String
  twitterUrl: String
  url: String
  welcomeMessage: String
  youtubeUrl: String
}
type ConferenceSport {
  awards: [Award!]
  champions(school: String, years: String): [Champion!]
  document: LeagueKeeperDocument
  events(endDate: ISO8601DateTime, levelId: ID, startDate: ISO8601DateTime, years: String): [LeagueKeeperEvent!]
  gender: Gender
  id: ID!
  name: String
  sport: Sport
  standings(levelId: ID!, years: String!): [Standing!]
  summary(levelId: ID!, years: String!): [Summary!]
}
type ConfirmProfileChallengeResponse {
  auth: Auth!
  challenge: UserChallenge!
  success: Boolean!
}
type Consumer {
  accessBy: String
  color: String!
  description: String!
  icon: String!
  id: String!
  inApps: Boolean!
  logo: String!
  mask: String!
  modal: ConsumerModal
  title: String!
  url: String!
}
type ConsumerModal {
  descriptions: [String]!
  header: String!
}
type ContractItem {
  event_contract: EventContract
  event_id: Int
  id: Int
}
type ContractSignees {
  auth_hash: String
  comments: String
  email_id: String
  event_contract_number: Int
  id: Int
  original_contract: String
  sent_date: Date
  sign_contract: String
  sign_date: Date
  sign_status: String
}
type ControlPanelEventResult {
  createdAt: ISO8601DateTime!
  eventId: String!
  id: ID!
  school: ControlPanelSchool!
  updatedAt: ISO8601DateTime!
}
type ControlPanelSchool {
  address: String!
  city: String!
  createdAt: ISO8601DateTime!
  fax: String!
  id: ID!
  name: String!
  orgsId: String!
  phone: String!
  state: String!
  updatedAt: ISO8601DateTime!
  webfolder: String!
  zip: String!
}
"Owned by Vault Only used in legacy createCard"
type CoreCardFields {
  cardId: String!
  expirationMonth: Int!
  expirationYear: Int!
  gatewayId: String
  last4: String!
  name: String!
}
"Autogenerated return type of CpDeleteEventResult."
type CpDeleteEventResultPayload {
  "A unique identifier for the client performing the mutation."
  clientMutationId: String
  errors: [String!]
  success: Boolean
}
"Autogenerated return type of CpUpdateEventResult."
type CpUpdateEventResultPayload {
  "A unique identifier for the client performing the mutation."
  clientMutationId: String
  errors: [String!]
  eventResult: ControlPanelEventResult
}
type CreatedFundraiserUser {
  id: Int!
}
type CreatedOtkEntry {
  id: Int!
}
type CreatedParticipantRewards {
  id: Int!
}
type CurrentCustomersDeals {
  activity: String
  dealName: String
  dealStage: String
  entityId: String
  fundraiserStatus: String
  id: String
  lastLaunch: String
  leaderFirstName: String
  leaderLastName: String
  previousDealId: String
  slug: String
  snapFundraiserId: String
  totalRaised: Int
}
"Information of custom email templates"
type CustomContactTemplates {
  dateCreated: DateTime
  dateUpdated: DateTime
  id: Int
  message: String
  name: String
  subject: String
}
type DailyCalendarBusScheduleEvents {
  activity: String
  activityType: String
  bus_time: String
  calendarDate: String
  comments: String
  confirmed: String
  departure_location: String
  early_dismissal_required: String
  early_dismissal_time: String
  end_time: String
  estimated_return_time: String
  event_comments: String
  event_date: String
  event_id: Int
  g_s: String
  location: String
  num_buses: Int
  num_buses_text: Int
  opponent: String
  place: String
  sport: String
  sportGender: String
  sportLevel: String
  sport_name: String
  start_time: String
}
type DailyCalendarBusScheduleExportData {
  activity: String
  bus_time: String
  comments: String
  confirmed: String
  departure_location: String
  driver_name: String
  driver_phone: String
  early_dismissal_required: String
  early_dismissal_time: String
  end_time: String
  estimated_return_time: String
  event_date: String
  event_id: Int
  g_s: String
  gender: String
  groupval: String
  level1: String
  location: String
  num_buses_text: Int
  opponent: String
  place: String
  sport_description: String
  sport_name: String
  start_time: String
  transport_comments: String
  vehicle_id: String
  vehicle_type: String
}
type DailyCalendarEvent {
  activity: String
  comments: String
  departure_location: String
  description: String
  early_dismissal_time: String
  end_time: String
  estimated_return_time: String
  eventId: Int
  eventTransportDetails: [EventTransportDetails]
  event_date: Date
  location: String
  num_buses: Int
  officials: [Official]
  opponent: String
  preparations: [Preparation]
  schoolInfo: SchoolInfo
  season_team: Int
  season_years: String
  start_time: String
  workers: [Worker]
}
type DailyCalendarEventTransportDetails {
  driver_name: String
  driver_phone: String
  event_transport_details_id: Int
  vehicle_id: String
  vehicle_type: String
}
type DailyCalendarOfficialDuties {
  Address: String
  City: String
  Home_Phone: String
  SSN: String
  State: String
  Work_Phone: String
  Zip: String
  cell_phone: String
  duty: String
  email: String
  id: Int
  offic_id: String
  paid: String
  salary: String
  vendor_number: String
  voucher_field: Int
  voucher_number: String
  woker_name: String
}
type DailyCalendarOfficialExport {
  address: String
  cell_phone: String
  city: String
  comments: String
  duty: String
  email: String
  end_time: String
  event_date: String
  event_id: Int
  groupVal: String
  home_phone: String
  location: String
  offic_id: String
  opponent: String
  salary: String
  sport_description: String
  ssn: String
  start_time: String
  state: String
  vendor_number: String
  voucher_number: String
  woker_name: String
  work_phone: String
  zip: String
}
type DailyCalendarOfficialMessage {
  comments: String
  end_time: String
  event_date: String
  event_id: Int
  location: String
  official_duties: [DailyCalendarOfficialDuties]
  opponent: String
  sport: String
  start_time: String
}
type DailyCalendarPreparation {
  comments: String
  prep: String
  qty: String
}
type DailyCalendarPreparationExportData {
  comments: String
  end_time: String
  event_date: String
  event_id: Int
  groupval: String
  location: String
  opponent: String
  prep: String
  qty: String
  sport_description: String
  start_time: String
}
type DailyCalendarPreparationMessage {
  comments: String
  confirmed: String
  end_time: String
  event_date: String
  event_id: Int
  groupval: String
  location: String
  opponent: String
  place: String
  preparations: [DailyCalendarPreparation]
  sport: String
  start_time: String
}
type DailyCalendarWorkerDuties {
  SSN: String
  comments: String
  duty: String
  end_time: String
  event_date: String
  event_id: Int
  groupval: String
  location: String
  opponent: String
  organization: String
  paid: String
  pay_code: String
  phones: String
  salary: Float
  sport_description: String
  start_time: String
  woker_name: String
}
type DailyCalendarWorkerExportData {
  comments: String
  duty: String
  end_time: String
  event_date: String
  event_id: Int
  groupval: String
  location: String
  opponent: String
  pay_code: String
  phones: String
  sport_description: String
  start_time: String
  woker_name: String
}
type DailyCalendarWorkerMessage {
  comments: String
  end_time: String
  event_date: String
  event_id: Int
  groupval: String
  location: String
  opponent: String
  sport: String
  start_time: String
  worker_duties: [DailyCalendarWorkerDuties]
}
type DeleteCount {
  count: Int
}
type DeletedIncentiveRecord {
  id: ID!
}
type Deposit {
  amount: Decimal!
  campaignId: Int!
  created: Timestamp!
  description: String!
  destination: Account!
  effective: Timestamp!
  externalId: String!
  source: Account!
  transactionId: UUID!
}
type Division {
  id: ID!
  name: String
}
"Documents uploaded for validation purposes"
type Document {
  documentBack: String
  documentFront: String
  gateway: VaultFinancialProvider!
  id: String!
  type: String!
  vaultKybId: ID
  vaultKycId: ID
}
type DonateIntent {
  "Donation amount (without coverage fee) in cents"
  amount: Int
  anonymous: Boolean
  "Flag to indicate if the Credit card fee is covered by the donor"
  ccFeeCovered: Boolean
  "Credit card fee percentage, If leave null/undefined, it will be calculated at 0.03 (3%)"
  ccFeePercentage: Float
  "Fee amount coverage by donor (without coverage fee) in cents"
  coverageAmount: Int
  "Linked donation if existed"
  donation: DonorDonation
  donationId: String
  donorEmail: String
  donorFirstName: String
  donorLastName: String
  fundraiser: DonorFundraiser!
  groupLeaderId: String
  history: [DonateIntent]
  id: ID!
  latestCartPurchaseIntent: GiftShopPurchaseIntentData
  matchDon: String
  participantId: Int
  participantUserDirectoryId: String
  "Payment Method used for Strip or Paypal payment"
  paymentMethodId: String
  paymentProvider: PaymentProvider
  previousStatus: DonateIntentStatus
  "Share Type of donation"
  shareType: String
  "The subtotal amount in cents which will be send to Snap's Stripe account snapAmount = (amount + coverageAmount - allocationAmount + tipAmount), where allocationAmount is the subtotalAmount will be sent to team's Stripe connected account, calculated through donation-allocation-amount helper."
  snapAmount: Int
  status: DonateIntentStatus!
  tipAmount: Int
  "Total amounts will be charged to donor's credit card. Equals amount + coverageAmount"
  totalAmount: Int
}
"Not used yet. Will be removed soon."
type DonationInvite {
  email: String
  id: ID!
  lastEmailSentAt: String
  senderName: String
  status: String
}
type DonationInviteReleaseStatus {
  areDonationInviteEmailsReleased: Boolean!
}
type DonationInviteRemoveData {
  email: String
  id: Int!
}
type DonationInviteSMSData {
  id: Int!
  phoneNumber: String!
  status: String!
}
type DonationLevel {
  amount: Int!
  id: ID!
  level: DonationLevels!
  rewardDescription: String
  text: String
}
type DonationTopEarner {
  participantID: Int
}
"Donation Statistics. Will change name to CampaignDonationStats later."
type Donations {
  "Total of distinct donors in campaigns"
  AskedToShare: Int
  "Total count of donations in campaign"
  count: Int
  "Total donation amount in cents raised through email"
  emailTotal: Int
  "Total donation amount in cents raised other methods outside text, email and social"
  extraTotal: Int
  "Total of donation amount in cents raised through forward to friends."
  fowardTotal: Int
  "Total quantity of gear items purchased in campaign."
  gearAdded: Int
  "Total count of distinct donors that purchased gears."
  gearPurchased: Int
  "Total of purchase amount in cents of gear (OTK) items"
  gearTotal: Int
  "Total donation amounts in cents through retaining supporters from previous closed campaign. Share Type is 'reisgn_a' or 'resign_b'"
  legacySupporters: Int
  "Total count of distinct donors who are new and donated campaigns through share-type 'supporter_referral'"
  newSupporters: Int
  otherTotal: Int
  "Total count of donors who are retaining supporters from previous closed campaign. Share Type is 'reisgn_a' or 'resign_b'"
  pastSupporters: Int
  "Total donation amount in cents raised through social media"
  socialTotal: Int
  "Total donation amount in cents raised through sms/text"
  textTotal: Int
}
type DonationsResponse {
  fundraiserTopDonation: TopDonation
  fundraiserTotalDonationsRaised: TotalDonationsRaised
  participantTopDonation: TopDonation
  participantTotalDonationsRaised: TotalDonationsRaised
}
type DonorData {
  email: String
  firstName: String
  id: String
  lastName: String
}
"A donation made by a donor to a fundraiser."
type DonorDonation {
  anonymous: Boolean
  appFeeCents: Int
  authorizationCode: String
  averageDonationCents: Int
  ccFeePercentage: Float
  chargedBackAt: DateTime
  checkoutId: String
  checkoutState: DonorDonationCheckoutState
  cheerwallName: String
  countryCode: String
  coverageAmountCents: Int
  createdAt: DateTime
  creditCardId: String
  deletedAt: DateTime
  disputeWonId: String
  donateIntent: DonateIntent
  donationLevel: DonorDonationLevel
  donationLevelId: String
  donor: DonorUser
  donorId: String
  donorMessage: String
  donorName: String
  extCcFeeCents: Int
  fbPostId: String
  fundraiser: DonorFundraiser
  fundraiserId: String
  id: ID!
  intCcFeeCents: Int
  matchDon: String
  newsletterOptIn: Boolean
  origin: String
  originIp: String
  participant: DonorParticipantData
  participantId: Int
  participantUserDirectoryId: String
  paymentId: String
  paymentMethodId: String
  paymentProvider: String
  postalCode: String
  raiseId: Int
  refundReason: String
  serviceFeeCents: Int
  "Share Type of donation"
  shareType: String
  status: DonorDonationStatus
  subtotal: String
  subtotalCents: Int
  supporterId: Int
  tipAmountCents: Int
  totalPriceCents: Int
  transactionId: String
  updatedAt: DateTime
}
"Donation levels that are displayed to donors on the donation form, suggesting donation specific amounts and detailing why a donor might select them."
type DonorDonationLevel {
  amount: Int!
  createdAt: DateTime
  fundraiserId: String
  id: String!
  impact: String
  productId: Int
  rewardDescription: String
  updatedAt: DateTime
}
type DonorDonationRecordUpdate {
  donateIntentData: DonateIntent
  donationId: String
}
type DonorEmailData {
  deliveryStatus: String
  emailAddress: String
  followUpNumber: Int
  id: Int
  personListEntryID: Int
}
"A copy of the pertinent fields from the Fundraiser model in raise."
type DonorFundraiser {
  ccFeeCovered: Boolean
  ccFeePayer: String
  description: String
  donationMinimumCents: Int
  donations: [DonorDonation]
  endDate: DateTime!
  feeType: String
  "The amount of money to raise in dollars."
  goal: Int
  id: ID!
  logo: String
  name: String!
  "Old ID field from raise"
  raiseId: Int!
  startDate: DateTime!
  status: String
  whyDonations: String
}
type DonorFundraiserData {
  ccFeeCovered: Boolean
  ccFeePayer: SnapFeePayer!
  description: String
  donationMinimumCents: Int
  endDate: DateTime!
  feeType: String
  giftShop: Boolean!
  goal: Int
  id: String!
  isRaiseClassic: Boolean
  legalName: String
  logo: String
  name: String!
  participantGoal: Int
  personalMessage: String
  primaryColor: String
  programLeader: String
  programType: String
  raiseEntityId: Int
  raiseId: Int!
  redirect: Boolean
  "If the redirectPath is provided it means you should actually be accessing this data via the redirectPath"
  redirectPath: String
  region: String
  secondaryColor: String
  slug: String
  startDate: DateTime!
  status: FundraiserStatus!
  stripeConnectId: String
  subtotalDollarsSum: Float
  tippingEnabled: Boolean
  waCampaignSetup: Boolean
  whyDonations: String
}
type DonorFundraiserPurchase {
  createdAt: DateTime
  deletedAt: DateTime
  donationId: String
  donor: DonorUser
  fundraiserUserId: Int
  id: ID!
  lineItems: [DonorGiftShopItem]
  participant: DonorParticipantData
  participantUserDirectoryId: String
  paymentMethodId: String
  paymentProvider: String
  purchaseIntentId: String
  raiseId: Int
  status: String
  transactionId: String
  type: DonationsAndPurchasesEnum
  updatedAt: DateTime
}
type DonorGiftShopItem {
  costCents: Int
  createdAt: DateTime
  id: ID!
  image: String
  name: String
  priceCents: Int
  purchasableId: Int
  quantity: Int
  size: String
  status: String
  updatedAt: DateTime
}
type DonorOfflineDonation {
  anonymous: Boolean
  cheerwallName: String
  createdAt: DateTime
  deletedAt: DateTime
  "This type is meant for admin only"
  donor: DonorUser
  donorId: String
  donorMessage: String
  donorName: String
  fundraiser: DonorFundraiser
  fundraiserId: String
  id: ID!
  origin: String
  participant: DonorParticipantData
  participantId: Int
  participantUserDirectoryId: String
  raiseId: Int
  status: DonorDonationStatus
  subtotalCents: Int
  updatedAt: DateTime
}
type DonorParticipantData {
  raiseId: Int
  user: User
}
type DonorPayableRefundResponse {
  payable: DonorFundraiserPayable
  refundId: String
}
type DonorPersonListEntry {
  email: String
  id: Int!
}
"This type is meant for admin only"
type DonorUser {
  email: String
  firstName: String
  "Donor Id"
  id: ID!
  lastName: String
  raiseUserId: Int
  user: User
}
type DonorsCheerwall {
  createdAt: DateTime
  donorMessage: String
  donorName: String
  id: String
  images: [String]
  participant: DonorsParticipantPublic
  subtotalCents: Int
  type: String
}
type DonorsCheerwallsResult {
  cheers: [DonorsCheerwall]
  count: Int
}
type DonorsDonationLevel {
  amount: String!
  id: ID!
  impact: String
  rewardDescription: String
}
type DonorsDonationStatsResponse {
  count: Int
  donorsCount: Int
  otkPurchaseTotal: Int
  participantsWithDonationCount: Int
  totalAmountCents: Int
}
"@deprecated"
type DonorsFundraiserCheerWall {
  cheers: [CheerWallData]
  count: Int
}
type DonorsOrg {
  city: String
  id: ID!
  name: String
  phone: String
  stateCode: String
  street: String
  type: String
  url: String
  zipCode: String
}
type DonorsOrgsResult {
  data: [DonorsOrg!]!
  hasMore: Boolean
}
type DonorsParticipantOTKIncentive {
  description: String
  displayOrder: Int
  id: Int
  image: String
  name: String
  priceCents: Int
  purchaseLimit: Int
  purchased: Int
  size: String
}
type DonorsParticipantOTKIncentives {
  incentives: [DonorsParticipantOTKIncentive]
}
type DonorsParticipantPublic {
  data: DonorsParticipantPublicData
  raiseId: Int
}
type DonorsParticipantPublicData {
  firstName: String
  id: String
  lastName: String
  profilePicture: String
}
type DonorsParticipantsGiftShop {
  fundraiserUserId: Int
  giftShop: [DonorsParticipantsGiftShopData]
  totalPurchased: Int
}
type DonorsParticipantsGiftShopData {
  costCents: Int
  description: String
  id: Int
  image: String
  name: String
  netEarnedPercentage: Float
  priceCents: Int
  promoLabel: String
  purchaseLimit: Int
  size: String
}
"Account User is a user that is registered in Raise's database. Deprecated: Use UserDirectory 's User instead instead"
type DriveAccountUser implements DriveUser {
  email: String
  firstName: String
  id: String!
  lastName: String
  phoneNumber: String
  profilePicture: String
  snapRaiseId: Int
}
"MDM Actor. e.g. Account Manager/Salereps/Sales Manager"
type DriveAmSalesReps {
  "Primary contact email of salesrep"
  email: String
  "End time of salesrep"
  endDate: DateTime
  "First Name of Salesrep"
  firstName: String
  "Last Name of Salesrep"
  lastName: String
  "Primary phone number of salesrep"
  phone: String
  "The sales manager of this salesrep"
  salesManager: DriveAmSalesReps
  "Salesrep Id. This is Raise's account user id."
  salesrepId: Int
  "Start time of salesrep"
  startDate: DateTime
  "actor type. e.g. 'salesrep', 'salesmanager', 'am'"
  type: String
}
"Information of a single drive campaign"
type DriveCampaign {
  "The account manager of the campaign"
  accountManager: AccountManager
  "@deprecated: not used, in favor of totalRaisedCents"
  donationRaisedCents: Int
  "The date the campaign ends"
  endDate: DateTime
  "The date the campaign has been finalized/closed"
  finalizedDate: DateTime
  "The mapping id in fundraisers service"
  fundraiserId: ID
  "Indicate whether the campaign has any incentives, such as OTK"
  hasIncentive: Boolean
  id: Int!
  "Name of campaign"
  name: String
  "Notes for a campaign"
  notes: String
  "The origin of the campaign. Values: RAISE_CLASSIC, FUNDRAISERS_SERVICE"
  origin: String
  "The date where the campaign has settle its payments from snap"
  settlementDate: DateTime
  "Url-slug of campaign name which is a unique identifier"
  slug: String
  "The date campaign starts"
  startDate: DateTime
  "Current status of campaign"
  status: CampaignStatus
  "The size of the team/group attached to campaign"
  teamSize: Int
  "@deprecated: not used, in favor of `totalRaisedCents`"
  totalRaised: BigInt
  "Total amount of donation of campaign in cents."
  totalRaisedCents: Int
}
"Campaign/Fundraiser details information. NOTE: In the future, we might merged with DriveCampaign or Campaign"
type DriveCampaignDetails {
  "Account Balance of campaign."
  accountBalance: AccountBalanceReturn
  "The activity/sport of this campaign, e.g. 'football', 'wrestling', etc."
  activityType: String
  "The amount in cents that campaigns was advanced in Wallet."
  advanceAmount: Int
  "Bill type when settled/finalized. Values are 'invoice', 'regular' and 'default'"
  billType: String
  "Credit Card Fee Payer. Values are 'customer' or 'snap'."
  ccFeePayer: String
  "Charge back fee in cents. The charge from payment processor when a donor cancels their donation through their bank instead of getting a refund from us directly. (usually $15 per)"
  chargebackFee: Int
  "The statistic of a donation of campaigns"
  donationStats: DriveCampaignDonationStats
  "The statistics of emails delivered/sent"
  emailDelivery: EmailsInfo
  "The type of snap fee being collected. e.g. 'standard', '1+1', '20%/15%', etc."
  feeType: String
  "Total incentives price in cents."
  gearCost: Int
  "Group leader of a campaign"
  groupLeader: DriveGroupLeader
  id: Int!
  "Cover Image for Campaign"
  image: String
  "Goal of how much a fundraiser wants to raise"
  initialGoalCents: Int
  "kyc processor. Values are 'wepay' or 'stripe'"
  kycProcessor: String
  "kyc status of a campaign. Values are 'pending', 'active', 'inactive', etc."
  kycStatus: String
  "The address to ship the check for this campaign"
  mailingAddress: String
  "The amount in cents that campaigns was manual-advanced in Raise."
  manualAdvanceAmount: Int
  "Net raised amount in cents. Equals totalRaised minus snapFee and adds the otkBonus. It is the amount client received in their check/deposit."
  netRaisedTotal: Int
  "Notes for settlement for this campaign"
  notes: String
  "The legal name of a fundraisers organization"
  organizationLegalName: String
  "The tin number of the organization"
  organizationTin: String
  "Total bonus amount of otk in cents"
  otkBonus: Int
  "The statistics of participants's activity of campaign"
  participantStats: DriveParticipantStats
  "Processing Fee when settled/finalized."
  processingFee: Int
  "The date where the campaign has settle its payments from snap"
  settlementDate: DateTime
  "How the funds has delivered to the organization. Values are 'check_fundraiser', 'check_salesrep', 'check_office' and 'direct_deposit'"
  settlementMethod: String
  "The amount in cents collected as fee when a campaign ends based of the fee type"
  snapFee: Float
  "Location of this fundraiser"
  territory: String
  "The amount of purchase in cents."
  totalPurchaseCents: Int
  "Total raised of a fundraiser"
  totalRaisedCents: Int
  "The total raised combined this organization has previously ran campaigns"
  totalRaisedHistory: Int
}
"Pulls data on a campaigns donation broken down"
type DriveCampaignDonationStats {
  ccCoverageAmountCents: Int
  donationAppFeeCents: Int
  "The amount that been raised"
  donationRaisedCents: Int
  "The total count of donations"
  donationsCount: Int
  offlineTotalCents: Int
  onlineTotalCents: Int
  "The count of participants"
  participantCount: Int
  "The donated amount in cents that been raised by emails delivered"
  totalEmailDonations: Int
  "The amount that been raised by miscellaneous forms"
  totalExtraDonations: Int
  totalOtherDonations: Int
  "The donated amount that been raised by sharing through social media"
  totalSocialDonations: Int
  "The donated amount in cents that been raised by text message"
  totalTextDonations: Int
}
"Paginated list of campaigns"
type DriveCampaignList {
  "Total campaigns"
  count: Int
  list: [DriveCampaign]
  offset: Int
}
"Statistics for a set of campaigns in campaign-search."
type DriveCampaignSearchStatistics {
  avgDonationDollars: Float
  avgEmailsDelivered: Float
  avgEmailsPerParticipant: Float
  avgRaisedDollarsPerCampaign: Float
  avgRaisedDollarsPerEmail: Float
  avgTeamSize: Float
  totalCampaign: BigInt
  totalCountDelivered: BigInt
  totalCountParticpants: BigInt
  totalDonations: BigInt
  totalRaisedByEmail: Float
  totalRaisedDollars: Float
}
"Represents a Event object and its attributes"
type DriveEvent {
  "List of messages in the activity feed. Order by posted ascending by default."
  activityFeed: [DriveEventActivityFeed]
  "Activity types for an event. e.g. `football`, 'basketball', etc."
  activityTypes: [String!]
  "Event Agenda which include start-time/end-time of event dates. Should be empty is **agendaConfig.isSameTime** is true"
  agenda: [DriveEventAgendaItem!]
  """
  The configuration for agenda. Format as follows ```JSON { isSameTime: true, startTime: "09:00AM", endTime: "07:00PM" } ``` where **isSameTime** means all event dates will be occured at same **startTime** and **endTime**. If **isSameTime**, **startTime** and **endTime** should be filled-in
  """
  agendaConfig: JSON
  "List of attendees."
  attendees: [DriveEventAttendee!]
  "Booth information for an event."
  booth: DriveEventBooth
  "The user who is the clinic leader."
  clinicLeader: User
  "The user who created the event."
  createdBy: User
  "The description of an event."
  description: String
  "Indicator that event requires a door prize."
  doorPrizedRequired: Boolean
  "The end date of an event."
  endDate: String!
  "The hotel address for the attendees"
  hotelAddress: String
  "The hotel name for the attendees"
  hotelName: String
  "Url for hubspot link."
  hubspotTrackingLink: String
  id: ID!
  "Notes associated for the event."
  notes: String
  "A indicator whether the event had been completed planning phase."
  planningCompleted: Boolean
  "The time zone of an event"
  preferedTimezone: String
  "Description for the sponsorship"
  sponsorDescription: String
  "Indicator whether the door prize has been confirmed."
  sponsorDoorPrized: Boolean
  "The start date of an event."
  startDate: String!
  "The events status."
  status: DriveEventStatus!
  "The tier level for this event. Valid values is `1`, `2` and `3`"
  tier: Int
  "The name of the event."
  title: String!
  "Venue location information for an event."
  venue: DriveVenue
  "Website link for an event."
  websiteLink: String
}
"Information of a activity feed message"
type DriveEventActivityFeed {
  "A list of replies the message"
  children: [DriveEventActivityFeed]
  "The date this post was created"
  createdAt: DateTime
  "The event id"
  eventId: ID!
  "If the message is pinned to the top"
  featured: Boolean
  "The ID of the message"
  id: String!
  "The message posted"
  message: String
  "The date when this post has been updated"
  updatedAt: DateTime
  "A users first name and last and link to profile photo"
  user: User
  "The id of the user who posted the message"
  userId: ID!
}
"Attributes for an events agenda"
type DriveEventAgendaItem {
  description: String
  endTime: DateTime
  id: ID!
  location: String
  startTime: DateTime
  title: String
}
"Represents an events attendee attributes"
type DriveEventAttendee {
  "The department the attendee is in."
  department: String
  "The event ID"
  eventId: ID!
  "The status of an attendee flight request. Default is `NO_REQUEST`."
  flightRequest: EventAttendeeRequestStatus
  "The status of an attendee hotel request. Default is `NO_REQUEST`."
  hotelRequest: EventAttendeeRequestStatus
  id: ID! @deprecated(reason: "Use userId instead")
  "Indication whether this attendee a clinic leader."
  isClinicLeader: Boolean
  "The status of an attendee attendence."
  status: DriveAttendanceStatus!
  "User Information of attendee, includes `firstName`, `lastName`, `email`, etc. defined in UserDirectory."
  user: User
  "The udId of an attendee. e.g. `ud_abc123`"
  userId: ID!
}
"Booth attributes"
type DriveEventBooth {
  "Notes for the booth e.g. Electricity is paid for"
  boothNotes: String
  "The time to end breakdown or clean up event equipment in Venue."
  breakdownEndTime: DateTime
  "The time to start breakdown or clean up event equipment in Venue."
  breakdownStartTime: DateTime
  "Indication whether electricity is provided."
  electricityProvided: Boolean
  "Indication whether flooring is provided."
  flooringProvided: Boolean
  "Indication whether internet is provided."
  internetProvided: Boolean
  "The time to end setup for event"
  setupEndTime: DateTime
  "The time to begin setup for event"
  setupStartTime: DateTime
}
"Paginated list of drive events"
type DriveEventResults {
  events: [DriveEvent!]
  offset: Int
  totalCount: Int
}
"Organization Summary information used by Drive's Get List Application"
type DriveGetListOrganizationSummaryReturn {
  "Entity ID of the organization. Its purpose is for caching in Apollo Client."
  _id: Int @deprecated(reason: "use id")
  activitiesWithoutCampaigns: Int
  averageRaisedPerCampaign: Float
  campaignCompleted: Int
  city: String
  entityId: Int
  hubspotId: String
  id: String
  "Number of campaigns that completed of this Organization"
  name: String
  "Total students of the organization. Inherited from NCES data and studentCount from orgs-api."
  population: Int
  potentialRaised: Float @deprecated(reason: "use potentialRaisedDollars")
  "Total potential raised in dollars for this organization. Equals NCES total students multiply with National RPK."
  potentialRaisedDollars: Float
  saturationPercent: Float @deprecated(reason: "use saturationPercentage")
  "Saturation percentage of organization. It is calculated by divide total logged-in students by NCES total students of this organization."
  saturationPercentage: Float
  state: String
  "Lifetime total raised in dollars of organization. Computed from all non-upcoming campaigns."
  totalRaised: Float
  "Lifetime total raised in cents of organization."
  totalRaisedCents: Int
  type: String
}
type DriveGetlistProspectsReturn {
  offset: Int
  results: [HubspotProspectsDeals]
  totalCount: Int
}
type DriveGetlistWinbackReturn {
  offset: Int
  results: [WinbackDeals]
  totalCount: Int
}
"Group Leader information in Drive"
type DriveGroupLeader {
  "The full name of the group leader"
  name: String
}
type DriveOrgListResults {
  offset: Int
  orgs: [DriveGetListOrganizationSummaryReturn]
  totalCount: Int
}
"Organization used by Drive"
type DriveOrganization {
  activity: String
  city: String
  id: Int
  legalName: String
  name: String
  state: String
}
"List of organizations used by Drive"
type DriveOrganizationList {
  list: [DriveOrganization]!
  offset: Int
}
"Statistics information related to Participants in one campaign"
type DriveParticipantStats {
  "Count of participants who had logged in"
  loggedInCount: Int
  "Count of participants with 20 emails sent"
  participantsWithAtLeastTwentyEmails: Int
  "Percentage of active participants of campaign."
  participationPercent: Int
  "Count of participants with at least one donation"
  withAtLeastOneDonation: Int
}
"Information of a sms scheduled for release"
type DrivePreloadSmsSchedule {
  "The date for release. Valid format is YYYY-MM-DD"
  date: String
  "The campaign id"
  fundraiserId: Int
  "The timeframe for release."
  timeframe: DriveScheduleTimeFrame
  "The timezone for scheduled release"
  timezone: String
}
type DriveUsersResult {
  nextPage: Boolean
  users: [DriveAccountUser]
}
"Venue or location for Event used by Event Clinic"
type DriveVenue {
  "Building name or Room number. e.g. `Room 330`"
  buildingOrRoom: String
  "City of Venue location, e.g. `Dallas`"
  city: String
  id: ID!
  "Venue Name. e.g. `Hillton Convention Center`, e.g."
  name: String!
  "The short-code for US States. e.g. `TX`, 'CA'"
  state: String
}
type EasypostWebhookResponse {
  error: String
  success: Boolean!
}
type EditSubmissionDocumentResultObject {
  s3FileName: String!
}
type EmailLog {
  body: String
  from: String
  id: ID!
  messageId: String
  sentAt: Date
  subject: String
  to: String
}
"Information of a emails for a camapgin"
type EmailsInfo {
  "Total emails bounced back due to failed delivery"
  bounced: Int
  "Total emails loaded by particpants"
  loaded: Int
  "The schedule (timezone-sensitive) thatthe emails are scheduled to release"
  scheduledReleaseDate: ScheduledReleaseInfo
  "Total emails sent"
  sent: Int
  "The datetime the emails were sent out"
  sentAt: DateTime
}
type Event {
  activity: String
  activityLevel: String
  activityType: String
  author: String
  bus_fee: Float
  bus_time: String
  cancellation_status: String
  comments: String
  conference: String
  conference_event_id: Int
  conference_id: Int
  confirmed: String
  confirmedStatusBoolean: Boolean
  contract: String
  created_at: Date
  departure_location: String
  directions: String
  early_dismissal_required: String
  early_dismissal_time: String
  end_time: String
  estimated_return_time: String
  event: String
  eventOfficials: [Official]
  eventTiming: String
  event_date: Date
  event_id: Int!
  exists_in_mls: Int
  fee: Float
  formattedEventDate: String
  formattedEventDateSystem: String
  formattedEventDay: String
  g_s: String
  gate_revenue: Float
  groupval: String
  headline: String
  impact_event: String
  isDuplicate: Boolean
  lead: String
  location: String
  loss_points: Int
  num_buses: Int
  opponent: String
  opponent_code: String
  opponent_score: String
  picture: String
  place: String
  prep_setup: String
  promote: String
  results: String
  revenue: Float
  rollover: String
  rolloverStatusBoolean: Boolean
  seasonInfo: String
  seasonSportCode: String
  season_team: Int
  sportCode: String
  sportDescription: String
  sportGender: String
  sportLevel: String
  sportName: String
  start_time: String
  team_score: String
  title: String
  tournament: String
  trans_id: Int
  transportDetails: String
  transport_comments: String
  transportation: String
  update_at: Date
  vehicle_count: Int
  web_dir: String
  weekdayname: String
  win_points: Int
  years: String
}
type EventArchiveResult {
  errors: [String]
  event: ScheduleEvent
}
type EventContract {
  comments: String
  event_contract_number: Int
  id: Int
  main_event_id: Int
  participant: String
  signee_name: String
  signees: [ContractSignees]
  years: String
}
type EventOfficialReport {
  comments: String
  event_id: String
  facility: String
  officials: [EventOfficials]
  opponent: String
  team: String
}
type EventOfficials {
  address: String
  cell_phone: String
  city: String
  duty: String
  email: String
  first_name: String
  home_phone: String
  last_name: String
  official_id: String
  paid: String
  pay_code: String
  received: String
  salary: Float
  ssn: String
  work_phone: String
  zip: String
}
type EventParticipants {
  SchoolName: String
  contract_received: String
  event_id: Int
  id: Int!
  notes: String
  paid: String
  school_code: String
}
type EventPreparations {
  comments: String
  event: Int
  id: Int!
  prep: String
  qty: String
}
type EventResult {
  data: JSON
  event: ScheduleEvent!
  id: ID!
}
type EventResultArchiveResult {
  errors: [String]
  eventResult: EventResult
}
type EventResultCreateResult {
  errors: [String]
  eventResult: EventResult
}
type EventResultUpdateResult {
  errors: [String]
  eventResult: EventResult
}
type EventResultsUpdateResult {
  errors: [String]
  eventResults: [EventResult]
}
type EventTransportDetails {
  driver_name: String
  driver_phone: String
  event_date: Date
  event_id: Int
  id: Int!
  vehicle_id: String
  vehicle_type: String
}
type EventTransportDetailsDeleteManyCount {
  count: Int
}
type EventsOpponent {
  activity: String
  confirmed: String
  end_time: String
  eventId: Int
  event_date: Date
  eventsOpponentId: Int
  season_years: String
  start_time: String
}
type ExportFile {
  content: String
  fileName: String
}
type ExternalBankAccount {
  accountId: String!
  accountType: AccountType
  bankName: String!
  campaignId: String!
  last4: String!
}
type Facility {
  Address1: String
  Address2: String
  City: String
  Date: Date
  Facility_id: Int
  State: String
  ZipCode: String
  directions: String
  indoor: String
  is_deleted: Boolean
  location_id: Int
  ml_site_id: Int
  ml_space: String
  ml_space_id: Int
  picture: String
  place_name: String!
  show: String
  specialDirections: String
  web: String
}
type FacilityLocation {
  Address1: String
  Address2: String
  City: String
  State: String
  ZipCode: String
  directions: String
  location_id: Int
  place_name: String
}
type Family {
  children: [User]
  parents: [User]
}
type FinancialAccount {
  acctId: ID
  applicationId: ID
  city: String
  country: String
  createdAt: DateTime!
  customerId: ID
  id: ID!
  kybId: ID!
  paymentMethodId: ID
  processor: Processor!
  recipientName: String
  settlementMethod: SettlementMethod
  stateCode: StateCode
  status: FinAcctStatus!
  street: String
  streetLine2: String
  stripeEnv: FinAcctStripeEnv!
  updatedAt: DateTime
  zipCode: String
}
type FinancialAcctOrg {
  acctId: ID
  applicationId: ID
  city: String
  country: String
  createdAt: DateTime
  customerId: ID
  finAcctId: ID
  kybId: ID
  orgEin: String
  orgId: ID
  orgName: String
  orgType: OrgTypeWithCampaign
  paymentMethodId: ID
  processor: Processor
  recipientName: String
  settlementMethod: SettlementMethod
  stateCode: StateCode
  status: FinAcctStatus
  street: String
  streetLine2: String
  stripeEnv: FinAcctStripeEnv
  updatedAt: DateTime
  zipCode: String
}
type FinancialAcctUser {
  acctId: ID
  applicationId: ID
  authorizationCreatedAt: DateTime
  city: String
  country: String
  customerId: ID
  finAcctId: ID
  isBeneficialOwner: Boolean
  isContact: Boolean
  isPrincipal: Boolean
  isRepresentative: Boolean
  kybId: ID
  kycId: ID
  orgId: ID
  personId: ID
  processor: Processor
  recipientName: String
  settlementMethod: SettlementMethod
  stateCode: StateCode
  status: FinAcctStatus
  street: String
  streetLine2: String
  userId: ID
  zipCode: String
}
"Owned by Vault Duplicated by Wallet Used in legacy createCard"
type FinancialAddress {
  city: String
  line1: String
  line2: String
  state: String
  zip: String
}
type Fundraiser {
  blockedDonationInviteDomains: [String!]
  description: String
  donationReceiptCustomText: String
  emailInvitesReleasedAt: Date
  emailInvitesScheduledAt: Date
  endDate: Date
  goal: Int
  groupLeaders(where: FundraisersGroupLeadersWhereInput): [FundraiserGroupLeader!]
  id: ID!
  links: JSON
  name: String
  origin: FundraiserOrigin
  raiseId: Int
  slug: String
  smsInvitesReleasedAt: Date
  smsInvitesScheduledAt: Date
  startDate: Date
  status: FundraiserStatus
}
type FundraiserActivationEligibilityResponse {
  fundraiserActivationEligibility: Boolean!
  fundraiserActivationEligibilityFailedReasons: [String!]
  id: String!
}
type FundraiserApprovalSubmission {
  additionalNotes: String
  campaignRaiseId: Int
  createdAt: DateTime!
  customerSupportInfo: String
  duration: String
  estimatedTotalRaisedCents: Int
  firstName: String
  formId: Int!
  formType: FundraiserApprovalSubmissionFormType
  fundraiserApprovalSubmissionDocs: [FundraiserApprovalSubmissionDocument!]!
  fundraiserApprovers: [FundraiserApprover!]!
  fundraiserName: String!
  fundraiserServiceWebsite: String
  groupSize: Int
  howThisWorks: String
  id: Int!
  lastName: String
  latestRevision: Boolean!
  newFundraiser: Boolean!
  organizationId: String
  organizationName: String!
  previousRevision: Int
  pricingAndFees: String
  processorId: String!
  programId: String
  proposedStartDate: DateTime
  reason: String
  revisionRequest: String
  safety: String
  status: FundraiserApprovalSubmissionStatus!
  submittedAt: DateTime!
  submitterId: String
  updatedAt: DateTime!
}
type FundraiserApprovalSubmissionDocument {
  createdAt: DateTime
  filename: NonEmptyString
  filetype: NonEmptyString
  id: Int
  location: NonEmptyString
  s3Filename: NonEmptyString
  status: NonEmptyString
  updatedAt: DateTime
}
type FundraiserApprovalSubmissionResult {
  errors: [String]!
  fundraiserApprovalSubmission: FundraiserApprovalSubmission
  status: String!
}
type FundraiserApprovalSubmissionsOrgNames {
  fundraiserName: String!
  organizationName: String!
  programName: String!
}
type FundraiserApprover {
  approverType: FundraiserApproverType
  createdAt: DateTime
  email: String
  firstName: String
  id: Int
  lastName: String
  phone: String
  roleId: String
  updatedAt: DateTime
  userId: String
}
type FundraiserBlockedDonationInviteDomains {
  domain: String!
  id: ID!
}
type FundraiserCheerwall {
  cheers: [Cheer]
  count: Int
}
type FundraiserCustomRewardsByLevel {
  id: Int
  name: String
  sizes: String
}
type FundraiserCustomRewardsData {
  rewards: [FundraiserCustomRewardsByLevel]
  rewardsLevel: Int!
}
type FundraiserDataByCoachJoinCode {
  coachJoinCode: String
  entityId: Int
  id: Int
  joinCode: String
  logo: String
  name: String
  status: String
}
type FundraiserDriveInvitesDetail {
  email: FundraisersInvitesReleaseDetail
  fundraiserId: ID!
  fundraiserStatus: FundraiserStatus
  participants: FundraiserDriveParticipantsDetail
  sms: FundraisersInvitesReleaseDetail
}
type FundraiserDriveParticipantsDetail {
  loggedInCount: Int!
  participantsJoinedCount: Int!
}
type FundraiserEntityResource {
  entityId: Int
}
type FundraiserFinalizationEligibilityResponse {
  fundraiserFinalizationEligibility: Boolean!
  fundraiserFinalizationEligibilityFailedReasons: [String!]
  id: String!
}
type FundraiserGroup {
  id: ID!
  label: String
}
type FundraiserGroupLeader {
  id: ID!
  isPrimary: Boolean
  raiseUserId: Int
  user: User
  userDirectoryId: String
}
type FundraiserManagerAlert {
  createdAt: Date!
  fundraiserManagerId: ID!
  id: ID!
  readAt: Date
  type: FundraiserManagerAlertType!
  updatedAt: Date!
}
type FundraiserParticipantPublic {
  "Raise Participant Id"
  id: Int!
  participant: ParticipantPublic
}
type FundraiserParticipantSmsDonationInvitesAddResponse {
  errors: JSON
  smsInvites: [ParticipantSmsDonationInvite!]
}
"""
@deprecated(reason: "Use participantsPublic instead")
"""
type FundraiserParticipants {
  id: Int!
  participant: User
}
type FundraiserRaisedAmount {
  subtotalCents: Int
}
type FundraiserRewardLevelsCount {
  levelsCount: Int
}
type FundraiserRewardsProduct {
  default: Boolean
  fitting: String
  id: Int
  image: String
  name: String
  sizes: String
  tier: Int
}
type FundraiserSetupProgress {
  nextIncompletedSection: String
  sections: [FundraiserSetupProgressSection!]!
}
type FundraiserSetupProgressSection {
  currentStep: Int!
  id: ID!
  section: String!
  status: FundraiserSetupProgressStatus!
  totalSteps: Int!
}
type FundraiserTopDonation {
  donorName: String
  subtotalCents: Int
}
type FundraiserUserIncentiveID {
  id: Int
}
type FundraiserUserRole {
  isGroupLeader: Boolean
  isParticipant: Boolean
  roles: [String]
}
type FundraisersGroupLeaderDonationInvite implements IFundraisersDonationInvite {
  emailAddress: String
  fundraiserId: ID!
  id: ID!
  lastEmailNumber: Int
  lastEmailSentAt: String
  participantId: ID
  status: DonationInviteStatus
}
type FundraisersInvitesReleaseDetail {
  bounced: Int
  delivered: Int
  releasedAt: Date
  scheduleData: JSON
  scheduledAt: Date
  sent: Int!
  total: Int!
  type: FundraiserInvitesReleaseType
}
type FundraisersMediaGallery {
  fundraiserId: String!
  id: String!
  image: String
  mediaType: FundraiserMediaType!
  position: Int
  url: String
}
type FundraisersParticipantDonationInvite implements IFundraisersDonationInvite {
  emailAddress: String
  fundraiserId: ID!
  groupLeaderId: ID
  id: ID!
  lastEmailNumber: Int
  lastEmailSentAt: String
  status: DonationInviteStatus
}
type FundraisersUserDoesNotHaveAccess implements FundraisersError {
  message: String!
}
type FundraisersUserNotLoggedIn implements FundraisersError {
  message: String!
}
type GalleryItems {
  campaignLogo: String
  campaignLogoThumb: String
  carouselItems: [CarouselItem]
}
type Gender {
  code: String
  createdAt: ISO8601DateTime
  createdBy: Int
  id: ID!
  name: String
  updatedAt: ISO8601DateTime
  updatedBy: Int
}
type GiftShopPurchaseIntentData {
  donateIntentId: String
  donorEmail: String
  donorFirstName: String
  donorId: String
  donorLastName: String
  fundraiserUserId: Int
  id: String!
  lineItems: [DonorGiftShopItem!]!
  participantUserDirectoryId: String
  purchaseIntentId: String
  raiseFundraiserId: Int
  status: String
  userId: Int
}
type GiftShopPurchaseIntentId {
  id: String!
}
type GroupLeader {
  email: String
  id: ID!
  name: String
  phone: String
}
"See CampaignMembership for details"
type GroupLeaderCampaign {
  basicStatus: BasicCampaignStatus!
  donationLink: String!
  entityId: Int
  goalInDollars: Int
  id: ID!
  isCocoach: Boolean!
  joinCode: String
  "Getting Kyc status adds time to your query **expensive**"
  kyc: CampaignKyc
  name: String!
  "Primary group leader"
  primary: PrimaryGroupLeader
  raiseUserJoinedAt: String
  "Getting roster adds time to your query"
  roster: [Roster]
  status: CampaignStatus!
  teamSize: Int
  totalCentsRaised: Int
  whyDonateText: String
}
type GroupLeaderDonationInvitesAddManyResponse {
  errors: JSON
  invites: [GroupLeaderDonationInvite!]!
}
type GroupLeaderDonationLevels {
  amount: Float!
  id: ID!
  impact: String
  rewardDescription: String
}
type GroupLeaderDonationStats {
  _id: String
  count: Int!
  donorsCount: Int
  hasResignLineage: Boolean
  otkPurchaseTotal: Int
  participantsWithDonationCount: Int
  totalCents: Int!
}
type GroupLeaderEmailDonationInvite implements GroupLeaderDonationInvite {
  createdAt: Date!
  deliveryStatus: EmailDeliveryStatus!
  emailAddress: String!
  id: ID!
  status: DonationInviteStatus
}
type GroupLeaderEmailsSent {
  donorThankYouEmailSent: Boolean
  participantCongratulationEmailsSent: Boolean
}
type GroupLeaderFundraiser {
  BlockedFundraiserDonationInviteDomains: [FundraiserBlockedDonationInviteDomains!]
  DonationLevels: [GroupLeaderDonationLevels!]
  FundraiserAcknowledgement: GroupLeaderFundraiserAcknowledgement
  FundraiserDonations: GroupLeaderFundraiserDonations
  FundraiserEmailInvites: GroupLeaderFundraiserDonationEmailInvites
  FundraiserFinancials: GroupLeaderFundraiserFinancials
  FundraiserParticipants: GroupLeaderFundraiserParticipantsResponse
  FundraiserSmsInvites: GroupLeaderFundraiserDonationSmsInvites
  GearLogoConfiguration: GroupLeaderGearLogoConfiguration
  MediaGallery: [GroupLeaderMediaGallery!]
  SetupProgress: FundraiserSetupProgress
  ShippingAddress: GroupLeaderFundraiserAddress
  activity: String
  basicStatus: FundraiserBasicStatus
  deprecatedRaiseEntityId: Int @deprecated(reason: "This field is only used for redirecting to legacy raise. It will be removed in the future.")
  earlyStartDateRequested: Boolean
  emailInvitesReleasedAt: Date
  endDate: Date
  fundraiserFeatures: GroupLeaderFundraiserFeatures
  fundraiserManagers: [GroupLeaderFundraiserManager!]
  goal: Float
  groupLeadersEmailSent: GroupLeaderEmailsSent
  howDonationsAreUsed: String
  id: ID
  joinCode: String
  logo: String
  name: String
  orgsId: String
  origin: FundraiserCreationOrigin
  participantGroups: [GroupLeaderParticipantGroup!]
  participantJoinData: ParticipantJoinData
  personalMessage: String
  primaryColor: String
  raiseId: Int
  secondaryColor: String
  slug: String
  smsInvitesReleasedAt: Date
  startDate: Date
  status: FundraiserStatus
  storeOrder: GroupLeaderStoreOrderResult
  stripeConnectAccountId: String
  tier: String
  whyDonations: String
}
type GroupLeaderFundraiserAcknowledgement {
  agreedToTerms: Boolean
  documentContent: String!
  fundraiserId: ID
  id: ID!
  signedAt: Date
  status: FundraiserAcknowledgementStatus!
  typedSignature: String
}
type GroupLeaderFundraiserAddress {
  addressType: FundraiserAddressType
  attentionTo: String
  city: String
  country: String
  editable: Boolean
  fundraiserId: ID!
  id: ID
  region: String
  streetLine1: String
  streetLine2: String
  zip: String
}
type GroupLeaderFundraiserDonationEmailInvites {
  fundraiserId: String!
  legacyDonorsInvitesCount: Int!
  myInviteCount: Int!
  reachedParticipantsInviteGoalCount: Int!
}
type GroupLeaderFundraiserDonationSmsInvites {
  fundraiserId: String!
  reachedParticipantsInviteGoalCount: Int!
}
type GroupLeaderFundraiserDonations {
  byLegacyDonors: GroupLeaderDonationStats
  byResignedFundraisers: GroupLeaderDonationStats
  bySelf: GroupLeaderDonationStats
  fundraiserId: ID!
  total: GroupLeaderDonationStats
}
type GroupLeaderFundraiserFeatures {
  hasCustomRewards: Boolean
  hasGuardianCampaign: Boolean
  hasOtk: Boolean
  hasRewards: Boolean
  hasTippingEnabled: Boolean
  id: ID!
  storeUrl: String
  waCampaignSetup: Boolean
}
type GroupLeaderFundraiserFinancials {
  externalOrderId: String
  fundraiserId: ID!
  hasBankAccount: Boolean
  id: ID!
  kycStatus: FundraiserKycStatus!
  settlementCheckAddress: GroupLeaderFundraiserAddress
  settlementDate: Date
  settlementMethod: FundraiserSettlementMethod!
  settlementMethodEditable: Boolean
  vaultKycStatus: FundraiserVaultKycStatus
}
type GroupLeaderFundraiserGear {
  id: ID!
  image: String!
  name: String!
}
type GroupLeaderFundraiserManager {
  fundraiserId: ID!
  id: ID
  lastLoginAt: Date
  role: FundraiserManagersRole
}
type GroupLeaderFundraiserNotFound implements GroupLeaderErrorMessage {
  message: String!
}
type GroupLeaderFundraiserParticipant {
  deletedAt: Date
  donationsRaised: GroupLeaderParticipantDonationRaisedResponse
  email: String
  emailsLoadedCount: Int
  firstName: String
  fullName: String
  guardianFullName: String
  id: ID!
  lastLoginAt: Date
  lastName: String
  missingUser: Boolean!
  participantGroupId: String
  profilePicture: String
  textsLoadedCount: Int
}
type GroupLeaderFundraiserParticipantUpdate {
  deletedAt: Date
  id: ID!
  lastLoginAt: Date
  participantGroupId: String
}
type GroupLeaderFundraiserParticipantsResponse {
  fundraiserId: String!
  participantsCount: Int!
  participantsJoinedCount: Int!
  profilePhotoUploadedCount: Int!
  signedInCount: Int!
}
type GroupLeaderFundraisersPaginated {
  fundraisers: [GroupLeaderFundraiser]!
  pagination: GroupLeadersPagination
}
type GroupLeaderGearLogoConfiguration {
  approvedAt: Date
  changeRequestDescription: String
  changesRequestedAt: Date
  fundraiserId: ID!
  gearLogoImageDigital: String
  gearSample: [GroupLeaderFundraiserGear!]
  id: ID!
}
type GroupLeaderList {
  count: Int
  cursor: String
  list: [GroupLeader]!
}
type GroupLeaderMediaGallery {
  fundraiserId: String!
  id: String!
  image: String
  mediaType: FundraiserMediaType
  position: Int
  url: String
}
type GroupLeaderNotFound implements GroupLeaderErrorMessage {
  message: String!
}
type GroupLeaderParticipantDonationRaisedResponse {
  count: Int!
  totalDollars: Int!
}
type GroupLeaderParticipantGroup {
  fundraiserId: ID!
  goal: Int
  id: ID!
  name: String!
}
type GroupLeaderPermissionDenied implements GroupLeaderErrorMessage {
  message: String!
}
type GroupLeaderRoster {
  id: ID!
  name: String!
  userDirectoryMembers: [GroupLeaderRosterMembers!]
}
type GroupLeaderRosterMembers {
  email: String
  firstName: String
  fullName: String
  id: ID
  initials: String
  lastName: String
  phoneNumber: String
  profilePicture: String
}
type GroupLeaderSmsDonationInvite implements GroupLeaderDonationInvite {
  createdAt: Date!
  deliveryStatus: EmailDeliveryStatus!
  id: ID!
  phoneNumber: String!
  status: DonationInviteStatus
}
type GroupLeaderStoreOrderResult {
  createdAt: Date!
  id: String!
  netsuiteId: String
  shippingReceivedAt: Date
  status: String
  trackingNumber: String
  trackingUrl: String
  updatedAt: Date!
}
type GroupLeaderValidationError implements GroupLeaderErrorMessage {
  message: String!
}
type GroupLeadersPagination {
  currentPage: Int
  totalCount: Int
  totalPages: Int
}
type GroupsOverviewDashboardResponse {
  count: Int!
  overview: [SpendGroupsOverview]
}
type HmAccountRole {
  account_id: Int
  id: String!
  role_id: Int
}
type HmGraphSales {
  soldDuringWeek: Int
  weekName: String
}
type HmSalesRep {
  rep_email: String
  sales_rep: String
}
type HmSpendAccount {
  id: Int
  orgTeamMembers: [String]
}
type HmSpendData {
  credited: Int
  inactiveCards: Int
  paid: Int
  pastDue: Int
  pending: Int
  upcoming: Int
}
type HmSponsorData {
  activeSponsorships: Int
  assetCount: Int
  available: Int
  sponsorShipValue: Int
  totalAssets: Int
}
type HmStore {
  active_stores: String
  monthly_sales: [HmGraphSales]
  total_orders: String
  total_points: String
  total_sales: String
}
type HmUser {
  email: String
  id: Int!
  name: String
}
"Hubspot Engagement entity"
type HubspotCallEngagement {
  contactIds: [String]
  dealIds: [String]
  engagementId: String
  ownerId: String
  type: String
}
type HubspotProspectsDeals {
  activity: String
  dealName: String
  dealStage: String
  id: String
  isProspectPinned: Boolean
  lastActivityDate: String
  leaderFirstName: String
  leaderLastName: String
  projectedLaunchDate: String
}
"Common payload for mutations."
type IMutationResult {
  data: JSON
  message: String
}
type InitiateProfileChallengeResponse {
  challenge: UserChallenge!
  success: Boolean!
}
type InsAddPreApprovedContactsResult {
  contactsResult: [InsPreApprovedContactResult]
  errors: [String]
  status: String!
}
type InsAnalyticsSummaryStat {
  amount_raised_cents: Int!
  campaign_id: Int!
  month: Int!
  year: Int!
}
type InsCampaignDonation {
  amount_cents: Int
  campaign_id: Int!
  campaign_name: String
  created_at: DateTime
  donor_name: String
  id: Int!
  participant_name: String
  slug: String!
}
type InsCampaignRaiseEntityInfo {
  campaign_id: Int!
  entity_id: Int
}
type InsCampaignSettlement {
  amount_cents: Int
  campaign_id: Int!
  campaign_name: String
  id: Int!
  last_updated: DateTime
  method: String
  slug: String!
  status: String
}
type InsCampaignStat {
  donations_count: Int
  end_date: DateTime
  forecasted_amount_cents: Int
  group_leader_email: String
  group_leader_name: String
  id: Int!
  insights_status: String
  name: String
  participants: Int
  participation: Float
  preloading: Float
  slug: String!
  start_date: DateTime
  total_raised_cents: Int
}
type InsCampaignsData {
  analyticsSummaryStats: [InsAnalyticsSummaryStat]
  campaignStats: [InsCampaignStat]
  donationMapStats: [InsDonationMapStat]
  inviteStats: [InsInviteStat]
  lTRChart: InsLTRChartData
  monthlyCampaignStats: [InsMonthlyCampaignStat]
}
type InsDeletePreApprovedContactResult {
  errors: [String]
  status: String!
}
type InsDonationMapStat {
  campaign_id: Int
  campaign_ids: [Int]
  donations_amount_cents: Int
  donations_count: Int
  lat: Float
  long: Float
  postal_code: String
}
type InsDonorParticipantContact {
  campaign_id: Int!
  donor_email: String
  donor_id: Int
  donor_name: String
  participant_email: String
  participant_id: Int
  participant_name: String
}
type InsEditPreApprovedContactResult {
  error: String
  status: String!
}
type InsEditPreApprovedInvite {
  activity: String!
  createdBy: String!
  email: String!
  firstName: String!
  inviteStatus: String!
  lastName: String!
  orgName: String!
  phoneNumber: String
}
type InsEditPreApprovedInviteResult {
  errors: [String]
  status: String!
  updatedInvite: InsEditPreApprovedInvite
}
type InsEmailTestPreApprovedContactResult {
  email: String!
  status: String!
}
type InsInviteStat {
  campaign_id: Int
  campaign_ids: [Int]
  invite_count: Int!
  invite_type: String!
  total_amount_cents: Int!
}
type InsLTRChartData {
  activeCampaignsCount: Int
  activeCampaignsParticipation: Float
  activeCampaignsTotalCents: Int
  avgDonationCents: Int
  closedCampaignsCount: Int
  closedCampaignsParticipation: Float
  closedCampaignsTotalCents: Int
  ltr: Int
  upcomingCampaignsCount: Int
  upcomingCampaignsForecastedAmountCents: Int
  upcomingCampaignsParticipantSignIn: Float
}
type InsMonthlyCampaignStat {
  campaign_ids: [Int]!
  month: Int!
  participation: Float
  preloading: Float
  year: Int!
}
type InsOrg {
  id: Int!
  name: String
}
type InsOrgCampaignSummary {
  campaignsCount: Int!
  orgId: String
  totalRaisedCents: Int!
}
type InsPreApprovedContact {
  activity: String
  associated_org_id: String
  associated_org_name: String
  email: String
  first_name: String
  id: Int
  invite_phone_number: String
  invite_sent_at: DateTime
  invite_status: String
  last_name: String
}
type InsPreApprovedContactResult {
  email: String!
  status: String!
}
type InsResendPreApprovedContactResult {
  errors: [String]
  status: String!
}
type InsSalesRepInfo {
  email: String!
  featured_image: String
  hubspot_calendar_link: String
  phone_number: String
  post_title: String
}
type InsSetNotificationPreferencesResult {
  errors: [String]
  messages: [String]
  status: String!
}
type InsSetUserPreferencesResult {
  errors: [String]
  messages: [String]
  status: String!
}
type InsUser {
  id: Int!
}
type InsightsGetUserPreference {
  campaign_raise_id: Int
  id: Int!
  org_id: String
  user_id: String
}
type InvalidCampaignPersonListData {
  email: String!
  errorMessage: String
}
type InvalidCampaignSmsInviteData {
  errorMessage: String!
  phoneNumber: String
}
type InvalidToken implements Error {
  message: String!
}
type InviteError implements Error {
  message: String!
}
type InviteFundraiserResponse {
  errors: JSON
  invitesSent: [InviteFundraiserSentResponse]
}
type InviteFundraiserSentResponse {
  dateSent: Date
  id: String!
  invite: String
  inviteType: JoinFundraiserInviteType
  status: JoinFundraiserInviteStatus!
}
type InviteInfo {
  email: String
  flow: String
  flowParams: JSONObject
  id: ID
  joincode: String
  params: UserInviteParams
  phoneNumber: String
  requester: User
  status: String
  type: InviteType
}
type InviteList {
  acceptedAt: String
  createdAt: String
  email: String
  expiresAt: String
  id: String
  joincode: String
  params: UserInviteParams
  phoneNumber: String
  requester: User
  status: UserInviteStatus
  type: InviteType
}
type InviteNotFound implements Error {
  message: String!
}
type InvitesListResponse {
  invites: [InviteList!]
  pagination: Pagination!
}
type InvitesResponse {
  invites: [InviteInfo!]!
}
type JoinedFundraiserID {
  "Returns the ID of the fundraiser"
  id: Int
}
type Leader {
  createdAt: DateTime!
  isConfirmed: Boolean
  title: TeamTitle!
  updatedAt: DateTime
}
type LeagueKeeperDocument {
  description: String
  docType: String
  id: ID!
  order: Int
  path: String
  sportName: String
  url: String
}
type LeagueKeeperEvent {
  activity: String
  conferenceEvent: Boolean
  date: ISO8601DateTime
  eventType: String
  gameComplete: String
  id: ID!
  level: LeagueKeeperLevel
  location: String
  loser: Int
  scorePlus: Boolean
  startTime: String
  staticSite: Boolean
  summary: String
  team1: School
  team1Name: String
  team1Score: String
  team2: School
  team2Name: String
  team2Score: String
  team3: School
  team3Name: String
  tie: Boolean
  title: String
  winner: Int
  years: String
}
type LeagueKeeperLevel {
  id: ID!
  level: String
  name: String
  sortOrder: Int
}
type LegacyTransactionsOutput {
  count: Int!
  transactions: [Transaction]!
}
type Level {
  ID: Int!
  Level: String
  is_deleted: Boolean
}
type MDMDetails implements DriveUser {
  email: String
  firstName: String
  lastName: String
  phone: String
}
type MagentoOrder {
  baseDiscountAmount: Float
  baseGrandTotal: Float
  baseShippingAmount: Float
  baseSubtotal: Float
  baseTaxAmount: Float
  createdAt: ID
  customerEmail: String
  customerFirstName: String
  customerLastName: String
  discountAmount: Float
  grandTotal: Float
  incrementId: String
  items: [OrderItem]
  mageworxRewardPointsAmount: Float
  orderId: BigInt
  scopeId: BigInt
  shippingAmount: Float
  state: String
  status: String
  storeDashbordStatus: String
  subtotal: Float
  taxAmount: Float
  totalPaid: Float
  updatedAt: String
}
type MagentoOrders {
  orders: [MagentoOrder]
}
type MagentoPointActivities {
  activities: [MagentoPointActivity]
}
type MagentoPointActivity {
  comment: String
  createdAt: String
  customerId: BigInt
  customerName: String
  expirationDate: String
  orderIncrementId: String
  pointsBalance: Float
  pointsDelta: Float
  receiver: MagentoPointReceiver
  scopeId: BigInt
  sender: MagentoPointSender
  status: String
  storeCode: String
  storeName: String
  teamName: String
  transactionId: BigInt
  transactionType: String
}
type MagentoPointReceiver {
  email: String
  name: String
  receiverId: Int
}
type MagentoPointSender {
  email: String
  name: String
  senderId: Int
}
type MagentoStore {
  accountManagerId: String
  activityType: String
  baseTotalSales: Float
  brands: [Brand]
  campaignId: Int
  city: String
  code: String
  digitalLogo: String
  domain: String
  earnedPoints: Float
  earnedPointsBalance: Float
  earnedSharedPoints: Float
  email: String
  embroideryLogo: String
  enabled: Int
  entityId: BigInt
  expiringPoints: Float
  favicon: String
  givenPoints: Float
  givenSharedPoints: Float
  groupLeaderEmail: String
  hatLogo: String
  headerLogo: String
  heroSliderVariableId: BigInt
  homepagePageId: String
  managerId: BigInt
  name: String
  ncesId: String
  orgId: Int
  organizationName: String
  parentStoreId: Int
  pointsPercentage: Int
  primaryColor: String
  productColors: [ScopeProductColor]
  productPricePercentage: Int
  programId: Int
  raiseId: Int
  salesRepId: String
  secondaryColor: String
  state: String
  status: String
  storeId: BigInt
  storevariablesId: BigInt
  teamName: String
  totalDiscountAmount: Float
  totalOrders: BigInt
  totalPoints: Float
  totalSales: Float
  zipCode: Int
}
type MagentoStoreManager {
  email: String
  firstName: String
  groupId: Int
  lastName: String
  managerId: BigInt
  points: Float
  scopeId: BigInt
}
type MagentoStorePointsEarned {
  endDate: String
  startDate: String
  totalPoints: Float
}
type MagentoStoresPointsEarned {
  points: [MagentoStorePointsEarned]
}
type MagentoTransactionWebhookResponse {
  error: String
  success: Boolean
}
type ManageCoach {
  adId: Int
  coachId: Int
  createdAt: Date
  createdBy: Int
  firstName: String
  headCoach: Boolean
  isApproved: Boolean
  lastName: String
  photoId: Int
  schoolId: Int
  seasonId: Int
  summary: String
  title: String
  updatedAt: Date
  updatedBy: Int
}
type ManageCoachList {
  count: Int
  list: [ManageCoach]
  skip: Int
  take: Int
}
type ManageEvent {
  "Type of activity or sport."
  activity: String
  "Author or writer of the event story."
  author: String
  "Fee associated with transportation or bus."
  busFee: Int
  "Scheduled time for the bus or transportation."
  busTime: String
  "Status indicating event's cancellation."
  cancellationStatus: String
  "Additional comments or notes."
  comments: String
  "Conference details or name."
  conference: String
  "Event ID related to the conference."
  conferenceEventId: Int
  "ID associated with the conference."
  conferenceId: Int
  "Confirmation status of the event."
  confirmed: String
  "Contract details or identifier."
  contract: String
  "Timestamp when the event was created."
  createdAt: Date
  "Timestamp when the event result was created."
  createdResultAt: String
  "User ID of the creator of the event result."
  createdResultBy: Int
  "Location for event departure."
  departureLocation: String
  "Link for directions or map."
  directionLink: String
  "Directions or map details for the event."
  directions: String
  "Indication if early dismissal is required."
  earlyDismissalRequired: String
  "Scheduled time for early dismissal."
  earlyDismissalTime: String
  "End time of the event."
  endTime: String
  "Estimated time of return post event."
  estimatedReturnTime: String
  "Indication if the event is complete."
  eventComplete: Int
  "Date of the event."
  eventDate: Date
  "Date and Time for start of event."
  eventDateTime: String
  "Calculated field from event date indicating day of the week."
  eventDay: String
  "Calculated field from event ID for event details."
  eventDetailsPath: String
  "The unique ID of the event."
  eventId: Int
  "Lead or main summary of the event."
  eventLead: String
  "Unique ID for the event result."
  eventResultID: Int
  "Detailed story or description of the event."
  eventStory: String
  "Title or main heading of the event story."
  eventTitle: String
  "Type or category of the event."
  eventType: String
  "Indication if event exists in MLS system."
  existsInMls: Int
  "Fee or cost associated with the event."
  fee: Int
  "Revenue generated from gate or entry fee."
  gateRevenue: Int
  "Gender specification for the event."
  gender: String
  "Value grouping multiple events."
  groupVal: String
  "General status of the event."
  gs: String
  "Main headline or caption for the event."
  headline: String
  "Indication if the event has major impact."
  impactEvent: String
  "Indication if event details are approved."
  isApproved: Boolean
  "Lead information or summary."
  lead: String
  "Level of the event."
  level: String
  "Location description or address."
  location: String
  "Points deducted for a loss."
  lossPoints: Int
  "Number of buses or transportation units."
  numBuses: Int
  "Name or description of the opponent."
  opponent: String
  "Code identifier for the opponent."
  opponentCode: String
  "Opponent details or description."
  opponentForEvent: ManageOpponent
  "Score achieved by the opponent team."
  opponentScore: String
  "Outcome or result of the event."
  outcome: String
  "URL or path to event picture."
  picture: String
  "Location or venue of the event."
  place: String
  "Details for event setup or preparation."
  prepSetup: String
  "Indication if event should be promoted."
  promote: String
  "Indication if score should be reported."
  reportScore: Int
  "Indication if story related to event should be reported."
  reportStory: Int
  "Results or outcomes from the event."
  results: String
  "Total revenue generated from the event."
  revenue: Int
  "Indication if event rolls over to next season."
  rollover: String
  "Season details or year."
  season: String
  "Associated team for the season."
  seasonTeam: Int
  "Indication if event should be showcased on front page."
  showFrontPage: Int
  "Start time used for sorting."
  sortStartTime: String
  "Start time of the event."
  startTime: String
  "Name of the associated team."
  teamName: String
  "Score achieved by the home team."
  teamScore: String
  "Title or heading of the event."
  title: String
  "Indication if the event is part of a tournament."
  tournament: String
  "Unique ID for transportation details."
  transId: Int
  "Comments related to transportation."
  transportComments: String
  "Transportation details for the event."
  transportation: String
  "Date when event result was last updated."
  updateResultDate: String
  "Timestamp of the last update to the event."
  updatedAt: Date
  "Timestamp of the last update to the event result."
  updatedResultAt: String
  "User ID of the last updater of the event result."
  updatedResultBy: Int
  "Web directory or path for the event."
  webDir: String
  "Points awarded for a win."
  winPoints: Int
  "Year(s) associated with the event."
  years: String
}
type ManageEventList {
  calendarDate: String
  count: Int
  list: [ManageEvent]
  skip: Int
  take: Int
}
"A type representing details about an opponent in the context of school management."
type ManageOpponent {
  "The name of the athletic director (AD) associated with the school/opponent."
  adName: String
  "The physical address of the school."
  address: String
  "The city where the school is located."
  city: String
  "The primary contact email address for the school."
  email: String
  "The fax number for the school, if available."
  fax: String
  "A flag indicating if the opponent's record has been marked as deleted."
  isDeleted: Boolean
  "The National Center for Education Statistics (NCES) identifier for the school."
  ncesId: String
  "A flag indicating if the entity is not an actual school."
  nonSchool: Boolean
  "The primary contact phone number for the school."
  phone: String
  "The unique identifier for the school. This is mandatory."
  schoolCode: String!
  "The official name of the school."
  schoolName: String
  "The state or province where the school is located."
  state: String
  "The postal code for the school's address."
  zip: String
}
"The object which contains all the input data for the manage organization query"
type ManageOrganization {
  "The ad of the school"
  ad: String
  "AD Program ID"
  adProgramID: Int
  "Ad report"
  adReport: Int
  "The ad school of the school"
  adSchool: Int
  "The address of the school"
  address: String
  "Announcements for the organization"
  announcementsForOrganization(filter: ManageResourceAnnouncementFilter): ManageResourceAnnouncementList
  "AOTM Spotlight"
  aotmSpotlight: Int
  "Auto Approve Score Only"
  autoApproveScoreOnly: Int
  "Camp Registration Confirmation Text"
  campRegConfirmationTxt: String
  "The city of the school"
  city: String
  coachForOrganization(filter: ManageCoachListFilter): ManageCoachList
  "The First Color of the school"
  color1: String
  "The Second Color of the school"
  color2: String
  "Conference Name"
  conferenceName: String
  "Conference URL"
  conferenceURL: String
  "Custom Label"
  customLabel: String
  "Custom Label 2"
  customLabel2: String
  "Custom Link"
  customLink: String
  "Custom Link 2"
  customLink2: String
  "The Database folder of the school"
  dbFolder: String
  "Email Blast Enabled"
  emailBlastEnabled: Int
  "Email Spotlight"
  emailSpotlight: Int
  "Equipment Enabled"
  equipmentEnabled: Boolean
  "Event Locations for the organization"
  eventLocationsForOrganization(filter: ManageResourceEventLocationFilter): ManageResourceEventLocationList
  "Facebook Url"
  facebookUrl: String
  "The fax number of the school"
  fax: String
  "The featured of the school"
  featured: Int
  "Feeders Towns for the school"
  feederTowns: String
  "Gallery Spotlight"
  gallerySpotlight: Int
  "Has Activities"
  hasActivities: Boolean
  "Has Access OTM"
  hasAotm: Boolean
  "Has Athletics"
  hasAthletics: Boolean
  "Has Email Blast"
  hasEmailBlast: String
  "Has Facebook Url"
  hasFacebookUrl: Boolean
  "Has Instagram Url"
  hasInstagramUrl: Boolean
  "Has Migrated From Access To Sql Server"
  hasMigratedFromAccessToSqlServer: Boolean
  "Has Registration"
  hasRegistration: Boolean
  "Has Twitter Url"
  hasTwitterUrl: Boolean
  "The icon of the school"
  iCon: String
  "Instagram Url"
  instagramUrl: String
  "League Keeper school id"
  lKschoolId: Int
  "The latitude of the school"
  lat: Float
  "The id of the link to school database"
  linkSchoolId: Int
  "The logo of the school"
  logo: String
  "The longitude of the school"
  long: Float
  "The mascot of the school"
  mascot: String
  "Does the school have email updates"
  massUpdate: Int
  "Master School Id"
  masterSchoolId: Int
  "The message of the school"
  message: String
  "Motto"
  motto: String
  "Old School Id"
  oldSchoolId: Int
  "Pages for the organization"
  pagesForOrganization(filter: ManageResourcePagesFilter): ManageResourcePagesList
  "The url path for the school"
  path: String
  "The phone number of the school"
  phone: String
  "Photos for the Organization"
  photosForOrganization(filter: ManageResourcePhotosFilter): ManageResourcePhotosList
  playersForOrganization(filter: ManagePlayerListFilter): ManagePlayerList
  "The principal of the school"
  principal: String
  programsForOrganization(filter: ManageProgramListFilter): ManageProgramList
  "PS School Id"
  psSchoolId: String
  "PS Store Active"
  psStoreActive: Int
  "The Registration Email of the school"
  registrationEmail: String
  "The Registration Enabled of the school"
  registrationEnabled: Int
  "The Registration Policy of the school"
  registrationPolicy: String
  "The Registration Type of the school"
  registrationType: Int
  "The id of the school"
  schoolId: Int
  "The name of the school"
  schoolName: String
  "The show of the school"
  show: Int
  "Show Ad"
  showAd: Boolean
  "Show School Name And Motto"
  showSchoolNameAndMotto: Boolean
  "Do you want to show the teams pages first on the site"
  showTeamPagesFirst: Int
  "A Sign up code for the school"
  signUpCode: String
  "The site type of the school"
  siteType: String
  "Sport Registration Confirmation Text"
  sportRegConfirmationTxt: String
  "The id of the sql database"
  sqlBase: Int
  "Staff Calendar Enabled"
  staffCalendarEnabled: Int
  "The state of the school (abbreviation)"
  state: String
  "The state organization of the school"
  stateOrg: String
  "Time Zone"
  timeZone: String
  "Twitter Url"
  twitterUrl: String
  "Urgent Announcements for the organization"
  urgentAnnouncementsForOrganization(filter: UrgentAnnouncementFilter): ManageResourceAnnouncementList
  "The web folder of the school that you have chosen"
  webFolder: String
  "Web Password"
  webPassword: String
  "The website of the school"
  webSite: String
  "The zip code of the school"
  zip: String
}
type ManageOrganizationList {
  count: Int
  list: [ManageOrganization]
  skip: Int
  take: Int
}
"The object which contains all the input data for each player"
type ManagePlayer {
  ch1: Int
  ch2: Int
  ch3: Int
  city: String
  custodyCode: Int
  dayPhone: String
  dob: Date
  fName: String
  feePaid: Int
  gender: String
  gradYear: String
  hatsize: String
  height: String
  homePhone: String
  hospitalPhone: String
  insuranceCompany: String
  insurancePolicyNum: String
  jersey: String
  lName: String
  livesWithCode: Int
  noPress: Int
  permission: Int
  physical: Int
  physicalDate: Date
  physician: String
  physicianTelephone: String
  position: String
  preferredHospital: String
  rosterId: Int
  schoolId: Int
  seasonId: Int
  shirtsize: String
  shortsize: String
  siteStudentId: Int
  stAddress: String
  state: String
  status: String
  studentId: String
  weight: String
  zip: String
}
type ManagePlayerList {
  count: Int
  list: [ManagePlayer]
  skip: Int
  take: Int
}
type ManageProgram {
  announcementsForPrograms(filter: ManageResourceAnnouncementFilter): ManageResourceAnnouncementList
  gender: String
  groupVal: String
  homeField: String
  id: Int
  level1: String
  photosForPrograms(filter: ManageResourcePhotosFilter): ManageResourcePhotosList
  seasonsForProgram(filter: ManageSeasonListFilter): ManageSeasonList
  sportCode: String
  sportDescription: String
  sportName: String
}
type ManageProgramList {
  count: Int
  list: [ManageProgram]
  skip: Int
  take: Int
}
"The object which contains all the input data for the ManageResource announcement query"
type ManageResourceAnnouncement {
  active: String
  announcement: String
  createdAt: Date
  createdBy: Int
  emailFilter: String
  emailRequest: Int
  endDate: Date
  id: Int
  imageId: Int
  isApproved: Boolean
  onFront: Int
  recordStatus: Int
  schoolId: Int
  showUrgentFrom: Date
  showUrgentUntil: Date
  sortVal: Int
  sport: String
  sportName: String
  startDate: Date
  title: String
  updatedAt: Date
  updatedBy: Int
  urgent: Boolean
}
type ManageResourceAnnouncementList {
  count: Int
  list: [ManageResourceAnnouncement]
  skip: Int
  take: Int
}
"The object which contains all the input data for the ManageResource event location query"
type ManageResourceEventLocation {
  "The fileName for the event location"
  fileName: String
  "The sport gender and level for the event location"
  gVal: String
  "The id for the event location"
  id: Int
  "The isApproved for the event location"
  isApproved: Int
  "The type for the event location"
  lType: String
  "The link for the event location"
  link: String
  "The name for the event location"
  name: String
  "The onFornt for the event location"
  onFront: Int
  "The recordStatus for the event location"
  recordStatus: Int
  "The school id for the event location"
  schoolNum: Int
  "The sortOrder for the event location"
  sortOrder: Int
}
type ManageResourceEventLocationList {
  count: Int
  list: [ManageResourceEventLocation]
  skip: Int
  take: Int
}
"The object which contains all the input data for the ManageResource Pages query"
type ManageResourcePages {
  createdAt: Date
  createdBy: Int
  id: Int
  name: String
  pageContent: String
  pageTitle: String
  schoolId: Int
  updatedAt: Date
  updatedBy: Int
}
type ManageResourcePagesList {
  count: Int
  list: [ManageResourcePages]
  skip: Int
  take: Int
}
"The object which contains all the input data for the ManageResource photos query"
type ManageResourcePhotos {
  batch: String
  caption: String
  companyId: Int
  createdAt: Date
  createdBy: Int
  filename: String
  group: String
  id: Int
  image: Buffer
  layout: String
  level: String
  needsApproval: Int
  projectId: Int
  recordStatus: Int
  rphoto: Int
  school: Int
  sport: String
  src: String
  title: String
  updatedAt: Date
  updatedBy: Int
}
type ManageResourcePhotosList {
  count: Int
  list: [ManageResourcePhotos]
  skip: Int
  take: Int
}
"The object which contains all the input data for each roster"
type ManageRoster {
  fName: String
  gradYear: String
  height: String
  jersey: String
  lName: String
  level: String
  participantId: String
  position: String
  sortJersey: Int
  weight: String
}
type ManageRosterList {
  count: Int
  list: [ManageRoster]
}
type ManageSeason {
  budget: Float
  coachForProgramSeason(filter: ManageCoachListFilter): ManageCoachList
  defaultTimeForEvent: String
  defaultTimeForPractice: String
  eventsForSeason(filter: ManageEventListFilter): ManageEventList
  homeField: String
  isDeleted: Boolean
  playersForProgramSeason(filter: ManagePlayerListFilter): ManagePlayerList
  preview: String
  rosterForProgramSeason: ManageRosterList
  season: String
  seasonId: Int
  sportCode: String
  upcomingEventsForSeason: ManageEventList
  webPassword: String
  year: String
}
type ManageSeasonList {
  count: Int
  list: [ManageSeason]
  skip: Int
  take: Int
}
type ManageUser {
  contracts: String
  events: String
  groups: [String]
  id: Int
  is_deleted: Boolean
  is_super_user: String
  maintenance: String
  password: String
  seasons: String
  teams: String
  user_email: String
  user_id: String
  user_level: String
}
type ManageUsersList {
  items: [ManageUser]
  limit: Int
  offset: Int
  totalFilteredItems: Int
  totalPages: Int
  totalRows: Int
}
type MarcoWebhookResponse {
  error: String
  success: Boolean!
}
type MessageContactDetailResponse {
  createdAt: Date!
  status: ActivityStatus!
}
type MessageContactResponse {
  activity: [MessageContactDetailResponse!]!
  attributes: JSON!
  id: String!
  to: String!
}
type MessagesUser {
  id: ID!
  image: String
  name: String
}
type MessagesUsers {
  users: [MessagesUser!]!
}
type MissingArguments implements Error {
  message: String!
}
type ModifyDeleteCount {
  count: Int
}
type Mutation {
  activateStore(active: Boolean!, magentoStoreCode: String, magentoStoreId: Int, storeUrl: String): StoreResult!
  addParticipantGuardianDonationInviteEmailsV2(emails: [String]): [ParticipantGuardianReturn!]!
  "Add revision request. New submission will be created. Email will be sent to submitter."
  addRevisionFundraiserApprovalSubmission(revision: NonEmptyString!, submissionId: Int, token: String): RevisionFundraiserApprovalSubmissionResult!
  "Approve submission using approver's token. Email will be sent to submitter."
  approveFundraiserApprovalSubmission(submissionId: Int, token: String): ApproveFundraiserApprovalSubmissionResult!
  campaignPersonListBulkCreate(emails: [String!]!, fundraiserId: Int!, participantUserId: Int!): CampaignPersonListEntries
  campaignPersonListCreate(contextableId: Int!, contextableType: String!, email: String, participantUserId: Int!, personListType: String!, phone: String): CampaignPersonList @deprecated(reason: "Will be using campaignPersonListBulkCreate instead.")
  campaignPersonListEntryDelete(personListEntryId: Int!): CampaignPersonListEntryDelete
  "Create saved search filters for campaigns for current user."
  campaignSearchFilterCreate(filterCriteria: JSON, filterName: String): CampaignSearchFilter
  "Remove saved search filters for campaigns for current user."
  campaignSearchFilterDelete(id: Int): CampaignSearchFilter
  campaignSmsInviteAdd(fundraiserId: Int!, participantUserId: Int!, phoneNumbers: [String!]!): CampaignSmsDataResponse
  campaignSmsInviteDelete(fundraiserId: Int!, smsInviteId: Int!): CampaignSmsInviteDeleteResponse!
  "Create and update content"
  commsRegister(code: String!, emailProvider: SupportedEmailProviders, from: String, fromName: String, id: String, mjml: String, name: String, service: SnapService, serviceUserId: String, smsProvider: SupportedSmsProviders, subject: String, testData: JSON, text: String, transport: MessageTransportType, type: MessageType, unsubscribeGroupId: Int, unsubscribeGroups: [Int]): CommsRegisterResponse!
  "Send message"
  commsSend(contacts: [MessageContact]!, sendAt: Date, templateAttributes: JSON, templateId: String, templateName: String, workflowId: String): CommsSendResponse!
  "Send Test Message"
  commsTest(attributes: JSON, challenge: Boolean, mjml: String, templateId: String, text: String): CommsTestResponse!
  "Edit document Status associated to a Fundraiser Approval Submission Form"
  completeFundraiserApprovalSubmissionDocumentUpload(approvalSubmissionId: Int!, s3FileName: String!): completeFundraiserApprovalSubmissionDocumentUploadResult!
  confirmPassword(email: String!, newPassword: String, verificationCode: String!): String
  "Deletes a custom template based off ID"
  contactTemplateDelete(id: Int): CustomContactTemplates
  "Saves custom contact-template for current user"
  contactTemplateSave(templateMessage: String, templateName: String, templateSubject: String): CustomContactTemplates
  cpDeleteEventResult(
    "Parameters for CpDeleteEventResult"
    input: CpDeleteEventResultInput!
  ): CpDeleteEventResultPayload
  cpUpdateEventResult(
    "Parameters for CpUpdateEventResult"
    input: CpUpdateEventResultInput!
  ): CpUpdateEventResultPayload
  createCard(card: IssueCardInput!, gateway: FINANCIAL_GATEWAY,
    "organizationId will be replaced by raiseOrganizationId"
    organizationId: String,raiseOrganizationId: String  ): CoreCardFields! @deprecated(reason: "This will be replaced with latestCreateCard after data has been migrated from Spend and Raise")
  """
  @deprecated(reason: "Use createChildFundraiserParticipantV2 instead")
  """
  createChildFundraiserParticipant(input: CreateChildFundraiserParticipantInput!): JoinedFundraiserID!
  createChildFundraiserParticipantV2(input: CreateChildFundraiserParticipantInput!): JoinedFundraiserID!
  createFundraiserApprovalSubmission(additionalNotes: String, campaignRaiseId: Int, customerSupportInfo: String, duration: String, estimatedTotalRaisedCents: Int, firstName: String, formType: FundraiserApprovalSubmissionFormType, fundraiserApprovalSubmissionDocs: [FundraiserApprovalSubmissionDocumentInput], fundraiserApprovers: [FundraiserApproverInput], fundraiserServiceWebsite: String, groupSize: Int, howThisWorks: String, lastName: String, newFundraiser: Boolean, organizationId: String, pricingAndFees: String, programId: String, proposedStartDate: DateTime, reason: String, revisionRequest: String, safety: String, submitterId: NonEmptyString!): FundraiserApprovalSubmissionResult!
  createFundraiserUser(firstName: String!, lastName: String!, phoneNumber: String): CreatedFundraiserUser
  createOtkParticipantEntry(fundraiserId: Int!, incentiveId: Int!, size: String!): CreatedOtkEntry
  createProduct(product: ProductInput): ProductResult
  createStore(overrideSkipLogic: Boolean = false, store: StoreInput!): StoreResult!
  createStoreOrder(order: OrderInput, restartWorkflow: Boolean = false): OrderResult
  "Create the log record with type DEBUG"
  debug(payload: JSON!, service: AuditLogService!, source: AuditLogSource!): Boolean
  "Delete documents from fundraiser approval submission. If document is not linked to previous submission (history) it will be removed from S3 as well."
  deleteFundraiserApprovalSubmissionDocument(approvalSubmissionId: Int!, documentKey: String!): String!
  "Create new donate intent. The status with created always INITIATED"
  donorDonateIntentCreate(input: DonorDonateIntentCreateInput!): DonateIntent
  "Update the status of donate intent to be PROCESSING. This is called when the donation was successfully sent to Stripe, but we are waiting for an charge.succeeded event from Stripe to finalize the payment."
  donorDonateIntentMarkProcessing(data: DonorDonateIntentUpdateInput, id: ID!): DonateIntent
  "Update donate intent as user update the payment form. The status with created always become PENDING."
  donorDonateIntentUpdate(browserSecret: String, id: ID!, input: DonorDonateIntentUpdateInput!): DonateIntent
  donorsAdhocGiftShopIntentAdd(intentInput: AdhocGiftShopIntentInput!, item: PurchaseItemInput!): GiftShopPurchaseIntentId!
  donorsAlumni(input: AlumniDonor!): AlumniDonorSchoolDetails
  "create an offline donation"
  donorsDonationCreateOffline(offlineDonationData: DonorDonationOfflineInput!): DonorOfflineDonation
  donorsDonationDelete(id: ID!): DonorDonation
  donorsDonationUpdate(data: DonorDonationUpdateInput!, id: ID!): DonorDonation
  donorsFundraiserPayableRefund(input: DonorFundraiserPayableRefundInput!): DonorPayableRefundResponse
  donorsGiftShopIntentAdd(intentInput: GiftShopIntentInput!, item: PurchaseItemInput!): GiftShopPurchaseIntentId!
  donorsGiftShopIntentAddMany(intentInput: GiftShopIntentInput!, items: [PurchaseItemInput!]!): GiftShopPurchaseIntentId!
  donorsGiftShopIntentMarkProcessing(purchaseIntentId: String!): GiftShopPurchaseIntentData!
  donorsGiftShopIntentRemove(incentiveId: Int!, purchaseIntentId: String!): GiftShopPurchaseIntentId!
  donorsGiftShopIntentUpdate(item: PurchaseItemInput!, purchaseIntentId: String!, status: PurchaseIntentStatus!): GiftShopPurchaseIntentId!
  donorsGiftShopIntentUpdateIntent(intentInput: GiftShopIntentUpdateInput!, purchaseIntentId: String!): GiftShopPurchaseIntentData!
  donorsGiftShopPurchaseUpdate(data: PurchaseUpdateInput!, purchaseId: String!): DonorFundraiserPurchase
  donorsReceiptsResend(transactions: [TransactionInput!]!): ResendReceiptsResult!
  driveAddHubspotProspectPin(dealId: String, hubspotOrgId: String): String
  driveAddWinbackPin(dealId: String, hubspotOrgId: String): String
  "Finalize the (closed) fundraiser after campaign settled."
  driveCampaignFinalize(fundraiserId: Int!): IMutationResult
  "Settle the fundraiser when campaigns start to be closed campaigns."
  driveCampaignSettle(fundraiserIds: [Int!]): IMutationResult
  "Update the fundraiser. Right now only fundraiser notes can be updated."
  driveCampaignUpdate(data: DriveCampaignUpdateInput!,
    "Fundraiser Id"
    id: Int!
  ): IMutationResult
  driveDisableActivity(dealId: String, hubspotOrgId: String): String @deprecated(reason: "use driveDisableOrgActivity")
  driveDisableOrgActivity(activityId: String, activityType: String, campaignsCount: Int, curricularType: String, hubspotOrgId: String): String
  driveEnableActivity(dealId: String, hubspotOrgId: String): String @deprecated(reason: "use driveEnableOrgActivity")
  driveEnableOrgActivity(activityId: String, hubspotOrgId: String): String
  "Posts a message in the activity feed"
  driveEventActivityCreate(eventId: String!, input: DriveEventActivityInput): DriveEventActivityFeed
  "Removeds an activity feed message"
  driveEventActivityRemove(id: ID!): String
  "Updates the message and feature toggle in a activity message"
  driveEventActivityUpdate(id: ID!, input: DriveEventActivityInput!): DriveEventActivityFeed
  "Takes in 2 arguments to post the agendas"
  driveEventAgendaCreate(eventId: ID!, input: DriveEventAgendaInput!): DriveEventAgendaItem
  "Adds an attendee to event."
  driveEventAttendeeAdd(eventId: ID!, input: DriveEventAttendeeInput!,
    "Set true to force existing attendee record update with values defined in `input`"
    update: Boolean = false
  ): DriveEventAttendee
  "Updates attendance information of attendee."
  driveEventAttendeeUpdate(eventId: ID!, input: DriveEventAttendeeInput!, userId: ID!): DriveEventAttendee
  "Removes an attendee from event."
  driveEventAttendeesRemove(eventId: ID!, userIds: [String!]!): Int
  "Create Event (Drive Event-Tracker)"
  driveEventCreate(input: DriveEventInput!): DriveEvent
  "Delete Event (Drive Event-Tracker)"
  driveEventRemove(id: ID!): String
  "Update Event (Drive Event-Tracker)"
  driveEventUpdate(id: ID!, input: DriveEventInput!): DriveEvent
  "Set tracking by specific organization current user."
  driveOrgUserTrackingSet(
    "Input fields needed to create tracking row"
    input: DriveTrackingInput,
    "Org ID of the organization. It is mapped with **'fields.id'** in **'Org'** entity"
    orgId: String!
  ): driveOrgUserTrackingReturn
  "Cancel the scheduled preload sms"
  drivePreloadSmsScheduleCancel(fundraiserId: Int!): String
  "Send out preload sms to all participants"
  drivePreloadSmsSend(fundraiserId: Int!,
    "Schedule of sms release in the future. If not provided, it will send immidiately under 15min"
    schedule: DriveScheduleInput
  ): String
  drivePreloadSmsSendScheduleUpdate(fundraiserId: Int!, schedule: DriveScheduleUpdateInput!): String
  driveRemoveHubspotProspectPin(dealId: String): String
  driveRemoveWinbackPin(dealId: String): String
  "Resizes the uploaded attachment in S3 in Drive."
  driveS3ImageResize(fileUploaded: String): String
  driveShareProjectedRaisedResults(activityType: String!, earlyAccessApprovedFundsMax: Int!, earlyAccessApprovedFundsMin: Int!, location: String!, participantCount: Int!, projectedRaisedMax: Int!, projectedRaisedMin: Int!, to: String!): IMutationResult
  "Generate S3 generate pre-signed url for Drive to upload. This is used for uploading big attachments to the support ticket"
  driveUploadUrlCreate(fileName: String!): String!
  easypostWebhook(status: EasypostEventInput!): EasypostWebhookResponse!
  editFundraiserApprovalSubmission(additionalNotes: String, campaignRaiseId: Int, customerSupportInfo: String, duration: String, estimatedTotalRaisedCents: Int, firstName: String, formType: FundraiserApprovalSubmissionFormType, fundraiserApprovalSubmissionDocs: [FundraiserApprovalSubmissionDocumentInput]!, fundraiserApprovers: [FundraiserApproverInput]!, fundraiserServiceWebsite: String, groupSize: Int, howThisWorks: String, id: Int!, lastName: String, newFundraiser: Boolean, organizationId: String, pricingAndFees: String, programId: String, proposedStartDate: DateTime, reason: String, revisionRequest: String, safety: String, status: FundraiserApprovalSubmissionStatus): FundraiserApprovalSubmissionResult!
  "Create the log record with type ERROR"
  error(payload: JSON!, service: AuditLogService!, source: AuditLogSource!): Boolean
  escalateRaiseStoreBuild(fundraiserId: Int!): StoreResult!
  eventCreate(input: CreateEventInput): Event
  eventDelete(input: DeleteEventInput): Event
  eventModify(input: ModifyEventInput): Event
  eventParticipantsDeleteMany(input: DeleteManyEventParticipantsInput): DeleteCount
  eventParticipantsUpsert(input: UpsertEventParticipantsInput): UpsertEventParticipantsCount
  eventPreparationCreate(input: CreateEventPreparationInput): DailyCalendarPreparation
  eventPreparationDelete(input: DeleteEventPreparationInput): DailyCalendarPreparation
  eventPreparationModify(input: ModifyEventPreparationInput): DailyCalendarPreparation
  eventPreparationsCreateMany(input: [EventPreparationsInput]): [EventPreparations]
  eventPreparationsDeleteMany(input: DeleteManyEventPreparationsInput): ModifyDeleteCount
  eventPreparationsModifyMany(input: ModifyManyEventPreparationsByEventInput): ModifyDeleteCount
  eventPreparationsUpsert(input: UpsertEventPreparationsInput): UpsertEventPreparationsCount
  eventResultArchive(id: ID!, origin: String!): EventResultArchiveResult
  eventResultCreate(eventId: ID!, eventResult: EventResultInput!, origin: String!): EventResultCreateResult
  eventResultUpdate(eventResult: EventResultInput!, eventResultId: ID!, origin: String!): EventResultUpdateResult
  eventResultsUpdate(eventResultIds: [ID]!, eventResults: [EventResultInput]!, origin: String!): EventResultsUpdateResult
  eventTransportDetailsDeleteMany(input: DeleteManyEventTransportDetailsInput): EventTransportDetailsDeleteManyCount
  eventTransportDetailsUpsertMany(input: UpsertEventTransportDetailsInput): UpsertEventTransportDetailsCount
  facilitiesUpsert(input: [UpsertFacilitiesInput]): [FacilityLocation]
  facilityCreate(input: CreateFacilityInput): Facility
  facilityDelete(input: DeleteFacilityInput): Facility
  facilityModify(input: ModifyFacilityInput): Facility
  "Associate a User with a FinancialAccount. This will also create a User Node only if they exist in User Directory but not Orgs."
  finAcctUserAssociationCreate(finAcctId: ID!, isBeneficialOwner: Boolean = null, isContact: Boolean = null, isPrincipal: Boolean = null, isRepresentative: Boolean = null, kycId: ID = null, personId: ID = null, userId: ID!): MutationResponse!
  "Add a deleted_at property to a User's FinancialAccount association. This will NOT delete the relationship."
  finAcctUserAssociationDelete(finAcctId: ID!, userId: ID!): MutationResponse!
  "Update a User's FinancialAccount association properties. This mutation will re-activate a user that has been deleted by default. **Note** There is a known bug preventing status Booleans from updating from True to False."
  finAcctUserAssociationUpdate(finAcctId: ID!, isBeneficialOwner: Boolean = null, isContact: Boolean = null, isPrincipal: Boolean = null, isRepresentative: Boolean = null, kycId: ID = null, personId: ID = null, userId: ID!): MutationResponse!
  "Create a processor specific FinancialAccount Node that is associated with an Org. This requires a valid Org id and a valid KYB id"
  financialAcctCreate(acctId: ID = null, applicationId: ID = null, customerId: ID = null, kybId: ID!, orgId: ID!, processor: Processor!, status: FinAcctStatus! = PENDING, stripeEnv: FinAcctStripeEnv! = RAISE): MutationResponse!
  """
  Change a FinancialAccount status to "TERMINATED". This will NOT delete the node.
  """
  financialAcctDelete(finAcctId: ID!): MutationResponse!
  "Update a FinancialAccount properties that is already associated with an Org."
  financialAcctUpdate(acctId: ID = null, applicationId: ID = null, city: String = null, country: String = null, customerId: ID = null, finAcctId: ID!, kybId: ID = null, orgId: ID!, paymentMethodId: ID = null, recipientName: String = null, settlementMethod: SettlementMethod = null, stateCode: StateCode = null, status: FinAcctStatus = null, street: String = null, streetLine2: String = null, zipCode: String = null): MutationResponse!
  fundraiserParticipantUpdate(input: FundraiserParticipantUpdateInput!, raiseFundraiserId: Int!): ParticipantFundraiserParticipant!
  fundraiserStoreUrlUpdate(fundraiserId: Int!, storeUrl: String): UpdatedFundraiserStoreUrl
  fundraisersDonationInviteDelete(id: ID!): Boolean
  fundraisersDonationInviteUpdate(id: ID!, input: FundraisersDonationInviteUpdateInput!): FundraisersDonationInvite
  fundraisersSmsDonationInvitesRelease(fundraiserId: ID!): Boolean!
  fundraisersSmsDonationInvitesReleaseSchedule(fundraiserId: ID!, schedule: FundraiserReleaseSmsInvitesScheduleInput): Boolean!
  fundraisersSmsDonationInvitesReleaseScheduleCancel(fundraiserId: ID!): Boolean!
  "update the list of blocked email domains for a fundraiser for donation invites"
  groupLeaderBlockedFundraiserDonationInviteDomainsUpdate(fundraiserId: ID!, input: GroupLeaderBlockedDomainsUpdateInput!): [FundraiserBlockedDonationInviteDomains!]
  groupLeaderDonationInviteUpdate(fundraiserId: ID!, input: GroupLeaderDonationInviteContactUpdateInput!): GroupLeaderDonationInvite
  groupLeaderDonationInvitesAddMany(contacts: [String!]!, fundraiserId: ID!, inviteType: GroupLeaderDonationInviteType!): GroupLeaderDonationInvitesAddManyResponse
  groupLeaderDonationInvitesRemoveMany(fundraiserId: ID!, ids: [ID!]!, inviteType: GroupLeaderDonationInviteType!): [ID]!
  "Send out email to group-leader. Support CC emails"
  groupLeaderEmailSend(
    "List of CC emails"
    ccEmails: [String!],fundraiserId: Int!,
    "Email of group leader of campaign. This is also main recipient"
    groupLeaderEmail: String!,
    "Message in email. Plain text only."
    message: String!,
    "The subject of email"
    subject: String!
  ): IMutationResult
  groupLeaderFundraiserAcknowledgementSign(fundraiserId: ID!, input: GroupLeaderFundraiserAcknowledgementSignInput!): GroupLeaderFundraiserAcknowledgement
  groupLeaderFundraiserAddressUpdate(addressType: FundraiserAddressType!, fundraiserId: ID!, input: GroupLeaderFundraiserAddressInput!): GroupLeaderFundraiserAddress
  groupLeaderFundraiserDonationLevelsUpdate(fundraiserId: ID!, input: [GroupLeaderDonationLevelsInput!]!): [GroupLeaderDonationLevels!]
  "edit a fundraiser financials information associated with the group leader"
  groupLeaderFundraiserFinancialsUpdate(fundraiserId: ID!, input: GroupLeaderEditFundraiserFinancialsInput!): GroupLeaderFundraiserFinancials
  groupLeaderFundraiserManagerUpdate(fundraiserId: ID!, input: GroupLeaderFundraiserManagerUpdateInput!): GroupLeaderFundraiserManager
  groupLeaderFundraiserMediaGalleryUpdate(fundraiserId: ID!, input: GroupLeaderFundraiserMediaGalleryUpdateInput!): [GroupLeaderMediaGallery]
  groupLeaderFundraiserParticipantsUpdate(fundraiserId: ID!, input: GroupLeaderParticipantsUpdateInput!): [GroupLeaderFundraiserParticipantUpdate!]
  "edit a fundraiser associated with the group leader"
  groupLeaderFundraiserUpdate(fundraiserId: ID!, input: GroupLeaderEditFundraiserInput!): GroupLeaderFundraiser
  "update the gear logo configuration to approve the logo or request changes"
  groupLeaderGearLogoConfigurationUpdate(fundraiserId: ID!, input: GroupLeaderGearLogoUpdateInput!): GroupLeaderGearLogoConfiguration
  groupLeaderMarkFundraiserManagerAlertAsRead(alertId: ID!): FundraiserManagerAlert!
  groupLeaderParticipantGroupDelete(id: ID!): ParticipantGroupMutationResult!
  groupLeaderParticipantGroupUpdate(fundraiserId: ID!, input: UpdateParticipantGroupInput!): ParticipantGroupMutationResult!
  groupLeaderParticipantJoinFundraiserInvite(fundraiserId: String!, inviteSource: JoinFundraiserInviteSource!, inviteType: JoinFundraiserInviteType!, invites: [String!]!): InviteFundraiserResponse!
  groupLeaderParticipantJoinFundraiserInviteUpdate(fundraiserId: ID!, input: JoinFundraiserInviteUpdateInput!, inviteSource: JoinFundraiserInviteSource, inviteType: JoinFundraiserInviteType): InviteFundraiserResponse!
  groupLeaderSendEmails(fundraiserId: ID!, input: GroupLeaderEmailInput!): GroupLeaderEmailsSent
  helpDocumentDelete(input: HelpDocumentDeleteInput!): String
  helpDocumentUpload(input: HelpDocumentUploadInput!): String
  "Update hubspot call engagement for fundraiser"
  hubspotCallEngagementToDealAdd(fundraiserId: Int!): HubspotCallEngagement
  "Update hubspot note engagement for fundraiser"
  hubspotNoteEngagementToDealAdd(fundraiserId: Int!): HubspotCallEngagement
  "Create the log record with type INFO"
  info(payload: JSON!, service: AuditLogService!, source: AuditLogSource!): Boolean
  insightsAddPreApprovedContacts(approver: InsAddPreApprovedContactApproverInput!, contacts: [InsPreApprovedContactInput]!, dashboardUrl: String!, org: InsAddPreApprovedContactOrgInput!): InsAddPreApprovedContactsResult!
  insightsDeletePreApprovedContact(contactId: Int!, userId: String!): InsDeletePreApprovedContactResult!
  insightsEditPreApprovedContact(contactId: Int!, orgId: String!, senderName: String!, updatedContact: InsEditPreApprovedContactInput!): InsEditPreApprovedContactResult!
  insightsEditPreApprovedContactInvite(token: String): InsEditPreApprovedInviteResult!
  insightsResendInvitePreApprovedContact(contactId: Int!, senderName: String!): InsResendPreApprovedContactResult!
  insightsSetNotificationPreferences(preferences: [InsSetNotificationPreferences]!, userId: String!): InsSetNotificationPreferencesResult!
  insightsSetUserPreferences(campaignIds: [Int], orgIds: [String], setTo: SetUserPreferenceFlag!, userId: String!): InsSetUserPreferencesResult!
  inviteAccept(inviteId: String): UserInviteResponse
  inviteCreate(email: String!, parent: InviteParentArguments, sendEmail: Boolean = true, type: InviteType, user: InviteUserArguments): String!
  inviteDelete(inviteId: String): String
  inviteResend(inviteId: String): InviteInfo
  joinFundraiserUser(joinCode: String!, joinType: JoinType, role: CampaignMemberAssociation!): JoinedFundraiserID!
  joinFundraiserUserById(fundraiserId: Int!, importType: FundraiserUserImportType, role: CampaignMemberAssociation!, ssoId: String!, user: JoinFundraiserUserInput!): JoinedFundraiserID!
  latestVaultCardCreate(altName: String, attachCardToKybBalance: Boolean!, cardType: VaultCardType!, kybId: ID!, metadata: VaultCardMetadataInput, recipientName: String, shippingAddress: VaultAddressInput, shippingService: VaultShippingService, spendingLimitAmount: Int!, spendingLimitInterval: VaultSpendingLimitInterval!): ID!
  levelCreate(input: CreateLevelInput): Level
  levelDelete(input: DeleteLevelInput): Level
  levelModify(input: ModifyLevelInput): Level
  "Create the log record. The most universal and agnostic request."
  log(payload: JSON!, service: AuditLogService!, source: AuditLogSource!, type: AuditLogType!): Boolean
  login(consumer: String, email: String!, password: String!): AuthChallenge
  logout(consumer: String): Boolean
  magentoRefundWebhook(magentoRefund: MagentoRefundInput): MagentoTransactionWebhookResponse
  magentoTransactionWebhook(magentoEvent: MagentoConsumerPointsLiabilityInput): MagentoTransactionWebhookResponse
  manageUserCreate(input: ManageUserCreateInput): ManageUser
  manageUserDelete(input: ManageUserDeleteInput): ManageUser
  manageUserUpdate(input: ManageUserUpdateInput): ManageUser
  marcoWebhook(status: MarcoEventInput!): MarcoWebhookResponse!
  messagesChannelAddModerators(id: String!, moderatorIds: [String!]!): ChatChannelAddModeratorsResult
  messagesChannelAddUsers(id: String!, userIds: [String!]!): ChatChannelAddUsersResult
  messagesChannelCreate(assignmentId: String, audience: ChannelAudienceEnum, description: String, isReadOnly: Boolean, message: String, moderatorIds: [String], name: String!, orgId: String, type: ChannelTypesEnum!, userIds: [String]!): ChatChannelCreateResult
  messagesChannelInviteAccept(inviteId: ID!, userId: String!): MessagesChannelInviteAcceptResult
  messagesChannelInviteLinkRevoke(code: String!): MessagesChannelInviteLinkRevokeResult
  messagesChannelInviteMembers(contacts: [InviteMemberContactInput!]!, id: ID!, users: [InviteMemberUsersInput!]!): MessagesChannelInviteMembersResult
  messagesChannelInviteResend(id: String!, inviteId: ID!): MessagesChannelInviteResendResult
  messagesChannelJoin(code: String!): MessagesChannelJoinResult
  messagesChannelRemoveMembers(id: String!, userIds: [String!]!): MessagesChannelRemoveMembersResult
  messagesChannelUpdate(channel: ChannelUpdateInput, id: String!): ChatChannelUpdateResult
  messagesChannelUpdateMembers(assignment: AssignmentInput, id: String, moderatorIds: [String], userIds: [String]): ChatChannelUpdateMembersResult
  messagesPrivateChannelCreate(channel: PrivateChannelCreateInput!, memberIds: [String!]!): ChatChannelCreateResult
  messagesToken: ChatTokenResult
  messagesUpsertUsers(userIds: [String!]!): MessagesUpsertUsersResult
  netsuiteWebhook(netsuiteEvent: NetsuiteEventInput): NetsuiteWebhookResponse
  noticeCreate(input: NoticeCreateInput): Notice
  noticeDelete(input: NoticeDeleteInput): [Notice]
  noticeModify(input: NoticeModifyInput): Notice
  oauthCallback(email: String!, firstName: String, lastName: String, provider: UserProviders!, providerUserId: String!): OAuthCallbackResponse
  officialCreate(input: CreateOfficialInput): Official
  officialDelete(input: DeleteOfficialInput): Official
  officialDutiesDelete(input: DeleteOfficialDutiesInput): [OfficialDuty]
  officialDutiesUpsert(input: [UpsertOfficialDutiesInput]): [OfficialDuty]
  officialDutyCreate(input: CreateOfficialDutyInput): OfficialDuty
  officialDutyModify(input: ModifyOfficialDutyInput): OfficialDuty
  officialModify(input: ModifyOfficialInput): Official
  officialPoolCreate(input: CreateOfficialPoolByIdInput): OfficialPool
  officialPoolDelete(input: DeleteOfficialPoolInput): [OfficialPool]
  officialPoolModify(input: ModifyOfficialPoolByIdInput): OfficialPool
  officialsUpsert(input: [UpsertOfficialsInput]): [OfficialPool]
  opponentCreate(input: CreateOpponentInput): Opponent
  opponentDelete(input: DeleteOpponentInput): Opponent
  opponentModify(input: ModifyOpponentInput): Opponent
  opponentsUpsert(input: [UpsertOpponentsInput]): [Opponent]
  "Create a new Affiliate. Affiliates have a parent Org, and this mutation will connect the two."
  orgAffiliateCreate(input: OrgAffiliateInput!): MutationResponse!
  orgBusinessCreate(input: OrgBusinessInput!): MutationResponse!
  "Create a new Club. Clubs do not have a parent Org."
  orgClubCreate(input: OrgClubInput!): MutationResponse!
  "Update an existing Club."
  orgClubUpdate(input: OrgClubUpdateInput!): MutationResponse!
  "Create a new District. Districts do not have a parent Org."
  orgDistrictCreate(input: OrgDistrictInput!): MutationResponse!
  "Update an Org Node's label in parallel with changes being made in MOD. This mutation is intended to preserve User Associations and Financial Account Associations."
  orgLabelUpdate(auxiliary_type: ModType, label: OrgType!, modId: ID!, parentModId: ID): MutationResponse!
  "Create a new Program named after a given activity and connect it to a given Org."
  orgProgramCreate(activity: ProgramActivity!, orgId: ID!): MutationResponse!
  "Create a new School. Schools usually have a parent District, and this mutation will optionally connect the two."
  orgSchoolCreate(input: OrgSchoolInput!): MutationResponse!
  "Update an existing School."
  orgSchoolUpdate(input: OrgSchoolUpdateInput!): MutationResponse!
  "Delete a User from all relationships connecting them to an Org Staff Roster."
  orgStaffRosterUserDelete(input: OrgStaffRosterUserDeleteInput!): MutationResponse!
  "Create a new Team for a given activity and connect it to a Program of the same activity under a given Org."
  orgTeamCreate(activity: ProgramActivity!, name: String!, orgId: ID!, teamAge: TeamAge!, teamGender: TeamGender!, teamGroupType: TeamGroupType!, teamSize: Int = null, teamTier: TeamTier!): MutationResponse!
  participantDonationInviteEdit(campaignID: String!, donationInviteID: String!, newEmail: String!): DonorEmailData
  participantDonationInviteEmailAdd(campaignID: String!, email: String!): DonorEmailData
  participantDonationInviteRemove(email: String!, fundraiserId: String!, personListEntryId: String!): [DonationInviteRemoveData]
  participantDonationInviteSMSAdd(campaignID: String!, phoneNumber: String!): DonationInviteSMSData
  participantDonationInvitesUpdate(action: ParticipantDonationInvitesAction!, raiseFundraiserId: Int!): ParticipantDonationInviteResponse
  "This will update the emails of a Participant's Guardian Email field"
  participantGuardianEmailUpdate(guardianEmail: String!, secondGuardianEmail: String): ParticipantGuardianEmailUpdate!
  participantIncentiveSelectionDelete(fundraiserId: ID!, fundraiserUserIncentiveId: ID!): DeletedIncentiveRecord
  participantRewardsBulkCreate(fundraiserId: Int!, rewards: [ParticipantRewardsInput!]!): [BulkRewards]
  participantRewardsCreate(fundraiserId: Int!, incentiveId: Int!, size: String!, tier: Int!): CreatedParticipantRewards
  participantSignup(apps: [String], email: String!, password: String!, phoneNumber: String!): [TransportType!]!
  participantSmsDonationInvitesAdd(fundraiserId: String, phoneNumbers: [String!]!, raiseFundraiserId: Int): FundraiserParticipantSmsDonationInvitesAddResponse
  participantSmsDonationInvitesRemove(ids: [String!]): [String]
  "Send out emails to particpants"
  participantsEmailSend(
    "The fundraiser ID"
    fundraiserId: Int!,
    "The message in email. Plain text only."
    message: String!,
    "The subject of the message"
    subject: String!
  ): IMutationResult
  "Sends out texts to particpants"
  participantsTextSend(
    "The fundraiser ID"
    fundraiserId: Int!,
    "The message to send out. Plain text only."
    message: String!
  ): IMutationResult
  paymentsApiCustomerGetOrCreate(stripeEnv: StripeEnv = RAISE): PaymentsApiCustomerResponse!
  paymentsApiDetachPaymentMethod(input: PaymentsApiDetachPaymentMethodInput): PaymentsApiDetachPaymentMethodResponse
  paymentsApiPayment(input: PaymentsApiPaymentIput): PaymentsApiPaymentResponse
  paymentsApiRefund(input: PaymentsApiRefundInput): PaymentsApiCreateRefundResponse
  "Reschedule time (timezone-sensitive) to send out preload email to all participants"
  preloadEmailRescheduleSend(
    "DateTime to send out (should be UTC). It support multiple format such as 2022-02-01 or 2022-02-01T00:00:00Z"
    dateTime: DateTime,fundraiserId: Int!,
    "Time offset in seconds"
    offsetInSeconds: Int,
    "Timezone of datetime to send out. e.g. American/New_York"
    timezone: String
  ): PreloadEmailSchedule
  preloadEmailScheduleCancel(fundraiserId: Int!): Boolean
  "Schedule time (timezone-sensitive) to send out preload email to all participants"
  preloadEmailScheduleSend(
    "DateTime to send out. It support multiple format such as 2022-02-01 or 2022-02-01T00:00:00Z"
    dateTime: DateTime!,fundraiserId: Int!,
    "Timezone of datetime to send out. e.g. American/New_York"
    timezone: String!
  ): PreloadEmailSchedule
  "Send out preload email to all participants"
  preloadEmailSend(fundraiserId: Int!): String
  preparationCreate(input: CreatePreparationInput): Preparation
  preparationDelete(input: DeletePreparationInput): Preparation
  preparationModify(input: ModifyPreparationInput): Preparation
  "Send push notification to user"
  pushNotificationSend(input: SendNotificationInput!): PushNotificationResponse!
  "Create multiple fundraiser users and return total number of fundraiser users created"
  raiseAdminFundraiserUsersCreateMany(input: RaiseFundraiserUsersCreateManyInput!): Int!
  resendNetsuiteTracking(netsuiteId: String!): TrackingResult!
  restartOrderWorkflow(netsuiteId: String!): OrderResult!
  roleCreate(data: RoleArguments!): Role
  roleDelete(id: String!): Role
  rolePermissionsUpdate(permissionIds: [String!]!, roleId: String!): Role
  roleUpdate(data: RoleArguments!, id: String!): Role
  "Clone an existing Roster and attach it to a new Org. This Roster will NOT be synced or subscribed to changes to the original roster. This will also clone the list of Members."
  rosterClone(input: RosterCloneInput!): MutationResponse!
  "Create a new Roster for a given Org and attach a list of Members to it."
  rosterCreate(input: RosterCreateInput!): MutationResponse!
  "Update a Roster's properties including adding to the list of Members OR removing members from the list."
  rosterUpdate(input: RosterUpdateInput!): MutationResponse!
  "Request one or more S3 pre-signed urls to upload files for approval submissions."
  s3PresignedInsightsApprovalsUrl(approvalsFormId: Int!, approvalsSubmissionId: Int!, files: [NonEmptyString]!): [S3PresignedInsightsApprovalsUrlResult!]!
  samlCallback(actualEmail: String, apps: [String], consumer: String, email: String, firstName: String, lastName: String): Auth
  scheduleEventArchive(id: ID!, origin: String!): EventArchiveResult
  scheduleEventCreate(event: ScheduleEventInput!, origin: String!): ScheduleEventCreateResult
  scheduleEventUpdate(event: ScheduleEventInput!, id: ID!, origin: String!): ScheduleEventUpdateResult
  schoolInfoCreate(input: CreateSchoolInfoInput): SchoolInfo
  schoolInfoDelete(input: DeleteSchoolInfoInput): SchoolInfo
  schoolInfoModify(input: ModifySchoolInfoInput): SchoolInfo
  seasonCreate(input: SeasonCreateInput): Season
  seasonDelete(input: SeasonDeleteInput): [Season]
  seasonModify(input: SeasonModifyInput): Season
  seasonPostponeModify(input: SeasonPostponeUnpostponeInput): PostponeUnpostponeResponse
  seasonScheduleTeamsCreate(input: SeasonScheduleTeamsCreateInput): [Season]
  seasonUnpostponeModify(input: SeasonPostponeUnpostponeInput): PostponeUnpostponeResponse
  "Add, update or remove Insights configurations for current user Allowed configurations: - VAULT_WELCOME_DISMISSED (YES | default=NO) - VAULT_ORG_APPROVED_DISMISS_BANNER (value should be org_id list) - APPROVALS_WELCOME_DISMISSED (YES | default=NO) Note: value=null to unset"
  setUserInsightsConfigurations(config: [UserInsightsConfigInput]!): UserInsightsConfigResult!
  "Update settlement note for a fundraiser"
  settlementIssuesRecord(fundraiserId: Int!, note: String): String
  "Update settlement status for a fundraiser"
  settlementStatusUpdate(fundraiserId: Int!, settlementStatus: SettlementStatus): IMutationResult
  snapMobileOneAccessRequest(input: SnapMobileOneAccessInput!): MutationResponse!
  snapMobileOneContactSupport(input: SnapMobileOneContactSupportInput!): MutationResponse!
  snapMobileOneCreate(input: SnapMobileOneAccessInput!): MutationResponse!
  snapMobileOneProductActivate(input: SnapMobileOneProductInput!): MutationResponse!
  snapMobileOneProductDelete(input: SnapMobileOneProductInput!): MutationResponse!
  snapMobileOneProductRequest(input: SnapMobileOneProductInput!): MutationResponse!
  snapMobileOneUpdate(input: SnapMobileOneAccessInput!): MutationResponse!
  spendAdminAllowCheckId(input: SpendCheckImageAllowance!): SpendCheckImageAllowanceResponse
  spendAdminApprovalUpdate(orgId: String!): spendAdminApprovalUpdateResponse
  spendAdminBulkAddUnit(input: SpendAdminBulkAddUnitInput): AuthorizedUsersAdded
  spendAdminCloseCustomer(orgId: String!): spendAdminCustomerCloseResponse
  spendAdminUnarchiveGroup(groupId: String!): SpendGroupID
  spendArchiveSeasonMembers(input: SpendArchiveSeasonMembersInput!): SpendArchiveSeasonMembersResponse
  spendAuthorizeAutoPay(input: SpendAutoPayInput): SpendAuthorizeAutoPayResponse
  spendBudgetCreate(input: SpendBudgetInput!): SpendBudgetID
  spendBudgetDelete(id: String!, replace: String): SpendBudgetID
  spendBudgetUpdate(id: String!, input: SpendBudgetInput!): SpendBudgetID
  spendCategoryCreate(input: SpendCategoryInput!): SpendCategoryID
  spendCategoryUpdate(id: String!, input: SpendCategoryInput!): SpendCategoryID
  spendCategoryUpsertBulk(input: [SpendUpsertCategoryInput]): [SpendCategoryID]
  spendCreditMemoCreate(input: SpendCreditMemoInput!): SpendCreditMemoCreateResponse
  spendCreditMemoUpdate(input: SpendCreditMemoUpdateInput!): SpendCreditMemoUpdateResponse
  spendGroupACHCredit(input: SpendGroupACHCredit): SpendACHCreditResponse
  spendGroupArchive(id: String!): SpendGroupArchiveResponse
  spendGroupBankAccessTokenCreate(id: String, publicToken: String!, status: String): SpendBankAccessCreateResponse
  spendGroupBankAccessTokenDelete(id: String): SpendBankAccessDeleteResponse
  spendGroupCheckCancel(checkId: String!): SpendCancelCheckID
  spendGroupCheckDepositPatch(input: SpendGroupCheckDepositTagsInput!): SpendCheckDepositTagsResponse
  spendGroupCheckSend(input: SpendCheckInput): SpendCheckSendResponse
  spendGroupCounterpartyCreate(input: SpendCounterpartyCreateInput): SpendCounterpartyCreateResponse
  spendGroupCounterpartyDelete(counterpartyId: String!, groupId: String!): SpendDeleteCounterpartyResponse
  spendGroupCreate(input: SpendGroupInput!): SpendGroupID
  spendGroupDebitCardCreate(id: String, input: SpendDebitCardInput!): SpendDebitCardID
  spendGroupDuplicate(id: String!): SpendGroupID
  spendGroupExternalTransfer(input: SpendGroupExternalTransferInput!): SpendExternalTransferResponse
  spendGroupPayeeCreate(input: SpendPayeeInput!): SpendPayeeCreateResponse
  spendGroupPayeeUpdate(input: SpendPayeeUpdateInput!): SpendPayeeCreateResponse
  spendGroupRosterCreate(input: SpendGroupRosterInput!): SpendGroupRosterID
  spendGroupRosterUpdate(input: SpendRosterUserUpdate!): SpendRosterUpdate
  spendGroupRostersCreate(input: SpendGroupRostersInput!): [SpendGroupRosterID]
  spendGroupUnArchive(id: String!): SpendGroupArchiveResponse
  spendGroupUpdate(id: String, input: SpendGroupInput!): SpendGroupID
  spendGuardianSignup(input: SpendGuardianSignupInput!): SpendUserID
  spendInvoiceArchive(id: String!): SpendInvoiceID
  spendInvoiceChangeRequest(input: SpendInvoiceRequestChangeInput!): SpendInvoiceID
  spendInvoiceCreate(input: SpendInvoiceInput!): SpendInvoiceID
  spendInvoiceOptInOrOut(input: SpendOptInOrOutInput!): SpendInvoiceID
  spendInvoicePaymentDeauthorize(input: SpendInvoicePaymentDeauthorizeInput!): SpendDeauthorizeResponse
  spendInvoiceRefund(input: SpendInvoiceRefundInput!): SpendInvoiceRefundResponse
  spendInvoiceReminderCreate(input: SpendInvoiceReminderInput!): SpendNotificationID
  spendInvoiceUpdate(id: String!, input: SpendInvoiceInput!): SpendInvoiceID
  spendInvoiceUpdateMany(input: SpendUpdateInvoices!): SpendInvoiceUpdateResponse
  spendInvoiceUpdatePaymentMethod(input: SpendInvoicePaymentMethodUpdate!): SpendInvoiceIDs
  spendNotificationCreate(input: SpendNotificationInput!): SpendNotificationStatus
  spendOrganizationACHCredit(input: SpendOrgACHCredit): SpendACHCreditResponse
  spendOrganizationAccountTransfer(input: SpendAccountTransferInput!): SpendAccountID!
  spendOrganizationAdminUpdate(input: SpendOrgAdminUpdateInput!): SpendOrgAdminUpdateResponse
  spendOrganizationBankAccessTokenCreate(publicToken: String!, status: String): SpendBankAccessCreateResponse
  spendOrganizationBankAccessTokenDelete: SpendBankAccessDeleteResponse
  spendOrganizationCheckCancel(checkId: String!): SpendCancelCheckID
  spendOrganizationCheckDepositPatch(input: SpendOrganizationCheckDepositTagsInput!): SpendCheckDepositTagsResponse
  spendOrganizationCheckSend(input: SpendCheckInput): SpendCheckSendResponse
  spendOrganizationCounterpartyCreate(input: SpendCounterpartyCreateInput): SpendCounterpartyCreateResponse
  spendOrganizationCounterpartyDelete(counterpartyId: String!): SpendDeleteCounterpartyResponse
  spendOrganizationCreate(externalId: String!, userId: String!): SpendOrganizationID
  spendOrganizationDebitCardActions(input: SpendDebitCardActionsInput!): SpendDebitCardActionResponse
  spendOrganizationDebitCardCreate(input: SpendDebitCardInput!): SpendDebitCardID
  spendOrganizationExternalTransfer(input: SpendOrganizationExternalTransferInput!): SpendExternalTransferResponse
  spendOrganizationPayeeCreate(input: SpendPayeeInput!): SpendPayeeCreateResponse
  spendOrganizationPayeeUpdate(input: SpendPayeeUpdateInput!): SpendPayeeCreateResponse
  spendOrganizationPayoutCreate(input: SpendOrganizationPayoutCreateInput!): SpendOrganizationPayoutResponse
  spendOrganizationRecurringPayoutCreate(input: SpendOrganizationRecurringPayoutCreateInput!): SpendOrganizationRecurringPayoutResponse
  spendOrganizationRecurringPayoutUpdate(input: SpendOrganizationRecurringPayoutUpdateInput!): SpendOrganizationRecurringPayoutResponse
  spendOrganizationSignup: SpendSignupFormResponse
  spendOrganizationUpdate(data: SpendOrganizationInput!, where: ID!): SpendOrganization
  spendPaymentMethodCreate(input: SpendPaymentMethodInput!): SpendPaymentMethodResponse
  spendPaymentScheduleCreate(input: SpendPaymentScheduleInput!): SpendPaymentScheduleID
  spendPaymentScheduleRevert(id: String!): SpendPaymentScheduleID
  spendPaymentScheduleRevertMany(ids: [String!]!): [SpendPaymentScheduleID]
  spendPaymentScheduleUpdate(id: String!, input: SpendPaymentScheduleInput!, rosterIds: [String]): SpendPaymentScheduleID
  spendPaymentScheduleUpdateBySeason(input: SpendPaymentScheduleBySeasonInput!): [SpendPaymentScheduleID]
  spendRemoveParticipants(input: SpendRemoveSeasonMemberInput!): SpendRemoveSeasonMemberResponse
  spendRoleChange(roleId: String!, sessionId: String!): SpendRoleID
  spendSeasonCreate(input: SpendSeasonInput!): SpendSeasonID
  spendSeasonUpdate(id: String!, input: SpendSeasonInput!): SpendSeasonID
  spendSessionCreate(inviteId: String): SpendSession
  spendSessionDelete: SpendSessionID
  spendSessionRefresh(sessionId: String!): SpendSessionID
  spendSettingsUpdate(input: SpendSettingsInput): SpendOrganizationID
  spendSettingsUpdateAll(input: SpendSettingsInput): SpendUpdateResponse
  spendSignupAgreementCreate(input: SpendSignupAgreementInput!): SpendAgreementID
  spendStopCheckPayment(checkId: String!): SpendStopCheckPaymentResponse
  spendSystemNotificationCreate(input: SpendSystemNotificationCreateInput): SpendSystemNotificationIdResponse
  spendSystemNotificationUpdate(input: SpendSystemNotificationUpdateInput): SpendSystemNotificationIdResponse
  spendTransactionAttachmentCreate(input: SpendTransactionAttachmentInput!): SpendTransactionAttachmentID
  spendTransactionAttachmentDelete(id: String!): SpendTransactionAttachmentID
  spendTransactionAttachmentDeleteByKey(attachmentKey: String!): [SpendTransactionAttachmentID!]
  spendTransactionBudgetUnreconcile(input: SpendTransactionBudgetUnreconcileInput!): SpendReconcileTransactionID
  spendTransactionInvoiceReconcile(input: SpendTransactionInvoiceReconcileInput!): SpendReconcileTransactionID
  spendTransactionInvoiceUnreconcile(input: SpendTransactionInvoiceUnreconcileInput!): SpendReconcileTransactionID
  spendTransactionNoteCreate(input: SpendTranscationNoteInput!): SpendTransactionNoteID
  spendTransactionNoteUpdate(id: String!, input: SpendTranscationNoteInput!): SpendTransactionNoteID
  spendTransactionReconcile(input: SpendTransactionReconcileInput!): SpendReconcileTransactionID @deprecated(reason: "Use spendTransactionReconcileV2")
  spendTransactionReconcileV2(input: SpendTransactionReconcileV2Input!): SpendReconcileTransactionID
  spendTransactionsCreate(input: SpendTransactionInput!): SpendTransactionsIdList
  spendUnitApplication: SpendUnitApplicationResponse
  spendUnitFileUpload(fileToUpload: String!, name: String!): SpendTransactionAttachmentID
  spendUpdateApproval(input: SpendApprovalUpdateInput): SpendApprovalUpdateResponse
  spendUserAccountUpdate(input: SpendUserAccountUpdateInput!): SpendUserAccountUpdateResponse
  spendUserAchPayment(input: SpendAchPaymentInput!): SpendAchPaymentResponse
  spendUserAchPaymentCancel(input: SpendAchPaymentCancelInput!): SpendAchPaymentID
  spendUserAcknowledgeNotification(input: SpendUserAcknowledgeNotificationInput): SpendUserAcknowledgeNotificationResponse
  spendUserBankAccessTokenCreate(externalAuthId: String, groupId: String!, publicToken: String!, status: String): SpendBankAccessCreateResponse
  spendUserBankAccessTokenDelete(counterpartyId: String, id: String): SpendBankAccessDeleteResponse
  spendUserEmailUpdate(input: SpendUserEmailUpdateInput): SpendUserEmailUpdateResponse
  spendUserInviteArchive(id: String!): SpendInviteID
  spendUserInviteCreate(input: SpendInviteInput!): SpendInviteID
  spendUserInviteDelete(id: String!): SpendInviteID
  spendUserInviteDismiss(inviteId: String!): SpendDismissInviteResponse
  spendUserInviteResend(input: SpendInviteResendInput!): SpendInviteID
  spendUserInviteResendAll(input: SpendInviteResendAllInput!): SpendAllInviteIds
  spendUserInviteStatusUpdate(id: String!, status: String!): SpendInviteID
  spendUserInviteUpdate(id: String!): SpendInviteID
  spendUserLeaveGroup(groupId: String!): SpendUpdateResponse
  spendUserLoginAudit(input: SpendUserLoginAuditInputV2!): SpendUserLoginAuditResponse
  spendUserNotificationSettingUpdate(input: SpendUserNotificationSettingInput): SpendUserNotificationSettingID
  spendUserPaymentsCardDetach(input: SpendPaymentMethodDetach): SpendPaymentMethodDetachResponse
  spendUserRoleArchive(groupId: String, userId: String!): SpendUserID
  spendUserRoleSet(roleId: String!): SpendUserRoleID
  spendUserRoleUpdate(groupId: String, inviteId: String!, roleName: SpendRoleNameEnum!, userId: String!): SpendUserRoleID
  spendUserRoleUpdateIsApprover(input: SpendIsApproverUpdateInput): SpendUserRoleID
  spendUserSignUp(input: SpendSignUpInput!): SpendSignUpResponse
  spendUserSignupUpdate(input: SpendUserSignupInput): SpendUserRoleID
  spendVaultApplicationCreate(input: SpendVaultApplicationCreateInput): SpendVaultApplicationCreateResponse
  storeBuildRequest(input: storeBuildRequestInput!): StoreBuildRequestUnionType
  storeEditInfo(input: StoreEditInfoInput!): StoreEditInfoUnionType
  storeManagerPointsUpdate(input: StoreManagerUpdatePoints): StoreManagerUpdatePointsUnionType
  storePaymentIntentCreate(input: StoreCreatePaymentIntent!): StorePaymantIntentUnionType
  storePointsWithdrawalRequest(input: StorePointsWithdrawalRequestInput): StorePointsWithdrawalRequestUnionType
  storeProfitPercentageUpdate(store_id: Int!, updated_product_price_percentage: Int!): StoreProfitPercentageUpdated
  storeTicketCreate(input: StoreTicketInput!): StoreTicketsUnionType
  storeTransactionSave(input: StoreSaveTransaction!): StoreTransactionUnionType
  storeTransferToCustomer(input: StoreTransferCustomer): StoreTransferPointsUnionType
  storeTransferToGL(input: StoreTransferGL): StoreTransferPointsUnionType
  storeTransferToParticipant(input: StoreTransferParticipant): StoreTransferPointsUnionType
  storeUpdatePayable(input: StoreUpdatePayableInput, scopeId: Int!): StorePayableInfoUnionType
  storeUpdateStatus(input: StoreStatusInput!): MagentoStoreUnionType
  storeUserPreferenceUpsert(featureTourCompleted: Boolean, userId: String!): UserPreference
  "Submits submission draft to be reviewed by approver. Email will be sent to approver."
  submitFundraiserApprovalSubmission(id: Int!): FundraiserApprovalSubmission!
  "Send support-ticket email to the support team."
  supportTicketSend(
    "Uploaded attachments"
    attachments: [DriveFileInput!],
    "List of emails to be cc'd."
    ccEmails: [String!],
    "Device and browser info of the session that created the ticket."
    deviceInfo: String,
    "Fundraiser id if email is campaign related."
    fundraiserId: Int,
    "The message of the email."
    message: String,
    "The subject of the email."
    subject: String!,
    "Collection of blocks of information to be displayed in the email body."
    supportTicketInfo: [DriveFieldInput!],
    "The unique id for form template being used"
    templateId: String,
    "The recipient of the email based on the department."
    to: SUPPORT_TICKET_DEPARTMENT!
  ): IMutationResult
  teamCreate(input: TeamCreateInput): Team
  teamDelete(input: TeamDeleteInput): Team
  teamModify(input: TeamModifyInput): Team
  teamOfficialCreate(input: TeamOfficialCreateInput): TemplateOfficial
  teamOfficialDelete(input: TeamOfficialDeleteInput): TemplateOfficial
  teamOfficialModify(input: TeamOfficialModifyInput): TemplateOfficial
  teamPreparationCreate(input: TeamPreparationCreateInput): TemplatePreparation
  teamPreparationDelete(input: TeamPreparationDeleteInput): TemplatePreparation
  teamPreparationModify(input: TeamPreparationModifyInput): TemplatePreparation
  teamWorkerCreate(input: TeamWorkerCreateInput): TemplateWorker
  teamWorkerDelete(input: TeamWorkerDeleteInput): TemplateWorker
  teamWorkerModify(input: TeamWorkerModifyInput): TemplateWorker
  "An example field added by the generator"
  testField: String!
  toggleCardActivation(cardId: String!, status: CARD_STATUS!): String!
  updateFundraiserUserTypeConfig(id: Int!, userTypeConfig: UserTypeConfiguration!): Boolean
  updateParticipantCampaignConfiguration(field: ParticipantCampaignConfigInput, id: ID!): participantCampaignConfiguration!
  updateParticipantFundraiserConfiguration(input: UpdateParticipantFundraiserConfigurationInput!): ParticipantFundraiserConfiguration
  updateParticipantGroup(fundraiserId: Int!, personListsId: String!, userId: Int!): UpdatedParticipantGroupCount
  "This will update the size of a Participant's OTK item"
  updateParticipantOtkSize(fundraiserUserIncentiveId: Int!, size: String!): FundraiserUserIncentiveID!
  upgradeToManageUser: String
  upgradeToWalletUser(email: String): User
  uploadLogoPNG(image: ImageInput): OrderResult
  "Create a relationship between a User and a Program with a given activity under a given Organization. This will also create a User Node only if they exist in User Directory but not Orgs."
  userActivityLeadsCreate(activity: ProgramActivity!, isConfirmed: Boolean = false, orgId: ID!, title: TeamTitle!, userId: ID!): MutationResponse!
  "Delete a relationship between a User and a Program with a given activity under a given Organization."
  userActivityLeadsDelete(activity: ProgramActivity!, orgId: ID!, title: TeamTitle!, userId: ID!): MutationResponse!
  "Update a relationship between a User and a Program with a given activity under a given Organization."
  userActivityLeadsUpdate(activity: ProgramActivity!, isConfirmed: Boolean!, orgId: ID!, title: TeamTitle!, userId: ID!): MutationResponse!
  userApiKeyCreate(expiresPeriod: String, maxRps: Int! = 50, name: String!, permissionIds: [String!]!): ApiKey
  userApiKeyDelete(id: String!): String!
  userApiKeyUpdate(id: String!, maxRps: Int! = 50, name: String!, permissionIds: [String!]!): ApiKey
  userAssociate(orgId: ID!, product: Product!, roleId: ID!, userId: ID!): MutationResponse! @deprecated(reason: "Please use userAssociationCreate instead")
  "Create first_seen and last_seen properties for a User and their product specific Org association."
  userAssociationAccess(orgId: ID!, product: Product!, userId: ID!): MutationResponse!
  "Associate a User with an Org and a specific product. This will also create a User Node only if they exist in User Directory but not Orgs."
  userAssociationCreate(orgId: ID!, product: Product!, roleId: ID!, userId: ID!): MutationResponse!
  "Add a deleted_at property to a User and their product specific Org association. This will NOT delete the relationship."
  userAssociationDelete(orgId: ID!, product: Product!, userId: ID!): MutationResponse!
  userBulkInviteSend(inviteType: InviteType!, orgPayload: [UserOrgInvitationPayload!]!): UserOrgInvitationResult!
  userChallengeUpdate(id: String!, status: UserChallengeStatus!): UserChallenge
  userChildCreate(firstName: String!, lastName: String!): User
  userChildUnassign(id: String!): String!
  userChildUpdate(firstName: String!, id: String!, lastName: String!, profilePicture: String): User
  userConfirmProfileChallenge(challengeId: String!, code: String!, data: UserConfirmProfileChallengeData!): ConfirmProfileChallengeResponse
  userCreate(apps: [String], email: String!, firstName: String, language: String, lastName: String, occupation: UserOccupation, parentId: String, password: String!, phoneNumber: String!, profilePicture: String, snapRaiseId: Int): User
  userEmailConfirm(code: String!): String
  userFittingAdd(fitting: UserFittingPreference!): UserFitting
  userImpersonate(userId: String!): Auth
  userImpersonateLogout: Auth
  "If it is confirmed that a Group Leader is no longer affiliated with their former Org, mark their relationship with a campaign as 'no_longer_affiliated'."
  userImpliedAffiliationDelete(orgId: ID!, userId: ID!): MutationResponse!
  userInitiateProfileChallenge(challengeId: String!, data: UserInitiateProfileChallengeData!): InitiateProfileChallengeResponse
  userInvite(email: String, joincode: String): String! @deprecated(reason: "use inviteCreate instead")
  userInviteAccept(inviteId: String): UserInviteResponse @deprecated(reason: "use inviteAccept instead")
  userInviteSend(channelInvite: ChannelInviteInput, orgInvite: OrgInviteInput, parentInvite: ParentInviteInput, type: InviteType!): InviteSendResult
  userLoginStep(email: String!): UserLoginStepResponse!
  "Connect a User to an Org with a specific title. This will also create a User Node only if they exist in User Directory but not Orgs."
  userOrgAffiliationCreate(description: String = null, isAdmin: Boolean = null, isConfirmed: Boolean = null, orgId: ID!, title: AffiliationTitle!, userId: ID!): MutationResponse!
  "Add a deleted_at timestamp to a User's title specific Org affiliation."
  userOrgAffiliationDelete(orgId: ID!, title: AffiliationTitle!, userId: ID!): MutationResponse!
  "Update the descripton, or isConfirmed on the affiliation between a User and an Org. If you want to change the title, you will need to delete the existing affiliation and create a new one."
  userOrgAffiliationUpdate(description: String = null, isAdmin: Boolean = null, isConfirmed: Boolean = null, orgId: ID!, title: AffiliationTitle!, userId: ID!): MutationResponse!
  userPermissionsUpdate(negativePermissions: [UserPermissionAssignment!]!, permissions: [UserPermissionAssignment!]!, roleIds: [String!]!, userId: String!): UserPermissionsList
  userPhoneNumberChallengeConfirm(challengeId: String!, code: String!): PhoneNumberChallengeConfirmation
  userPhoneNumberConfirm(code: String!): String
  userRefreshSession(refreshToken: String): Auth
  userResetPassword(email: String, id: String, transport: TransportEnum, withLink: Boolean = false): UserResetPasswordResponseWithAvailableTransport
  userResetPasswordByAdmin(forcePasswordResetChallenge: Boolean, password: String!, userId: String!): UserResetPasswordByAdminResponse
  userSignup(apps: [String], consumer: String, email: String!, firstName: String, inviteId: String, lastName: String, occupation: UserOccupation, password: String!, phoneNumber: String!, profilePicture: String): SignUpResponseWithAvailableTransport
  userSignupConfirm(code: String!, consumer: String, email: String!, skip: Boolean): Auth
  userSignupConfirmationResend(email: String!, transport: TransportEnum): [TransportType!]!
  "Create a relationship between a User and a specific Team. This will also create a User Node only if they exist in User Directory but not Orgs."
  userTeamLeadsCreate(isConfirmed: Boolean = null, teamId: ID!, title: TeamTitle!, userId: ID!): MutationResponse!
  "Add a deleted_at property to a title specific relationship between a User and Team."
  userTeamLeadsDelete(teamId: ID!, title: TeamTitle!, userId: ID!): MutationResponse!
  "Update the isConfirmed boolean on a title specific relationship between a User and Team."
  userTeamLeadsUpdate(isConfirmed: Boolean!, teamId: ID!, title: TeamTitle!, userId: ID!): MutationResponse!
  userUnassignParent(email: String, parentId: String): String
  userUpdate(apps: [String], email: String, firstName: String, hsGradYear: String, id: String, isConfirmed: Boolean, isDisabled: Boolean, language: String, lastName: String, occupation: UserOccupation, password: String, phoneNumber: String, profilePicture: String): User
  userUpdatePassword(email: String!, newPassword: String!, oldPassword: String!): User
  vaultCreateKybKycSubmit(bankAccountPayableOrgId: ID, beneficialOwners: [VaultKycCreateInput!], createAsBusinessWalletCustomer: Boolean, gateway: VaultFinancialProvider!, orgData: VaultKybCreateInput!, representativeData: VaultRepresentativeCreateInput!,
    "if provided, vault will also create a financial account node using this data for the payable org node"
    saveToPayableOrgId: ID,stripeData: VaultCreateKybKycStripeInput  ): VaultFormMutationResponse!
  vaultCustomerCreate(payableOrgName: String!, stripeEnv: VaultStripeEnv = RAISE): String!
  vaultDocumentGatewayUpload(documentBack: String, documentFront: String!, fileType: String!, gateway: VaultFinancialProvider!, stripeConnectAccountId: String, stripeEnv: VaultStripeEnv = RAISE, stripePersonId: String, type: VaultRequiredDocument!, unitApplicationId: String, unitDocumentId: String, vaultId: ID!): VaultMutationResponse
  vaultIncorrectKybSupportReport(comment: String, orgAddressCity: String!, orgAddressState: String!, orgAddressStreet: String!, orgAddressZip: String!, orgEin: String!, orgLegalName: String!, raiseCampaignId: String!, raiseUserId: String!): VaultMutationResponse
  vaultKybCreate(address: VaultAddressInput!, businessVertical: String, customerFacingName: String!, description: String, email: String, legalName: String!, numberOfEmployees: String, phoneNumber: String!, stateOfIncorporation: String, structure: KYB_STRUCTURE!, taxId: String!, type: KYB_TYPE!, url: String, yearOfIncorporation: String): ID!
  vaultKybUpdate(address: VaultAddressUpdateInput, businessVertical: String, customerFacingName: String, description: String, email: String, id: ID!, legalName: String, numberOfEmployees: String, phoneNumber: String, stateOfIncorporation: String, structure: KYB_STRUCTURE, taxId: String, type: KYB_TYPE, url: String, yearOfIncorporation: String): ID!
  vaultKycCreate(address: VaultAddressInput!, director: Boolean, dob: VaultDobInput!, email: String!, executive: Boolean, firstName: String!, lastName: String!, owner: Boolean, percentOwnership: Int, phoneNumber: String!, representative: Boolean, ssn: String, title: String): ID!
  vaultKycUpdate(address: VaultAddressUpdateInput, director: Boolean, dob: VaultDobInput, email: String, executive: Boolean, firstName: String, id: ID!, lastName: String, owner: Boolean, percentOwnership: Int, phoneNumber: String, representative: Boolean, ssn: String, title: String): ID!
  vaultOrgConfirmationSubmit(bankAccountPayableOrgId: ID, beneficialOwners: [VaultBeneficialOwnerCreateInput!], fundraiserId: String, fundraiserSlug: String, gateway: VaultFinancialProvider!, orgData: VaultFormKybInput!, primaryRepresentative: VaultRepresentativeCreateInput!, productDescription: String, representativeToReplace: VaultRepresentativeCreateInput, stripeData: VaultFormStripeInput, submittedOnYourBehalf: Boolean!): VaultFormMutationResponse!
  vaultSetupIntentCreate(customerId: String, stripeEnv: VaultStripeEnv = RAISE): String!
  vaultStripeConnectAccountBankAccountDefaultSet(bankAccountId: String!, stripeConnectAccountId: String!, stripeEnv: VaultStripeEnv = RAISE): VaultMutationResponse!
  vaultStripeConnectAccountBankAccountDelete(bankAccountId: String!, orgId: ID, stripeConnectAccountId: String!, stripeEnv: VaultStripeEnv = RAISE): VaultMutationResponse!
  vaultStripeConnectAccountCreate(metadata: StripeMetadataInput, statementDescriptor: String, stripeEnv: VaultStripeEnv = RAISE, vaultKybId: ID!): String!
  vaultStripeConnectAccountOwnersProvided(ownersProvided: Boolean!, stripeConnectAccountId: String!, stripeEnv: VaultStripeEnv = RAISE): String!
  vaultStripeConnectAccountPaymentMethodAttach(customerId: String, orgId: ID, paymentMethodId: String!, stripeConnectAccountId: String!, stripeEnv: VaultStripeEnv = RAISE): VaultMutationResponse!
  vaultStripeConnectAccountPaymentMethodDefaultSet(customerId: String!, orgId: ID, paymentMethodId: String!, stripeConnectAccountId: String!, stripeEnv: VaultStripeEnv = RAISE): VaultMutationResponse!
  vaultStripeConnectAccountPayoutFrequencySet(orgId: ID, payoutInterval: VaultPayoutInterval!, stripeConnectAccountId: String!, stripeEnv: VaultStripeEnv = RAISE): VaultMutationResponse!
  vaultStripeConnectAccountRemovePeople(stripeConnectAccountId: String!, stripeEnv: VaultStripeEnv = RAISE): VaultMutationResponse
  vaultStripeConnectAccountRemoveRepresentative(stripeConnectAccountId: String!, stripeEnv: VaultStripeEnv = RAISE): VaultMutationResponse
  vaultStripeConnectAccountUpdate(metadata: StripeMetadataInput, statementDescriptor: String, stripeConnectAccountId: String!, stripeEnv: VaultStripeEnv = RAISE, vaultKybId: ID!): String!
  vaultStripePersonCreate(stripeConnectAccountId: String!, stripeEnv: VaultStripeEnv = RAISE, vaultKycId: ID!): String!
  vaultStripePersonDelete(stripeConnectAccountId: String!, stripeEnv: VaultStripeEnv = RAISE, stripePersonId: String!): String!
  vaultStripePersonUpdate(stripeConnectAccountId: String!, stripeEnv: VaultStripeEnv = RAISE, stripePersonId: String!, vaultKycId: ID!): String!
  vaultUpdateVaultKybAutomated(payableOrgId: ID, raiseFundraiserId: Int): String
  vehicleCreate(input: CreateVehicleInput): Vehicle
  vehicleDelete(input: DeleteVehicleInput): Vehicle
  vehicleModify(input: ModifyVehicleInput): Vehicle
  "Create the log record with type WARNING"
  warning(payload: JSON!, service: AuditLogService!, source: AuditLogSource!): Boolean
  "Deprecated: use cron-jobs instead"
  weeklyEventEmailScheduleSend: IMutationResult
  workerDeleteMany(input: DeleteManyWorkerInput): WorkerDeleteManyCount
  workerPoolCreate(input: CreateWorkerPool): WorkerPool
  workerPoolDelete(input: DeleteWorkerPoolInput): [WorkerPool]
  workerPoolModify(input: ModifyWorkerPoolInput): WorkerPool
  workerUpsertMany(input: UpsertManyWorkersInput): WorkerUpsertManyCount
  workersUpsert(input: [UpsertWorkersInput]): [WorkerPool]
}
type MutationError {
  message: String!
  type: String
}
type MutationResponse {
  errors: [MutationError]!
  properties: OrgsProperties
  success: Boolean!
}
type MyChannels {
  channels: [Channel!]!
  count: Int
}
type MyModeratedChannels {
  channels: [Channel!]!
  count: Int
  orgsCount: Int
}
type NetsuiteWebhookResponse {
  error: String
  success: Boolean
}
type NotAuthenticated implements Error {
  message: String!
}
type NotAuthorized implements Error {
  message: String!
}
type Notice {
  body: String
  created_at: Date
  id: Int!
  isUrgent: Int
  title: String
}
type OAuthCallbackResponse implements Tokens {
  accessToken: String
  challengeId: String
  refreshToken: String
}
type Official {
  address: String
  city: String
  duty: String
  email: String
  event: Event
  event_id: Int
  first_name: String
  home_phone: String
  id: Int!
  last_name: String
  offic_id: String
  organization: String
  paid: String
  pay_code: String
  received: String
  salary: Float
  ssn: String
  state: String
  voucher_number: String
  work_phone: String
  worker_name: String
  zip: String
}
type OfficialAssignmentsReturn {
  grand_total: String
  official_names: [String]
  officials: [TransformedOfficialAssignment]
  total: [Total]
}
type OfficialDuty {
  duty: String
  id: Int
  is_deleted: Boolean
}
type OfficialPool {
  Address: String
  City: String
  First: String
  Home_Phone: String
  ID: String!
  Last: String
  SSN: String
  State: String
  Work_Phone: String
  Zip: String
  cell_phone: String
  email: String
  formattedSSN: String
  is_deleted: Boolean
  vendor_number: String
}
type OfficialSchedules {
  activity: String
  bus_count: Int
  bus_departure_location: String
  bus_early_dismissal_time: String
  bus_estimated_return_time: String
  bus_time: String
  cancellation_status: String
  comments: String
  confirmed: String
  driver_name: String
  driver_phone: String
  end_time: String
  event_date: String
  facility: String
  gender: String
  home_field: String
  id: Int
  levels: String
  official_address: String
  official_address_city: String
  official_address_state: String
  official_address_zip: String
  official_duty: String
  official_email: String
  official_first_name: String
  official_home_phone: String
  official_id: String
  official_last_name: String
  official_paid: String
  official_received: String
  official_row_id: String
  official_salary: String
  official_work_phone: String
  opponent: String
  place: String
  season_id: Int
  sports_code: String
  sports_description: String
  sports_group: String
  sports_name: String
  start_time: String
  vehicle_id: String
  vehicle_type: String
}
type OnException {
  exceptionCode: Int
  message: String
  type: String
}
type Opponent {
  Address: String
  Phone: String
  SchoolCode: String
  SchoolName: String
  State: String
  Zip: String
  ad_name: String
  city: String
  email: String
  fax: String
  is_deleted: Boolean
  nces_id: String
  non_school: Boolean
}
type OrderItem {
  itemId: Int
  orderId: Int
  parentItemId: Int
  price: Float
  productId: Int
  productName: String
  productType: String
  quantity: Int
  sku: String
}
type OrderResult {
  errors: [String]
  netsuiteId: String
  status: OrderStatus
}
type OrdersFilter {
  orders: [MagentoOrder]
  pagination: SDPagination
}
type OrdersSummary {
  baseSales: Float
  complete: BigInt
  discountAmount: Float
  processing: BigInt
  sales: Float
  scopeId: BigInt
  shipped: BigInt
}
type Org {
  createdAt: DateTime
  fields(names: [String] = []): JSONObject
  financialAccounts: [FinancialAccount!]
  id: ID!
  lastMigrated: DateTime
  migrated: DateTime @deprecated(reason: "Please use lastMigrated instead")
  name: String
  parent: Org
  parentId: String @deprecated(reason: "Please use parent.id instead")
  rosters: [OrgsRoster!]
  snapMobileOneAccess: SnapMobileOneAccess
  stores: [Store!]
  type: String!
  updatedAt: DateTime
}
type OrgAffiliation {
  affiliations: [UserAffiliation!]!
  org: Org!
}
type OrgCampaignAffiliation {
  campaigns: [Org!]!
  org: Org!
}
type OrgCampaignIds {
  campaignIds: [ID!]!
  legacyRaiseIds: [Int!]!
  role: ID
  rootOrgId: ID!
}
type OrgPlaceSuggestion {
  description: String!
  placeId: String!
}
type OrgRelationships {
  governingOrg: Org
  org: Org!
  titles: [RelationshipTitles!]!
}
type OrgStaffMember {
  orgAffiliations: [UserAffiliation!]!
  orgId: ID!
  programsLed: [ProgramWithTitle!]!
  teamsLed: [TeamWithTitle!]!
  user: UserNode!
}
type OrgsProperties {
  acctId: ID
  applicationId: ID
  createdAt: DateTime
  customerId: ID
  deletedAt: DateTime
  finAcctId: ID
  firstSeen: DateTime
  internal: Boolean
  isBeneficialOwner: Boolean
  isContact: Boolean
  isPrincipal: Boolean
  isRepresentative: Boolean
  kybId: ID
  kycId: ID
  lastMigrated: DateTime
  lastSeen: DateTime
  memberCount: Int
  migrated: DateTime @deprecated(reason: "Please use lastMigrated instead")
  orgId: ID
  parentId: ID
  personId: ID
  processor: Processor
  programId: ID
  roleId: ID
  rosterId: ID
  status: FinAcctStatus
  type: [String]
  updatedAt: DateTime
  userId: ID
}
type OrgsRoster {
  activity: ProgramActivity
  campaign: Org
  createdAt: DateTime!
  description: String
  gender: TeamGender
  id: ID!
  lastMigrated: DateTime
  members: [RosterUser!]!
  name: String!
  org: Org
  product: [RosterCreator!]!
  startYear: Int!
  updatedAt: DateTime
  userDirectoryMembers: [UserPublic!]!
}
type OrgsSearchResponse {
  hasNext: Boolean
  hasPrevious: Boolean
  orgs: [Org]
  total: Int
}
type OtkFundraiserProduct {
  "This otk item image will include the fundraiser logo."
  dynamicImage: String
  incentives: OtkProduct
}
type OtkParticipantData {
  id: ID!
  incentives: OtkProduct
  size: String
}
type OtkProduct {
  description: String
  fitting: String
  id: ID!
  "This is the base image for the otk item. It will not include the fundraiser logo."
  image: String
  name: String
  priceCents: Int
  productType: String
  sizes: String
}
type Pagination {
  currentPage: Int!
  itemCount: Int!
  pageSize: Int!
}
type Participant {
  campaignId: String
  email: String
  firstName: String
  id: ID!
  lastLogin: String
  lastName: String
  role: String
}
"See CampaignMembership for details"
type ParticipantCampaign {
  areDonationInviteEmailsReleased: Boolean!
  basicStatus: BasicCampaignStatus!
  campaignDates: CampaignDates
  campaignGoal: Int
  donationsRaised: ParticipantDonationsRaised
  "Getting donor email addresses adds time to your query"
  donorEmailData: [DonorEmailData]
  donorPersonListEntries: [DonorPersonListEntry]
  "Getting donor phone numbers adds time to your query"
  donorPhoneNumbers: [DonationInviteSMSData]
  fundraiserRewardLevelsCount: FundraiserRewardLevelsCount
  galleryItems: GalleryItems
  group: ParticipantGroup
  hasCustomRewards: Boolean
  id: ID!
  isCoparticipant: Boolean
  isGuardianLedCampaign: Boolean!
  isOTKEnabled: Boolean
  isRewardsEnabled: Boolean
  isTopEarner: Boolean!
  joinCode: String
  name: String!
  newStack_participantFundraiserLink: String
  otk: [OtkParticipantData]
  participantGoal: Int
  participantGuardianEmails: ParticipantGuardianEmail
  participantRewardsData: [ParticipantRewardsData]
  primaryColor: String!
  raiseUserId: Int
  raiseUserJoinedAt: String
  slug: String
  "Datetime-ISO format for sms invites released"
  smsInvitesReleasedAt: String
  status: CampaignStatus!
  userFitting: UserFitting
}
type ParticipantCheer {
  anonymous: Boolean
  createdAt: DateTime
  donorMessage: String
  donorName: String
  firstName: String
  id: Int
  images: [String]
  lastName: String
  subtotalCents: Int
}
type ParticipantCheerwall {
  cheers: [ParticipantCheer]
  count: Int
}
type ParticipantData {
  data: User
  raiseId: Int
}
type ParticipantDonationInvalidEmails {
  email: String!
  message: String!
}
type ParticipantDonationInvite {
  deliveryStatus: EmailDeliveryStatus!
  email: String!
  id: ID!
}
type ParticipantDonationInviteResponse {
  addedInvites: [ParticipantDonationInvite!]
  invalidEmails: [ParticipantDonationInvalidEmails!]
  removed: [ID!]
}
type ParticipantDonationsRaised {
  numberOfDonations: Int!
  subtotalCents: Int!
}
"This is just a Fundraiser, but specifically for a participant"
type ParticipantFundraiser {
  emailInvitesReleasedAt: Date
  id: ID
  name: String
  origin: String
  primaryColor: String
  raiseId: Int
  secondaryColor: String
  slug: String
  smsInvitesReleasedAt: Date
}
type ParticipantFundraiserConfiguration {
  autoImportEmails: ParticipantFundraiserConfigStates!
  autoImportTexts: ParticipantFundraiserConfigStates!
  fundraiserId: String!
  fundraiserParticipantId: String!
  giftShop: ParticipantFundraiserConfigStates!
  guardianSetup: ParticipantFundraiserConfigStates!
  id: ID
  profileSetup: ParticipantFundraiserConfigStates!
  raiseFundraiserId: Int
  rewards: ParticipantFundraiserConfigStates!
}
type ParticipantFundraiserParticipant {
  fundraiserId: ID
  id: ID!
  lastLoginAt: Date
}
type ParticipantGroup {
  id: ID
  label: String
}
type ParticipantGuardianDonorEntry {
  email: String
  error: String
  fundraiserId: Int
  id: Int
}
type ParticipantGuardianEmail {
  guardianEmail: String
  secondaryGuardianEmail: String
}
type ParticipantGuardianEmailUpdate {
  id: Int
}
type ParticipantGuardianFundraiser {
  id: ID!
  logo: String
  name: String!
  participants: [ParticipantUser]
  slug: String!
  status: BasicCampaignStatus!
}
type ParticipantGuardianReturn {
  entry: [ParticipantGuardianDonorEntry!]
}
type ParticipantJoinData {
  isSmaApp: Boolean
  joinLink: String
}
type ParticipantList {
  count: Int
  cursor: String
  list: [Participant]!
}
type ParticipantPendingInvite {
  hasPendingInvite: Boolean!
  joinCode: String
}
type ParticipantPreloadDonationInvitesHistoryResponse {
  emails: [String!]
  phoneNumbers: [String!]
}
type ParticipantPublic {
  firstName: String
  "User directory id"
  id: String
  lastName: String
  profilePicture: String
}
"A 'tremendous' reward. The presence means that a reward has been sent to a participant"
type ParticipantReward {
  id: ID
  status: RewardStatus
}
type ParticipantRewardsData {
  id: Int
  incentives: FundraiserRewardsProduct
  size: String
  tier: Int
}
type ParticipantSSOID {
  userDirectoryId: String
}
type ParticipantSmsDonationInvite {
  id: ID!
  participant: ParticipantFundraiserParticipant
  phoneNumber: String!
  status: ParticipantDonationInviteStatus!
}
type ParticipantTextTemplate {
  guardianTemplate: String
  participantTemplate: String
}
type ParticipantTopDonation {
  donorName: String
  subtotalCents: Int
}
type ParticipantUser {
  fundraiserUserId: Int!
  user: User!
}
type ParticipantsInfo {
  count: Int
  loggedInCount: Int
  withAtLeastOneDonation: Int
  withAtLeastTwentyInvites: Int
}
type PayableInfo {
  campaignId: Int
  city: String
  country: String
  ein: Int
  fullAddressOne: String
  fullAddressTwo: String
  name: String
  payableName: String
  payableType: String
  region: String
  scopeId: Int
  street: String
  zip: Int
}
type Payee {
  address: PayeeAddress!
  name: String!
}
type PayeeAddress {
  apartment: String
  attention: String
  city: String!
  state: String!
  street: String!
  zip: String!
}
type PaymentsApiAccountByEntityResponse {
  entity: String
  id: String!
  stripeAccountId: String
  unitAccountId: String
}
type PaymentsApiCreateRefundResponse {
  applicationFeeRefundAmount: Int
  id: String!
  reason: String
  refundApplicationFee: Boolean
  transactionId: String
}
type PaymentsApiCustomerPaymentMethod {
  billingAddress: PaymentsApiPMAddress
  brand: String
  expiration: PaymentsApiPMExpiration
  id: String!
  identifier: String!
  type: PaymentsApiCustomerPaymentMethodType!
  zipCode: String
}
type PaymentsApiCustomerResponse {
  customerId: String
  email: String!
  name: String
  paymentMethods: [PaymentsApiCustomerPaymentMethod]!
}
type PaymentsApiDetachPaymentMethodResponse {
  paymentMethodId: String!
  stripeEnv: StripeEnv!
}
type PaymentsApiPMAddress {
  city: String
  country: String
  line1: String
  line2: String
  postalCode: String
  state: String
}
type PaymentsApiPMExpiration {
  month: Int!
  year: Int!
}
type PaymentsApiPaymentResponse {
  amount: Int!
  id: String!
  paymentMethodId: String!
  snapAmount: Int!
  snapId: String!
  stripeEnv: StripeEnv!
}
type Permission {
  description: String
  id: String
  name: String
}
type PhoneNumberChallengeConfirmation implements Tokens {
  accessToken: String
  refreshToken: String
}
type PointActivityFilter {
  activities: [MagentoPointActivity]
  pagination: SDPagination
}
type PostponeUnpostponeResponse {
  affected_events: Int
  success: Boolean
}
type PotentialCustomersDeals {
  activity: String
  campaignsCount: Int
  curricularType: String
  id: String
}
"Information of a emails scheduled for release"
type PreloadEmailSchedule {
  "The date and time for release"
  dateTime: DateTime
  "The campaign id"
  fundraiserId: Int
  "The timezone for scheduled release"
  timezone: String
}
"Information of premade email templates"
type PremadeContactTemplate {
  "The message of the template. Plain text only"
  message: String
  "The name of the template"
  name: String
  "The subject of the template"
  subject: String
  templateMedium: ContactTemplateMedium
  "The type of template"
  templateType: ContactTemplateType
}
type Preparation {
  duty: String
  id: Int!
  is_deleted: Boolean
  qty: String
}
type PreparationReport {
  comments: String
  event: Int
  id: Int
  prep: String
  qty: String
}
type PreparationSheet {
  activity: String
  bus_count: Int
  comments: String
  confirmed: String
  driver_name: String
  driver_phone: String
  end_time: String
  event_date: String
  facility: String
  fee: Float
  g_s: String
  gender: String
  home_field: String
  id: Int
  levels: String
  place: String
  preparations: [PreparationReport]
  season_id: Int
  sports_code: String
  sports_description: String
  sports_group: String
  sports_name: String
  start_time: String
  vehicle_id: String
  vehicle_type: String
}
"Primary Group Leader The primary group leader is the main contact for a fundraiser The association is not direct with a user but made through email"
type PrimaryGroupLeader {
  "Signifies user is primary group leader. Extra query."
  currentUserIsPrimary: Boolean!
  email: String
  fullName: String
  phoneNumber: String
}
type ProductResult {
  errors: [String]
  id: String
}
type ProfitPercentage {
  points_percentage: String
  updated_at: String
  updated_product_price_percentage: String
}
type ProgramLeadership {
  org: Org!
  programs: [ProgramWithTitle!]!
}
type ProgramWithTitle {
  program: Org!
  titles: [Leader!]!
}
type PublicFundraiserData {
  alternateColor: String
  description: String
  donationMinDollars: Int
  endDate: DateTime
  fanStore: Boolean
  goal: Int
  hasCustomRewards: Boolean
  id: ID!
  incStore: Boolean
  isGuardianLedCampaign: Boolean
  joinCode: String
  logo: String
  name: String
  participantGoal: Int
  personalMessage: String
  primaryColor: String
  programLeader: String
  programType: String
  secondaryColor: String
  slug: String
  startDate: DateTime
  status: String
  storeUrl: String
  whyDonations: String
}
type PublicGroup {
  id: String!
  name: String!
  seasons: [PublicSeason!]
}
type PublicGroupResponse {
  groups: [PublicGroup!]!
}
type PublicSeason {
  id: String!
  isLinkEnabled: Boolean!
  name: String!
  paymentScheduleStatus: Boolean!
}
type PushNotificationResponse {
  id: String!
}
type Query {
  accountTransactions(input: AccountTransactionsInput!): [Transaction]!
  auditLogs(limit: Int! = 50, offset: Int! = 0): AuditLogList
  "Returns donations data for multiple participants of a fundraiser."
  bulkParticipantsDonationsQuery(fundraiserId: Int!, participantIds: [Int!]!): [TotalDonationsRaised]!
  busSchedules(input: CommonSchedulesFiltersInput): [busSchedules]
  "Return startDateTime and endDateTime as formatted UTC strings"
  campaignDates(campaignId: String!): CampaignDates
  "Get fundraiser approval submission associated to campaigns. Latest submission only."
  campaignFundraiserApprovalSubmissions(campaignRaiseIds: [Int!]!, status: [FundraiserApprovalSubmissionStatus]): [FundraiserApprovalSubmission!]!
  campaignHistoryListGet(fundraiserId: Int): [CampaignHistoryList]
  campaignKyc(campaignId: String!): CampaignKyc
  "Query uses Users JWT to find their Memberships See CampaignMembership type for details"
  campaignMemberships(basicStatus: BasicCampaignStatus, fundraiserId: ID): [CampaignMembership!]!
  "List of saved search filters for campaigns of current user."
  campaignSearchFiltersGet: [CampaignSearchFilter]
  "List campaigns based on logged-in user"
  campaigns(
    "Filter campaigns by account-manager. Applicable for sales manager only."
    accountManagerIds: [Int!],
    "Filter closed campaigns days closed. Valid values are '0-31' and '32+'"
    daysClosed: String,
    "Filter upcoming campaigns by the days to launch. Valid values are '0-14', '15-45', '46+'"
    daysToLaunch: String,
    "@Deprecated. Ignore logged-in context and show all"
    fetchAllCampaigns: Boolean,
    """
    Set Team-Selling context. If true, it will list campaigns that reassigned to account-manager"
    """
    getTeamSelling: Boolean,
    "number of campaigns returned per page. Default is 25"
    limit: Int,offset: Int,
    "Filter campaigns by salesreps. Applicable for sales manager only"
    salesrepIds: [Int!],
    "Filter the campaigns by name"
    searchTerm: String,sortDirection: SortDirection, sortField: SortField,
    "Status of campaigns. Valid values are 'closed', 'finalized', 'settled', 'active' and 'upcoming'"
    status: String
  ): CampaignList
  "Retrieve contact activity"
  commsContact(id: ID!): [CommsContactResponse!]
  "Get email logs"
  commsGetEmailLogs: [EmailLog]
  "Retrieve Content"
  commsHtml(data: JSON, mjml: String!): CommsHtmlResponse
  "Retrieve message activity"
  commsMessage(id: ID!): CommsMessageResponse!
  "Retrieve template details"
  commsTemplate(id: ID!): CommsTemplateResponse
  "Retrieve all templates for a service"
  commsTemplates(name: String, service: SnapService, transport: MessageTransportType): [CommsRegisterResponse!]
  "Retrieve Service Unsubscribe Groups"
  commsUnsubscribeGroups(service: SnapService): CommsUnsubscribeGroupsResponse
  competitionList(input: GetCompetitionListInput): [TransformedEventReturn]
  conference(conferenceSlug: String, id: ID): Conference!
  conferences: [Conference!]!
  consumers(hidden: Boolean = false): [Consumer]
  "List of contact templates based on current user"
  customContactTemplatesGet: [CustomContactTemplates]
  dailyCalendarBusSchedules(input: GetDailyCalendarBusScheduleByDateInput): TransformedDailyCalendarBusSchedule
  dailyCalendarEvents(input: GetDailyCalendarEventsByDateInput): [TransformedDailyCalendarEventReturn]
  dailyCalendarOfficials(input: GetDailyCalendarOfficialsByDateInput): TransformedDailyCalendarOfficials
  dailyCalendarPreparations(input: GetDailyCalendarPreparationsByDateInput): TransformedDailyCalendarPreparation
  dailyCalendarWorkers(input: GetDailyCalendarWorkersByDateInput): TransformedDailyCalendarWorkers
  daySchedules(input: CommonSchedulesFiltersInput): [CommonSchedules]
  donationLevels(raiseFundraiserId: Int!): [DonorsDonationLevel!]!
  donationLevelsByFundraiserId(fundraiserId: String!): [DonationLevel!]!
  donationTopEarner(fundraiserId: String!): DonationTopEarner @deprecated(reason: "This can be queried for specific personas via CampaignMemberships.")
  donationsQuery(fundraiserId: Int!, participantId: Int): DonationsResponse
  donorAlumni(donateIntentId: String!): AlumniDonorSchoolDetails
  donorDonateIntent(browserSecret: String, id: ID!): DonateIntent
  donorFundraiserData(slug: String): DonorFundraiserData!
  donorPersonListEntries(fundraiserId: Int!, userId: Int!): [DonorPersonListEntry]
  donorsCheerwall(fundraiserId: Int!, participantId: Int, take: Int!): DonorsFundraiserCheerWall @deprecated(reason: "Use `donorsCheerwalls` query instead")
  donorsCheerwalls(fundraiserId: Int!, participantId: Int, take: Int!): DonorsCheerwallsResult
  "Retrieve a donation a donor has made"
  donorsDonation(donateIntentId: String, donationId: String): DonorDonation
  donorsDonationStats(where: DonorsDonationStatsWhereInput!): DonorsDonationStatsResponse
  donorsFundraiserPayables(input: DonorFundraiserPayablesInput!): [DonorFundraiserPayable]
  donorsGeneratePdfFlyer(participantId: ID, slug: String!): String
  donorsGiftShopIntent(purchaseIntentId: String!): GiftShopPurchaseIntentData!
  donorsGiftShopPurchase(purchaseId: String!): DonorFundraiserPurchase
  donorsOrgs(pagination: DonorsOrgsSearchPagination, where: DonorsOrgsSearchInput!): DonorsOrgsResult!
  donorsParticipantOTKIncentives(fundraiserId: String!, participantId: String!): DonorsParticipantOTKIncentives
  donorsParticipantsGiftShop(fundraiserId: String!, participantId: String!): DonorsParticipantsGiftShop
  donorsRedirects(limit: Int, offset: Int): [Redirect!]!
  "List all activity types of campaigns. Used by Drive"
  driveActivityTypes: [String]
  "Get details of campaigns. This endpoint need to considered to combined with searchDriveCampaign endpoint"
  driveCampaignDetails(fundraiserId: Int!): DriveCampaignDetails
  "Get statistics of the set of campaigns returned by the filter. This endpoint are meant to be used with the searchDriveCampaigns endpoint. Arguments should be the same."
  driveCampaignSearchStats(activityTypes: [String!], campaignName: String, campaignStatuses: [CampaignStatus!], endDate: DateTime, fundraiserId: Int, hasIncentive: Boolean, maxTeamSize: Int, maxTotalRaisedCents: Int, minTeamSize: Int, minTotalRaisedCents: Int, organizationIds: [Int!], organizationTypes: [String!], salesrepIds: [Int!], startDate: DateTime, territories: [String!], usStates: [String!]): DriveCampaignSearchStatistics
  "Show details of a specific event"
  driveEvent(
    "Takes in the event id"
    id: ID!
  ): DriveEvent
  "List events"
  driveEvents(limit: Int = 20, offset: Int = 0, sortBy: SortDirection, sortField: String, where: DriveEventSearchInput): DriveEventResults
  driveGetEarlyAccessFunds(activityType: String!, campaignStatus: String, fundraiserId: Int!, participantCount: Int!, state: String!): earlyAccess
  driveGetListActivitiesAvailableList(hubspotId: String, limit: Int = 25, offset: Int = 0, sortField: String): driveGetListPotentialCustomersReturn
  driveGetListActivitiesNotAvailableList(hubspotId: String, limit: Int = 25, offset: Int = 0, sortBy: SortDirection, sortField: String): driveGetListPotentialCustomersReturn
  driveGetListCampaignHistoryList(hubspotId: String, limit: Int = 25, offset: Int = 0, sortField: String): driveGetListCurrentCustomersReturn
  driveGetListCurrentCustomersList(hubspotId: String, limit: Int = 25, offset: Int = 0, sortField: String): driveGetListCurrentCustomersReturn
  driveGetListHubspotProspectsList(hubspotId: String, limit: Int = 25, offset: Int = 0, sortField: String): DriveGetlistProspectsReturn
  driveGetListOrgList(limit: Int = 25, offset: Int = 0, orgName: String, sortBy: SortDirection, sortField: String): DriveOrgListResults
  "Returns Organization Summary information of single organization"
  driveGetListOrganizationSummary(
    "Hubspot org ID"
    hubspotId: String,
    "Entity ID of the organization. It is mapped with **'fields._deprecated_raise_id'** in **'Org'** entity"
    orgId: Int!
  ): DriveGetListOrganizationSummaryReturn
  driveGetListPotentialCustomersList(hubspotId: String, limit: Int = 20, offset: Int = 0, sortField: String): driveGetListPotentialCustomersReturn @deprecated(reason: "changed name to driveGetListActivitiesAvailableList")
  driveGetListWinbackList(hubspotId: String, limit: Int = 25, offset: Int = 0, sortField: String): DriveGetlistWinbackReturn
  driveGetProjectedRaised(activityType: String!, participantCount: Int!, state: String!): earlyAccess
  "Return all tracking of current user to specific organization in Drive's Get List app."
  driveOrgUserTrackings(
    "Org ID of the organization. It is mapped with **'fields.id'** in **'Org'** entity"
    orgId: String!,where: DriveOrgUserTrackingSearchInput  ): [driveOrgUserTrackingReturn]
  "List organization types used by Drive"
  driveOrganizationTypes: [String]
  "List all salesreps"
  driveSalesreps: [DriveAmSalesReps]
  "List all salesreps which had re-assigned any campaigns to current user. This endpoint is applicable for account-manager only."
  driveSalesrepsByAccountManager: [DriveAmSalesReps]
  "List all salesreps which managed by current user. This endpoint is applicable for sales manager only."
  driveSalesrepsByManager(type: SalesrepType): [DriveAmSalesReps]
  driveSmsInviteData(fundraiserId: Int!): SmsInviteData
  "Search Raise users by UserDirectory 's id"
  driveUsers(limit: Int = 20, offset: Int = 0, where: DriveUserSearchInput): DriveUsersResult @deprecated(reason: "Use UserDirectory 's users query instead ")
  "List Event-Tracker's venues"
  driveVenues(where: DriveVenueSearchInput): [DriveVenue]
  event(input: GetEventByIdInput): Event!
  eventCalendar(input: EventCalendarFiltersInput): [CommonCalendar]
  eventContractItems(input: eventContractItemInput): [ContractItem]
  eventOfficial(input: GetOfficialByIdInput): Official!
  eventOfficials: [Official]
  eventParticipants(input: GetEventParticipantsByIdInput): EventParticipants!
  eventPreparations(input: GetEventPreparationsByIdInput): EventPreparations!
  eventPreparationsByEvent(input: GetEventPreparationsByEventIdInput): [EventPreparations]
  eventResult(id: ID!): EventResult
  eventTransportDetailsByEventOrDates(input: GetEventTransportDetailsByEventOrDatesInput): [EventTransportDetails]
  eventWorker(input: GetWorkerByIdInput): Worker!
  eventWorkers: [Worker]
  eventWorkersByEvent(input: GetWorkersByEventIdInput): [Worker]
  events: [Event]
  eventsParticipants: [EventParticipants]
  eventsPreparations: [EventPreparations]
  eventsTransportDetails: [EventTransportDetails]
  facilities: [Facility]
  facility(input: GetFacilityByPlaceNameInput): Facility!
  facilityCalendar(input: CommonCalendarFiltersInput): [CommonCalendar]
  facilityGrid(input: CommonGridFiltersInput): [CommonGrid]
  facilitySchedules(input: CommonSchedulesFiltersInput): [CommonSchedules]
  family: Family
  feeReportSheets(input: CommonSheetFiltersInput): [CommonSheet]
  "Get all Financial Accounts that are associated with a Stripe Connect Account Id If you are looking for Financial Accounts associated with a parent Org, please use the 'financialAcctRoot' query."
  financialAcctAcctId(acctId: ID!): [FinancialAcctOrg!]!
  "Get all Financial Accounts that are associated with an Org using the Org Id. If you are looking for Financial Accounts associated with a parent Org, please use the 'financialAcctRoot' query."
  financialAcctOrg(orgId: ID!): [FinancialAcctOrg!]!
  "Get all Financial Accounts that are associated with an Org using the Kyb Id. If you are looking for Financial Accounts associated with a parent Org, please use the 'financialAcctRoot' query."
  financialAcctOrgByKyb(kybId: ID!): [FinancialAcctOrg!]!
  "Get all Financial Accounts that are associated with a parent Org. This includes parents of parents and all the way up the tree. This will NOT return the Financial Accounts for the target Org, please use the 'financialAcctOrg' query for specific Orgs."
  financialAcctRoot(orgId: ID!): [FinancialAcctOrg!]!
  "Get all User data associated with a single Financial Account with an Orgs Platform Id. Eg: 'finacct_stripe_abcd1234'"
  financialAcctUsers(finAcctId: ID!): [FinancialAcctUser!]!
  "Get all User and Financial Account data associated with a single Kyc Id. Eg: 'vkyc_abcd1234'"
  financialAcctUsersByKyc(kycId: ID!): [FinancialAcctUser!]!
  fundraiser(id: ID!): Fundraiser
  fundraiserApprovalSubmissions(approverId: String, campaignIds: [Int], formId: Int, latest: Boolean, status: FundraiserApprovalSubmissionStatus, submitterId: String!): [FundraiserApprovalSubmission]!
  "Get fundraiser approval submissions authenticating request by approver's token"
  fundraiserApprovalSubmissionsHistory(formId: Int, token: String): [FundraiserApprovalSubmission!]!
  "Get fundraiser approval submission related org names authenticating request by approver's token"
  fundraiserApprovalSubmissionsOrgNames(submissionId: Int, token: String): FundraiserApprovalSubmissionsOrgNamesResult!
  fundraiserCheerwall(fundraiserId: String!, take: Int!): FundraiserCheerwall
  fundraiserCustomRewardsDetails(fundraiserId: String!): [FundraiserCustomRewardsData]
  fundraiserDataByCoachJoinCode(coachJoinCode: String!): FundraiserDataByCoachJoinCode
  fundraiserDriveInvitesDetail(fundraiserId: ID!): FundraiserDriveInvitesDetail!
  "resourceId is the fundraiserId"
  fundraiserEntityResources(resourceId: Int!): FundraiserEntityResource
  fundraiserGroups(joinCode: String!): [FundraiserGroup]
  fundraiserMediaGallery(fundraiserId: ID!): [FundraisersMediaGallery!]
  fundraiserRaisedAmount(fundraiserId: String!): FundraiserRaisedAmount
  fundraiserRewardsDetails(fundraiserId: String!): [FundraiserRewardsProduct]
  fundraiserTopDonation(fundraiserId: String!): FundraiserTopDonation
  fundraiserUserRole(fundraiserId: Int!): FundraiserUserRole
  fundraisersActivationEligibility(fundraiserId: String!): FundraiserActivationEligibilityResponse
  fundraisersFinalizationEligibility(fundraiserId: String!): FundraiserFinalizationEligibilityResponse
  fundraisersShippingAddressConfirmed(fundraiserId: String!): Boolean!
  galleryItems(fundraiserId: String!): GalleryItems
  gearLogoConfigurationApproved(fundraiserId: String!): Boolean!
  getCampaignRoster(campaignId: String!): [Roster]
  getFundraiserUserID: Int
  getInsightsUserOrgs(userId: Int!): [InsOrg]!
  getSalesRepHome(zip: String!): HmSalesRep!
  getUserSavedSalesRep(udId: String!): UserSavedRep!
  "return the most recently created fundraiser associated with the group leader"
  groupLeaderDefaultFundraiser: GroupLeaderFundraiser
  groupLeaderEmailDonationInvites(fundraiserId: ID!): [GroupLeaderEmailDonationInvite!]
  "return a fundraiser by id"
  groupLeaderFundraiser(fundraiserId: ID!): GroupLeaderFundraiser
  groupLeaderFundraiserManager(fundraiserId: ID!): GroupLeaderFundraiserManager
  groupLeaderFundraiserManagerAlert(alertType: FundraiserManagerAlertType, fundraiserId: ID!): [FundraiserManagerAlert!]
  groupLeaderFundraiserParticipants(fundraiserId: ID!): [GroupLeaderFundraiserParticipant!]
  "return Fundraiser Pdf based on type"
  groupLeaderFundraiserPdf(fundraiserId: ID!, type: GroupLeaderFundraiserPdfType!): String
  "return a list of fundraisers associated with the group leader, paginated, and filtered by role and status"
  groupLeaderFundraisers(limit: Int, offset: Int, roles: [FundraiserGroupLeaderRoles!], status: [FundraiserStatus!]): GroupLeaderFundraisersPaginated
  groupLeaderParticipantGroups(fundraiserId: ID!): [GroupLeaderParticipantGroup!]
  groupLeaderPendingParticipantJoinFundraiserInvites(fundraiserId: ID!, inviteType: JoinFundraiserInviteType): [InviteFundraiserSentResponse!]
  groupLeaderRoster(fundraiserId: ID!, rosterId: ID!): GroupLeaderRoster
  groupLeaderSmsDonationInvites(fundraiserId: ID!): [GroupLeaderSmsDonationInvite!]
  "List of group leaders"
  groupLeaders(cursor: String, groupLeaderId: Int, limit: Int): GroupLeaderList
  helpDocumentsList: [String]
  infoSheets(input: CommonSheetFiltersInput): [CommonSheet]
  insightsCampaignDonations(campaignIds: [Int]!): [InsCampaignDonation]!
  insightsCampaignRaiseEntityInfo(campaignId: Int!): InsCampaignRaiseEntityInfo!
  insightsCampaignSettlements(campaignIds: [Int]!): [InsCampaignSettlement]!
  insightsCampaignsData(campaignIds: [Int]!): InsCampaignsData!
  insightsDonorParticipantContacts(campaignIds: [Int]!): [InsDonorParticipantContact]!
  insightsNotificationPreferences(userId: String!): insightsUserOrgsNotificationPreferencesResponse!
  insightsOrgsCampaignsSummary(orgCampaignIds: [InsOrgCampaignSummaryInput]!): [InsOrgCampaignSummary]!
  insightsPreApprovedContacts(orgIds: [String]): [InsPreApprovedContact]!
  insightsPreApprovedContactsEmailTest(emails: [String]!, orgId: String!): [InsEmailTestPreApprovedContactResult]!
  insightsSalesRepInfo(salesRepEmail: String!): InsSalesRepInfo
  insightsUserPreferences(campaignIds: [Int], orgIds: [String], queryType: UserPreferenceQueryType!, userId: String!): [InsightsGetUserPreference]
  invite(id: String): InviteInfo
  invites(filter: InviteFilter, limit: Int! = 25, offset: Int = 0, showAll: Boolean): [InviteList]
  invitesList(currentPage: Int! = 0, pageSize: Int! = 25, search: String, showAll: Boolean): InvitesListResponse
  "joinCode is a string that is used to join a fundraiser"
  joinCodeType(joinCode: String!): JoinCodeType!
  legacyTransactions(input: LegacyTransactionsInput!): LegacyTransactionsOutput!
  level(input: GetLevelByIdInput): Level!
  levelGrid(input: CommonGridFiltersInput): [CommonGrid]
  levels: [Level]
  manageCoach(id: Int!): ManageCoach
  manageCoachList(filter: ManageCoachListFilter): ManageCoachList
  manageEvent(id: Int!): ManageEvent
  manageEventList(filter: ManageEventListFilter): ManageEventList
  "Get a manage organization by web folder"
  manageOrganization: ManageOrganization
  "Get a list of all manage organizations"
  manageOrganizationList(filter: ManageOrganizationFilter): ManageOrganizationList
  managePlayerList(filter: ManagePlayerListFilter): ManagePlayerList
  manageProgram(id: Int!): ManageProgram
  manageProgramList(filter: ManageProgramListFilter): ManageProgramList
  manageSeason(seasonId: Int!): ManageSeason
  manageSeasonList(filter: ManageSeasonListFilter!): ManageSeasonList
  manageUser(input: ManageUserInput): ManageUser
  manageUsersList(input: ManageUsersListOptions): ManageUsersList
  me: UserWithPermissions
  messagesChannel(assignment: AssignmentInput, id: String, inviteCode: String): ChatChannelResult
  messagesChannelInviteLinks(id: String!): MessagesChannelInviteLinksResult!
  messagesChannelInvites(id: String!): MessagesChannelInvitesResult
  messagesChannelMembers(id: String!): MessagesChannelMembersResult
  messagesChannelOrg(id: String!): MessagesChannelOrgResult!
  messagesChannels(filters: MessagesChannelsFilterInput): ChatChannelsResult
  messagesMyChannels(filters: MessagesMyChannelsFilterInput): MessagesMyChannelsResult
  messagesMyModeratedChannels: MessagesMyModeratedChannelsResult
  messagesUnreadCount(filters: UnreadCountFiltersInput): ChatUnreadMessagesResult
  notices: [Notice]
  officialAssignments(input: GetOfficialAssignmentsInput): OfficialAssignmentsReturn
  officialDuties: [OfficialDuty]
  officialDuty(input: GetOfficialDutyByIdInput): OfficialDuty
  officialListsReport(input: EventOfficialReportFilter): [EventOfficialReport]
  officialPoolById(input: GetOfficialPoolByIdInput): OfficialPool!
  officialPools: [OfficialPool]
  officialSchedules(input: CommonSchedulesFiltersInput): [OfficialSchedules]
  opponent(input: GetOpponentBySchoolCodeInput): Opponent!
  opponentEventsApplyFilters(input: GetEventsFilteredByOpponentStartAndEndDate): [EventsOpponent]
  opponentSchedules(input: CommonSchedulesFiltersInput): [CommonSchedules]
  opponents: [Opponent]
  """
  Get Org Node properties by its Org id. If hierarchy is set to true, it will return all the children of the organization, but NOT the target Org. 'fields' returns JSON of all remaining properties. You can optionally limit this to specific fields by passing in an array of keys. Example: "names": [ "country", "zip_code" ]
  """
  org(hierarchy: Boolean! = false, id: ID!): [Org]
  orgAddressSearch(address: String!): [OrgPlaceSuggestion!]
  """
  Get Org Node properties from the campaign's Org id (eg: "cam_abc123") You can request a specifc Org type by passing in a label. If hierarchy is set to true, this query will return every Org all the way to the root
  """
  orgCampaignId(campaignId: ID!, hierarchy: Boolean! = false, label: OrgType! = SCHOOL): [Org]
  "Get all Campaigns under a specific Org. This will return an array of all Campaigns in the type of Org, sorted by start_date descending."
  orgCampaigns(orgId: ID!): [Org]
  "Get fundraiser approval submission associated to orgs. Latest submission only."
  orgFundraiserApprovalSubmissions(orgIds: [String!]!, status: [FundraiserApprovalSubmissionStatus]): [FundraiserApprovalSubmission!]!
  "Get Org Node properties by its Hubspot id."
  orgHubspotId(hubspotId: String!): [Org!]!
  "Get an Organization's Org platform id by its Raise entities.id OR Get a Campaign's Org platform id by its Raise fundraisers.id This ONLY returns an Org id, not the full Org Node."
  orgId(raiseId: Int!): ID
  """
  Get the Org paid by a given campaign id. This required an Org Platform Campaign Id (eg: "cam_abc123") If you only have a Raise Fundraiser Id, you can use the 'orgId' query to find it.
  """
  orgPayable(campaignId: ID!): [Org]
  "Fuzzy search for Orgs by their name property, returned in order of match quality. Optionally accepts label, ein, city, two letter state code, or zip code. This search has a default limit of 25 and a maximim limit of 100 responses."
  orgSearch(city: String = "", ein: String = "", label: OrgType, limit: Int = 25, matchStyle: MatchStyle, name: String!, offset: Int = 0, stateCode: StateCode, zipCode: String = ""): [Org!]!
  "Get all Users that are directly 'affiliated with' or 'lead' a Program or Team under a specific Org."
  orgStaffRoster(orgId: ID!): [OrgStaffMember!]!
  "Get all Users that are associated with an Org and a specific product. This includes role and a boolean if the user is internal (snap-raise or snapraise email)."
  orgUserAssociations(orgId: ID!, product: Product!): [UserNode]!
  "This has been deprecated. Please use orgSearch which has been optimized and has more features. Search for Orgs by their name property, with an option to specify label. This will only return the first 100 orgs."
  orgs(label: OrgType, nameIncludes: String!): [Org!]! @deprecated(reason: "Please use orgSearch instead")
  "This has been deprecated. Please use orgSearch which has been optimized and has more features. This legacy search searches by type or specific properties. Default limit is 25 items."
  orgsSearch(pagination: OrgsSearchPaginationInput, where: OrgsSearchWhereInput): OrgsSearchResponse @deprecated(reason: "Please use orgSearch instead")
  "This query will return OTK Fundraiser Product details"
  otkFundraiserData(fundraiserId: ID!): [OtkFundraiserProduct]
  "This query will return OTK Participant details"
  otkParticipantDetails(fundraiserId: ID!): [OtkParticipantData]
  "This query will return the most recent OTK Participant details"
  otkPastParticipantDetails(fundraiserId: ID!): [OtkParticipantData]
  participantCampaignConfigurationData(campaignID: ID!): participantCampaignConfiguration
  participantCheerwall(fundraiserId: String!, participantId: String!, take: Int!): ParticipantCheerwall
  participantDonationInvites(raiseId: Int!): [ParticipantDonationInvite!]
  participantDonationsRaised(fundraiserId: Int!, userId: Int!): ParticipantDonationsRaised!
  "This query will return the incentives a Participant earned for a given fundraiser"
  participantEarnedRewardsDetails(fundraiserId: String!, userId: Int!): [ParticipantRewardsData]
  participantFundraiser(fundraiserId: String!): ParticipantFundraiser
  "Accepts a udid and returns a participantFundraiser object"
  participantFundraiserConfiguration(fundraiserId: String, raiseFundraiserId: Int): ParticipantFundraiserConfiguration
  participantFundraiserParticipant(fundraiserId: String, raiseFundraiserId: Int): ParticipantFundraiserParticipant
  participantFundraiserReward(raiseId: Int!): ParticipantReward
  participantFundraisers(udid: String!): [ParticipantFundraiser]!
  participantGuardianFundraisers: [ParticipantGuardianFundraiser]
  participantPendingInvite(email: String!): ParticipantPendingInvite
  participantPreloadDonationInvitesHistory: ParticipantPreloadDonationInvitesHistoryResponse
  participantPublic(fundraiserId: Int!, userId: Int!): ParticipantPublic
  "This query will return Participant Rewards details"
  participantRewardsDetails(fundraiserId: String!, userId: Int!): [ParticipantRewardsData]
  participantSSOId(fundraiserId: Int!, userId: Int!): ParticipantSSOID
  participantShareMessage(fundraiserId: String!, msgType: String!): String!
  participantSmsDonationInvites(where: ParticipantSmsDonationInvitesWhereInput): [ParticipantSmsDonationInvite!]
  participantTextTemplate(fundraiserId: Int!, userId: Int!): ParticipantTextTemplate
  participantTopDonation(fundraiserId: String!, participantId: String!): ParticipantTopDonation
  "List of participants"
  participants(cursor: String, limit: Int,
    "fundraiser_user Id"
    participantId: Int
  ): ParticipantList
  participantsListV2(fundraiserId: Int!): [FundraiserParticipants]
  participantsPublic(fundraiserId: Int!): [FundraiserParticipantPublic]
  paymentsApiCustomerByEmail(input: PaymentsApiCustomerInput!): PaymentsApiCustomerResponse!
  paymentsApiGetAccountByEntity(input: PaymentsApiEntityInput!): PaymentsApiAccountByEntityResponse!
  payouts(campaignId: Int!): [Payout]!
  permissions: [Permission]
  "List of pre-made contact templates"
  premadeContactTemplatesGet: [PremadeContactTemplate]
  preparation(input: GetPreparationByIdInput): Preparation!
  preparationReportSheets(input: CommonSheetFiltersInput): [PreparationSheet]
  preparations: [Preparation]
  "This resolver is only for testing purposes at this time"
  primaryGroupLeader(campaignId: String!): PrimaryGroupLeader
  "Identifier uses fundraiser join_code or slug"
  publicFundraiserData(fundraiserId: Int, identifier: String): PublicFundraiserData
  "Get user public data from User Directory by userID"
  publicUserData(userId: Int!): User
  "A query to get last year's fundraiser by the id of this year's fundraiser"
  raiseAdminFundraiserPast(id: Int!): RaiseAdminFundraiserPast
  registrationRoster(input: RegistrationRosterInput!): RegistrationRoster!
  role(id: String!): Role
  roles(id: String): [Role]
  "Get one Roster with it's members by its Roster id."
  roster(rosterId: ID!): OrgsRoster
  "Get all Rosters and their members that are associated with a specific Org."
  rosters(orgId: ID!, product: RosterCreator): [OrgsRoster!]!
  scheduleEvent(id: ID!): ScheduleEvent
  scheduleEvents(filter: EventFilter, orgsId: String!, programId: String): [ScheduleEvent]
  schoolInfo(input: GetSchoolInfoByIdInput): SchoolInfo!
  schoolInfos: [SchoolInfo]
  "Search campaigns"
  searchDriveCampaigns(
    "Search by activity types. e.g. 'baseball', 'music', 'football', etc."
    activityTypes: [String!],campaignName: String,
    "Status of campaign"
    campaignStatuses: [CampaignStatus!],
    "Search campaigns that has end date equal to"
    endDate: DateTime,fundraiserId: Int,
    "Search campaigns that has incentive only"
    hasIncentive: Boolean,
    "Search campaigns that settlement is pending. Use by internal tools"
    isPendingSettlement: Boolean,limit: Int = 25,
    "Search campaigns that has total member in group less or equal than this value"
    maxTeamSize: Int,
    "Search campaigns that has current total donation amount smaller than this value"
    maxTotalRaisedCents: Int,
    "Search campaigns that has total member in group larger or equal than this value"
    minTeamSize: Int,
    "Search campaigns that has current total donation amount larger than this value"
    minTotalRaisedCents: Int,offset: Int = 0,
    "Search by Organization"
    organizationIds: [Int!],
    "Search by organization type. e.g. 'Organization', 'School', 'District', 'Club', etc."
    organizationTypes: [String!],
    "Search campaigns by salesrep"
    salesrepIds: [Int!],sortDirection: SortDirection, sortField: SortField,
    "Search campaigns that has start date equal to"
    startDate: DateTime,
    "Not in used"
    territories: [String!],
    "Search by short-code US states. e.g. 'TX', 'CA', etc."
    usStates: [String!]
  ): DriveCampaignList
  "Search Raise's Organization. @Deprecated: should be replaced by Orgs API"
  searchDriveOrganizationsByName(limit: Int, offset: Int, organizationName: String): DriveOrganizationList
  season(input: SeasonByIdInput): Season!
  seasonByYearAndTeam(input: SeasonByYearTeamInput): [Season]
  seasonEventWizard(input: SeasonEventWizardInput): [Event]
  seasons: [Season]
  settlementMethodChangeEligibility(fundraiserId: String!): SettlementMethodChangeEligibilityResponse
  shipments(externalId: ShipmentInput): ShipmentResponse!
  spendAccountExport(status: String!): ExportFile
  spendAdminDebitCards(customerId: String!): SpendAdminDebitCardsResponse
  spendAdminGetArchivedGroups(orgId: String!): SpendGroupResponse
  spendAdminGroupRosterSearch(input: String!): SpendAdminGroupRosterSearchResponse
  spendAdminInvoices(groupRosterId: String!): SpendAdminInvoicesResponse
  spendAdminReorderDebitCard(input: SpendReorderCardInput): SpendDebitCardID
  spendAdminStaffSearch(input: String!): SpendAdminInviteResponse
  spendAdminTransactionByInvoiceId(invoiceId: String!): SpendAdminBankTransactionsResponse
  spendBudget(groupId: String, id: String!): SpendBudgetResponse
  spendBudgetExport: ExportFile
  spendBudgetTransactions(input: SpendBudgetTransactionInput): SpendBudgetTransactionsOutput!
  spendBudgets(groupId: String): SpendBudgetsResponse
  spendBudgetsProgramSummary: SpendBudgetSummaryResponse
  spendBudgetsSummary(groupId: String!, seasonId: String!): SpendBudgetSummaryResponse
  spendBudgetsSummaryV2(groupId: String!, seasonId: String!): SpendBudgetSummaryV2Response
  spendCategories(filterBy: CategoryFilterEnum, filterValue: String, groupId: String, seasonId: String): SpendCategoryResponse
  spendDataForHome(udid: String!): HmSpendData!
  spendDebitCardInvites(groupId: String): SpendInviteResponse
  spendGetApprovalDetails(id: String): SpendApprovalDetailResponse
  spendGetApprovalsByTab(input: SpendGetApprovalInput): SpendApprovalResponse
  spendGetCounterpartyById(counterpartyId: String!): SpendCounterparty
  spendGetDisputes(targetId: String): SpendGetDisputesResponse
  spendGetGroupCounterparties(groupOrOrgId: String!): SpendCounterpartiesResponse
  spendGetInstitutionByRoutingNumber(routingNumber: String!): SpendInstitutionResponse
  spendGetOrganizationCounterparties(groupOrOrgId: String!): SpendCounterpartiesResponse
  spendGetPayeeById(id: String!): SpendPayees
  spendGroup: SpendGroup
  spendGroupBankAccounts(groupId: String): SpendBankAccountsResponse
  spendGroupBankLinkTokenCreate(id: String): SpendBankLinkCreateResponse
  spendGroupById(id: String!): SpendGroup
  spendGroupDebitCards(id: String): SpendDebitCardResponse
  spendGroupPayees(groupId: String!): SpendPayeeResponse
  spendGroupRosters(filterBy: GroupRosterFilterEnum, filterValue: String): SpendGroupRosterResponse
  spendGroupRostersBySeason(pagination: SpendPaginationInput, seasonId: String!): SpendGroupRosterResponse
  spendGroupRostersBySeasonV2(archived: Boolean, pagination: SpendPaginationInput, seasonId: String!, sort: SpendSortInput): SpendGroupRosterResponse
  spendGroupRostersExport(filters: SpendRostersFilter!): ExportFile
  spendGroups(includeArchive: Boolean): SpendGroupResponse
  "List groups with pagination/filtering and sorting. Valid sort.field: name, seasonStartDate, seasonEndDate, playerCount, paid, upcoming, pastDue, dueToday, processing, paymentScheduleStatus"
  spendGroupsFiltered(pagination: SpendPaginationInput, sort: SpendSortInput, where: SpendGroupsWhereInput): SpendGroupsResponse
  spendGroupsOverviewDashboard(pagination: SpendPaginationInput): GroupsOverviewDashboardResponse
  spendGroupsPublic(orgId: String!): PublicGroupResponse
  spendGuardianGroupRosters: SpendGroupRosterResponse
  spendGuardianHighlight: SpendGuardianHighlight
  spendGuardianInvoices(filters: [SpendGuardianInvoiceFilter]): SpendGuardianInvoiceResponse
  spendGuardianInvoicesV2: SpendInvoiceResponse
  spendInvite(id: String!): SpendInviteResponse
  spendInvites(filterBy: InviteFilterEnum, filterValue: String): SpendInviteResponse
  spendInvitesFiltered(pagination: SpendPaginationInput, sort: SpendSortInput, where: SpendInviteWhereInput): SpendInviteResponse
  spendInvoiceById(id: String!): SpendInvoice
  spendInvoiceHistory(input: SpendInvoiceHistoryInput): SendEmailResponse
  spendInvoices(filterBy: InvoiceFilterEnum, filterValue: String, limit: Int, parentSignUp: Boolean): SpendInvoiceResponse
  spendInvoicesExport(filters: [SpendInvoiceFilter], limit: Int): ExportFile
  spendInvoicesExportV2(filters: [SpendInvoiceFilter], sort: SpendSortInput): ExportFile
  spendInvoicesFiltered(filters: [SpendInvoiceFilter], limit: Int): SpendInvoiceResponse
  spendOrganization: SpendOrganization
  spendOrganizationAccountLimits(groupId: String): SpendAccountLimitsResponse
  spendOrganizationAccounts: SpendAccountResponse
  spendOrganizationAllActiveCustomerCards: SpendDebitCardResponsePlus
  spendOrganizationBankAccounts: SpendBankAccountsResponse
  spendOrganizationBankLinkTokenCreate: SpendBankLinkCreateResponse
  spendOrganizationById(id: String!): SpendOrganization
  spendOrganizationCheckImage(input: SpendCheckImageRequest!): SpendImageResponse
  spendOrganizationDebitCards: SpendDebitCardResponse
  spendOrganizationPayees(orgId: String!): SpendPayeeResponse
  spendOrganizationPieChart: spendOrganizationPieChartResponse
  spendOrganizations(pagination: SpendPaginationInput, where: SpendOrganizationsWhereInput): SpendOrganizationsResponse
  spendPaginatedInvoices(filters: [SpendInvoiceFilter], pagination: SpendPaginationInput, sort: SpendSortInput): SpendInvoiceResponse
  spendPastDueInvoices(interval: SpendPastDueInterval, pagination: SpendPaginationInput, sort: SpendPastDueSort): SpendPastDueInvoicesOutput
  spendPaymentScheduleById(id: String!): SpendPaymentSchedule
  spendPaymentSchedules(groupIdOrSeasonId: String): [SpendPaymentSchedule]
  spendRoleCurrent(sessionId: String!): SpendRole
  spendRoles: SpendRoleResponse
  spendRosters(filterBy: RosterFilterEnum, filterValue: String): SpendRosterResponse
  spendRostersFiltered(pagination: SpendPaginationInput, sort: SpendSortInput, where: SpendGroupRostersWhereInput): SpendRosterResponse
  spendSeason(id: String!): SpendSeason
  spendSettings: SpendSettings
  spendSystemNotifications: SpendSystemNotificationsResponse
  spendTransaction(id: String): SpendBankTransactionResponse
  spendTransactionAttachment(id: String!): TransactionAttachmentsResponse
  spendTransactionAttachmentRaw(input: SpendRawFileInput!): SpendFileResponse
  spendTransactionAttachmentsByPaymentId(id: String!): TransactionAttachmentsResponse
  spendTransactionNotes(id: String): TransactionNotesResponse
  spendTransactionNotesByPaymentId(id: String): TransactionNotesResponse
  spendTransactions(filterBy: TransactionFilterEnum, filterValue: String, groupId: String, limit: Int): SpendBankTransactionsResponse @deprecated(reason: "Use spendTransactionsFiltered")
  spendTransactionsExport(allAccounts: Boolean, filters: [SpendTransactionFilter], limit: Int): ExportFile
  spendTransactionsFiltered(allAccounts: Boolean, dateRange: dateRangeEnum, disableCache: Boolean, filters: [SpendTransactionFilter], pagination: SpendPaginationInput, sort: SpendSortInput, where: SpendTransactionWhereInput): SpendBankTransactionsResponse
  spendTransactionsLegacy(groupId: String, pagination: SpendPaginationInput): SpendLegacyTransactionsResponse
  spendUserAccountBearerToken(input: SpendAccountBearerToken): SpendAccountBearerTokenResponse
  spendUserBankAccounts: SpendBankAccountsResponse
  spendUserBankAccountsList: SpendBankAccountsListResponse
  spendUserBankLinkTokenCreate(id: String): SpendBankLinkCreateResponse
  spendUserExportEmails(input: SpendExportEmailsInput!): ExportFile
  spendUserNotificationSetting: SpendUserNotificationSettingResponse
  spendUserVerificationToken: SpendVerificationTokenResponse
  sponsorDataForHome(orgId: String!): HmSponsorData!
  storeAllBrands: StoreBrandsUnionType
  storeAllColors: StoreColorsUnionType
  storeBestSeller(scopeId: Int!): StoreBaseSellerUnionType
  storeByScopeId(scopeId: Int!): MagentoStoreUnionType
  storeCampaignPayableInfo(input: StoreCampaignPayableInfoInput): StoreCampaignPayableInfoUnionType
  storeDataForHome(fundraiserId: [Int!], uEmail: String!): HmStore!
  storeEarnedPointsGraph(input: StorePointEarnedGraphInput): MagentoStorePointsEarnedUnionType
  storeGLCampaigns(input: StoreUserCampaignsInput): StoreUserCampaignUnionType
  storeGLParticipants(input: StoreUserParticipantsInput): StoreUserParticipantUnionType
  storeOrder(netsuiteId: String): StoreOrderResult
  storeOrderFilter(input: OrderFilterInput): OrdersFilterUnionType
  storeOrderItemImages(itemIds: [Int!]!): StoreOrderItemsUnionType
  storeOrderSalesGraph(input: OrderGraphInput): storeOrderSalesGraphUnionType
  storeOrderSummary(scopeId: Int!): OrdersSummaryUnionType
  storeOrdersCsv(scopeId: Int!): MagentoOrderUnionType
  storePayableFilter(input: StoreBaseFilterInput): StorePayableFilterUnionType
  storePointActivityFilter(input: PointsActivityFilterInput): PointActivityFilterUnionType @deprecated(reason: "Use storePointActivityFilterAdvance Query")
  storePointActivityFilterAdvance(input: PointsActivityFilterInput): PointActivityFilterUnionType
  storePointsActivityCsv(scopeId: Int!): MagentoPointActivityUnionType @deprecated(reason: "Use storePointsActivityCsvAdvance Query")
  storePointsActivityCsvAdvance(scopeId: Int!): MagentoPointActivityUnionType
  storePointsWithdrawalRequestFilter(input: StoreBaseFilterInput): StorePointsWithdrawalFilterUnionType
  storeProfitPercentage(storeId: Int!): ProfitPercentage
  storeScopePayableInfo(scopeId: Int!): StorePayableInfoUnionType
  storeSearch(pagination: StoreSearchPaginationInput, where: StoreSearchWhereInput): StoreSearchResponse!
  storeSubscribedUser: StoreSubscribedUserUnionType
  storeTickets(scopeId: Int!): StoreTicketUnionType
  storeUserDetails(email: String!): StoreUserInfoUnionType
  storeUserPreference(userId: String!): UserPreference
  storesByCampaignIds(input: StoreByCampaignIdsInput): StoreFilterUnionType
  storesByGLEmail(input: StoreByGLEmailInput): StoreFilterUnionType
  storesSummaryByCampaignIds(input: StoreSummaryByCampaignIdsInput): StoresSummaryUnionType
  storesSummaryByGLEmail(input: StoresSummaryByGLEmailInput): StoresSummaryUnionType
  team(input: TeamByCodeInput): Team!
  teamOfficials(input: OfficialByIdInput): [TemplateOfficial]
  teamOrgSchedules(input: CommonSchedulesFiltersInput): [CommonSchedules]
  teamPreparations(input: PreparationByIdInput): [TemplatePreparation]
  teamWorkers(input: WorkerByIdInput): [TemplateWorker]
  teams: [Team]
  "An example field added by the generator"
  testField: String!
  transactionDetail(transactionId: UUID!): TransactionDetail
  transactions(input: TransactionsInput!): [Transaction]!
  transactionsByAccount(input: AccountTransactionsInput!): [Transaction]!
  transactionsByAccountPaginated(input: AccountTransactionsPaginatedInput!): AccountTransactionsPaginatedOutput!
  transactionsBySpendDestination(input: TransactionsInput!): [Transaction]!
  transactionsBySpendSource(input: TransactionsInput!): [Transaction]!
  ""
  twispCardBalances(index: twispCardBalances_indexes, limit: Int32 = "1000", scope: String, sort: Sort, where: Where): [twispCardBalances]
  ""
  twispCards(index: twispCards_indexes, limit: Int32 = "1000", scope: String, sort: Sort, where: Where): [twispCards]
  ""
  twispStripeHooks(index: twispStripeHooks_indexes, limit: Int32 = "1000", scope: String, sort: Sort, where: Where): [twispStripeHooks]
  ""
  twispTransactions(index: twispTransactions_indexes, limit: Int32 = "1000", scope: String, sort: Sort, where: Where): [twispTransactions]
  unconfirmedEvents(input: UnconfirmedEventsFiltersInput): UnconfirmedEventsList
  updateSavedSalesRep(snapRepEmail: String!, udId: String!): UserSavedRep!
  user(email: String, id: String): User
  "Get all Programs/Activities that a User leads, grouped by parent Org."
  userActivityLeadership(userId: ID!): [ProgramLeadership!]!
  userAffiliations(userId: ID!): [OrgAffiliation!]!
  userApiKey(id: String!): ApiKey
  userApiKeyBySecret(secret: String!): ApiKey
  userApiKeys: [ApiKey]
  "Get all Org ids that are associated with a single User and a specific product, as well all the Campaign ids in each hierarchy."
  userAssociationCampaigns(product: Product!, userId: ID!): [OrgCampaignIds!]!
  "Get all Orgs that are associated with a User and a specific product."
  userAssociations(id: ID!, product: Product!): [Org]!
  userBySecret(secret: String!): UserWithPermissions
  userChallenge(challengeId: String!): UserChallenge
  "Get all Financial Account data associated with a single User Directory ID."
  userFinancialAccounts(userId: ID!): [FinancialAcctUser!]!
  userFitting: UserFitting
  "Get a list of Campaigns grouped by Org that User has been a Group Leader for. If you do not pass in a User ID, we will use the logged in User's ID."
  userGroupLeadership(userId: ID = null): [OrgCampaignAffiliation!]!
  "Get a list of implied affiliations based on GroupLeader relationships."
  userImpliedAffiliations(userId: ID!): [OrgCampaignAffiliation!]!
  "Get all Insights configurations for current user"
  userInsightsConfigurations: [UserInsightsConfig]!
  userInvites(ids: [String!]!): InvitesResponse
  "Get all titles that are associated with a User and a specific Org."
  userOrgAffiliation(orgId: ID!, userId: ID!): [UserAffiliation!]!
  "Get all Org relationships that are associated with a User."
  userOrgRelationships(userId: ID = null): UserOrgRelationships!
  userParentsInfo(id: [ID!]!): [UserParentsInfoResult!]!
  userParentsPublicInfo(id: [ID!]!): [UserParentsPublicInfoResult!]!
  "Get a list Campaigns grouped by Org that User has been a Participant in."
  userParticipation(userId: ID!): [OrgCampaignAffiliation!]!
  userPermissions(userId: String): UserPermissionsList
  userPublic(id: String): UserPublic @deprecated(reason: "use userPublicInfo instead")
  userPublicInfo(id: [ID]): [UserPublicInfoResult]
  "Get all Teams that a User leads."
  userTeamLeadership(userId: ID!): [TeamLeadership!]!
  userToken(expirationTime: String, permissions: [String!]): Auth!
  "Get counts of unconfirmed LEADS, AFFILIATIONS, and IMPLIED affiliations for a User."
  userUnconfirmedAffiliations(userId: ID!): UserAffiliationCounts!
  users(filter: UsersFilter, isInternal: Boolean = false, limit: Int! = 25, offset: Int, searchTerm: String, snapRaiseId: Int, snapSpendId: Int): Users
  vaultAccountPayoutFrequencySetAuditEntries(orgId: ID): [VaultAccountPayoutFrequencySetAuditEntry!]
  vaultAccountStatus(accountId: ID, accountRepresentativeGatewayId: String, applicationId: ID, beneficialOwnersGatewayIds: [String!], provider: VaultFinancialProvider!, stripeEnv: VaultStripeEnv = RAISE): VaultAccountStatus!
  vaultCard(id: String!): Card!
  vaultCreateKybKycSubmitAuditEntries(orgId: ID): [VaultCreateKybKycSubmitAuditEntry!]
  vaultKyb(id: ID!): VaultKyb!
  vaultKyc(id: ID!): VaultKyc!
  vaultOrgConfirmationSubmitAuditEntries(orgId: ID): [VaultOrgConfirmationSubmitAuditEntry!]
  vaultOwners(orgId: ID!): [VaultOwner!]
  vaultStripeConnectAccountBankAccountListFetch(stripeConnectAccountId: String!, stripeEnv: VaultStripeEnv = RAISE): [VaultBankAccount]
  vaultStripeConnectAccountFetch(stripeConnectAccountId: ID!, stripeEnv: VaultStripeEnv = RAISE): JSONObject
  vaultStripeConnectAccountPaymentMethodDefaultSetAuditEntries(orgId: ID): [VaultStripeConnectAccountPaymentMethodDefaultSetAuditEntry!]
  vaultStripeConnectAccountsBankAccountListFetch(stripeConnectAccountIds: [String!], stripeEnv: VaultStripeEnv = RAISE): JSONObject
  vaultStripeInvalidRepresentative(stripeConnectAccountId: ID!, stripeEnv: VaultStripeEnv = RAISE): ID
  vaultUnitApplicationTagsFetch(unitApplicationId: ID!): JSONObject
  vaultVgsValue(value: String!): String!
  vehicle(input: GetVehicleByIdInput): Vehicle!
  vehicles: [Vehicle]
  workerPoolById(input: GetWorkerPoolByIdInput): WorkerPool!
  workerPools: [WorkerPool]
}
type RaiseAdminFundraiser {
  "End date of upcoming fundraiser"
  endDate: DateTime
  "Raise Resource Entity ID of the fundraiser"
  entityId: Int
  "Int of participants from previous year"
  groupSize: Int
  "Fundraiser ID"
  id: Int
  "Fundraiser Image URL"
  image: String
  "Fundraiser name"
  name: String
  "A query for raise admin users to get upcoming resign participants"
  participantListForFundraiser(filter: RaiseAdminParticipantFilter): RaiseAdminParticipantList
  "Has the fundraiser been rolled over?"
  rollover: Boolean
  "Start date of upcoming fundraiser"
  startDate: DateTime
}
type RaiseAdminFundraiserPast {
  "Current year's fundraiser"
  current: RaiseAdminFundraiser
  "Last year's fundraiser"
  past: RaiseAdminFundraiser
}
type RaiseAdminParticipant {
  "Participant email"
  email: String
  "Participant first name"
  firstName: String
  "Participant ID"
  id: Int
  "Participant last name"
  lastName: String
  "Participant's Profile Image"
  profileImage: String
}
type RaiseAdminParticipantList {
  "Total number of fundraisers"
  count: Int!
  "List of fundraisers"
  list: [RaiseAdminParticipant]
}
type RaiseUser {
  email: String
  "SSO User ID"
  id: String
}
type ReceiptResult {
  error: String
  success: Boolean!
  transactionId: ID!
}
type Redirect {
  count: Int!
  createdAt: DateTime!
  fundraiser: DonorFundraiserData
  fundraiserId: String
  id: String!
  path: String!
  updatedAt: DateTime!
}
type Registration {
  activity: RegistrationActivity
  comment: String
  height: Int
  heightFormatted: String
  id: ID!
  paid: Boolean
  participant: RegistrationPerson
  participationStatus: String
  registeredBy: RegistrationPerson
  season: Boolean
  shortSize: String
  state: Int
  tryout: Boolean
  tshirtSize: String
  weight: Int
}
type RegistrationActivity {
  athleticSeason: String
  currentSeason: String
  fee: Int
  id: ID!
  kind: String
  name: String
  organization: RegistrationOrganization
  registerable: Boolean
}
type RegistrationGroup {
  gender: String
  id: ID!
  level: String
  name: String
  rosters: [RegistrationRoster!]!
  rosterwebserviceAccess: Boolean
  sportName: String
  state: Int
}
type RegistrationGroupRegistration {
  finalizeDate: ISO8601DateTime
  group: RegistrationGroup
  id: ID!
  jerseyNumber: String
  position: String
  registration: Registration
  roster: RegistrationRoster
  state: String
}
type RegistrationOrganization {
  id: ID!
  name: String!
  orgsId: String!
  schoolSlug: String!
}
type RegistrationPerson {
  accountType: String
  children: [RegistrationPerson!]!
  firstName: String
  id: ID!
  parents: [RegistrationPerson!]!
  udid: ID
}
type RegistrationRoster {
  final: Boolean
  group: RegistrationGroup
  groupRegistrations: [RegistrationGroupRegistration!]!
  id: ID!
  season: String
  teamRegistrations: [RegistrationGroupRegistration!]!
}
type RelationshipTitles {
  createdAt: DateTime!
  isAdmin: Boolean
  isConfirmed: Boolean
  title: String
  updatedAt: DateTime
}
type RemovedPendingInvitesResponse {
  removedInvites: [InviteFundraiserSentResponse]
}
type ResendReceiptsResult {
  results: [ReceiptResult!]!
}
type RevisionFundraiserApprovalSubmissionData {
  isSsoApprover: Boolean!
  submitterName: String!
}
type Role {
  description: String
  id: String
  name: String
  permissions: [RolePermission]
  scope: String
  title: String
}
type RolePermission {
  id: String
  permission: Permission
  permissionId: String
  roleId: String
}
type Roster {
  id: ID
  members: [RosterMember]
  name: String
}
type RosterMember {
  association: CampaignMemberAssociation
  id: ID
  userId: String
  wasDeleted: Boolean
}
type RosterUser {
  id: ID!
  internal: Boolean!
  isAdmin: Boolean!
  isDependent: Boolean!
  isGuardian: Boolean!
  type: [String!]!
}
type S3PresignedInsightsApprovalsUrlResult {
  expires: DateTime!
  filename: String!
  s3Filename: String!
  status: String!
  url: String!
}
type SDPagination {
  limit: Int
  offset: Int
  total: BigInt
}
type ScheduleEvent {
  data: JSON
  endDatetime: DateTime
  eventResults: [EventResult]
  eventType: EventType!
  id: ID!
  isArchived: Boolean!
  location: String
  orgsId: String!
  programId: String
  startDatetime: DateTime!
}
type ScheduleEventCreateResult {
  errors: [String]
  event: ScheduleEvent
}
type ScheduleEventUpdateResult {
  errors: [String]
  event: ScheduleEvent
}
"Information of a emails scheduled for cron jobs"
type ScheduledReleaseInfo {
  "The date and time for release"
  date: DateTime
  "The timezone for scheduled release"
  timezone: String
}
type School {
  ad: String
  adEmail: String
  adExt: String
  adHomeAddress: String
  adHomeCity: String
  adHomePhone: String
  adHomeState: String
  adHomeZip: String
  adPhone: String
  address: String
  assistantAd: String
  assistantEmail: String
  assistantPhone: String
  boc: Boolean
  city: String
  code: String
  conferenceId: Int
  createdAt: ISO8601DateTime
  createdBy: Int
  directory: Boolean
  displayName: String
  division: String
  fax: String
  id: ID!
  logoUrl: String
  map: String
  name: String
  phone: String
  principal: String
  schoolId: Boolean
  secretary: String
  secretaryExt: String
  secretaryPhone: String
  showName: String
  showOnWebsite: Boolean
  state: String
  status: Boolean
  superintendent: String
  trackScores: Boolean
  updatedAt: ISO8601DateTime
  updatedBy: Int
  url: String
  zipcode: String
}
type SchoolInfo {
  Schoolname: String
  ad: String
  ad_contract_signee: String
  address: String
  ccemail: String
  city: String
  conf_text_type: String
  email: String
  email_reminder: String
  email_reminder_officials: String
  email_reminder_workers: String
  enable_cc_email_as_origin: String
  enable_ml_updates: String
  fax: String
  icon: String
  id: String!
  mascot: String
  message_board_read_at: Date
  ml_key: String
  phone: String
  principal: String
  school_timezone: String
  secondary_ad_email: String
  secondary_ad_name: String
  signed_contract_notification: String
  ssn_on_file: String
  state: String
  state_org: String
  state_org_abbreviation: String
  use_security: String
  web_password: String
  zip: String
}
type ScopeProductColor {
  label: String
  value: Int
  visual: String
}
type Season {
  budget: Float
  created_at: Date
  default_time_for_event: String
  default_time_for_practice: String
  home_field: String
  is_deleted: Boolean
  preview: String
  season: String
  season_id: Int!
  sport: Team
  sport_code: String
  web_password: String
  year: String
}
type SendEmailResponse {
  success: Boolean!
}
"Settlement details for closed campaign."
type SettlementDetails {
  "The amount in cents that campaigns was advanced in Wallet."
  advanceAmount: Int
  "Address to send out check to"
  checkingAddress: String
  "The type of snap fee being collected. e.g. 'standard', '1+1', '20%/15%', etc."
  feeType: String
  "Total cost of incentives in cents"
  gearCost: Int
  "Code to build logo"
  incLogo: String
  "Primary color to build logo"
  incLogoPrimaryColor: String
  "Script to build logo"
  incLogoScript: String
  "Secondary color to build logo"
  incLogoSecondaryColor: String
  "Filename of Logo of Group of Campaign"
  logoImage: String
  "The amount in cents that campaigns was manual-advanced in Raise."
  manualAdvanceAmount: Int
  "Net raised amount in cents. Equals totalRaised minus snapFee and adds the otkBonus. It is the amount client received in their check/deposit."
  netRaisedTotal: Int
  "Notes of campaign"
  notes: String
  "Legal Name of Organization"
  organizationLegalName: String
  "TIN of Organization"
  organizationTin: String
  "Total bonus amount that team will receive through otk purchase cost (usually 10%) in cents."
  otkBonus: Int
  "Link to OTK Logo"
  otkLogoUrl: String
  "Percentage of participation of campaign"
  participationPercent: Int
  "Notes for settlement for this campaign"
  settlementNotes: String
  "Default Shipping address to send out gears/merchandise/check"
  shippingAddress: String
  "Total Fee in cents that Raise collects from campaigns"
  snapFee: Int
}
type SettlementMethodChangeEligibilityResponse {
  id: String!
  settlementMethodChangeEligibility: Boolean!
  settlementMethodChangeEligibilityFailedReasons: [String!]
}
"Attributes for settlement status"
type SettlementStatusUpdate {
  fundraiserId: Int
  settlementStatus: SettlementStatus
}
type Shipment {
  carrier: ShipmentCarrier
  customer: String!
  destination: String
  externalId: ShipmentExternalId!
  netsuiteId: String!
  netsuiteSoTranId: String
  netsuiteTranId: String
  shippedDate: Timestamp
  source: ShipmentSource
  status: ShipmentStatus
  trackingNumbers: [String]!
  updated: Timestamp!
  workflowStage: String!
}
type ShipmentCampaignId {
  campaignId: Int!
}
type ShipmentOtherId {
  externalId: UUID!
}
type ShipmentResponse {
  shipments: [Shipment]!
  status: ResponseStatus!
}
type SignUpResponseWithAvailableTransport {
  availableTransport: [TransportEnum!]!
  result: [TransportType!]!
}
type SignupAgreement {
  content: String
  createdAt: String
  name: String
}
type SmsInviteData {
  count: Int
  isReleased: Boolean
  smsInviteReleasedAt: DateTime
}
type SnapMobileOneAccess {
  activatedProducts: [SnapMobileOneProduct!]!
  level: Int!
  orgId: ID!
  requestedLevel: SnapMobileOneAccessLevelRequest!
  requestedProducts: [SnapMobileOneProduct!]!
  userOnboardedProducts: [SnapMobileOneProduct!]!
}
type SnapMobileOneAccessLevelRequest {
  level: Int
  requestedAt: DateTime
  requestedBy: String
}
type SpendACHCreditResponse {
  approvalId: String
  paymentId: String!
}
type SpendAccount {
  account: String @deprecated(reason: "removed later")
  available: Int
  balance: Int
  groupId: String
  groupStatus: String
  id: String
  name: String
  routing: String @deprecated(reason: "removed later")
  status: String
  type: String
}
type SpendAccountBearerTokenResponse {
  expiresIn: Int
  token: String
}
type SpendAccountID {
  approvalId: String
  id: String!
  reason: String
  status: String!
}
type SpendAccountLimitsResponse {
  ach: SpendAchLimitsData
  bookTransfer: SpendBookTransferLimitsData
  card: SpendCardLimitsData
  checkDeposit: SpendCheckDepositLimitsData
  checkPayment: SpendCheckPaymentLimitData
  wire: SpendWireLimitsData
}
type SpendAccountResponse {
  accounts: [SpendAccount]
  count: Int
  groupsBalance: SpendGroupAccountBalances
  totalBalance: Int
}
type SpendAchLimits {
  dailyCredit: Int
  dailyDebit: Int
  monthlyCredit: Int
  monthlyDebit: Int
}
type SpendAchLimitsData {
  limits: SpendAchLimits!
}
type SpendAchPaymentID {
  id: String
}
type SpendAchPaymentResponse {
  amount: Int!
  descriptor: String!
  id: String!
  status: String!
  transactionIdList: [String]
}
type SpendAddress {
  city: String!
  state: String!
  street: String!
  street2: String
  zip: String!
}
type SpendAdminBankTransactionsResponse {
  transactions: [SpendBankTransactionDetail!]!
}
type SpendAdminDebitCard {
  address: SpendAddress!
  groupName: String
  id: String!
  lastFour: String!
  status: String!
  username: String!
}
type SpendAdminDebitCardsResponse {
  cards: [SpendAdminDebitCard!]!
  orgName: String!
}
type SpendAdminGroupRosterSearchResponse {
  groupRosters: [AdminGroupRosterType!]!
}
type SpendAdminInvite {
  email: String!
  id: String!
  name: String!
  role: String!
  status: String!
}
type SpendAdminInviteResponse {
  invites: [SpendAdminInvite!]!
}
type SpendAdminInvoicesResponse {
  invoices: [AdminInvoices!]!
}
type SpendAgreementID {
  id: String
}
type SpendAllInviteIds {
  count: Int!
  inviteIds: [String!]
}
type SpendApprovalDetailResponse {
  approval: ApprovalDetailType
  id: String!
}
type SpendApprovalResponse {
  approvalCount: Int!
  approvals: [ApprovalType!]
  groups: [String!]
  staff: [String!]
}
type SpendApprovalUpdateResponse {
  id: String
}
type SpendArchiveSeasonMembersResponse {
  success: Boolean!
}
type SpendAuthorizeAutoPayResponse {
  invoiceIds: [String!]
}
type SpendBankAccessCreateResponse {
  error: String
  id: String
}
type SpendBankAccessDeleteResponse {
  success: Boolean
}
type SpendBankAccount {
  accountId: String @deprecated(reason: "Moved to details")
  availableBalance: Float @deprecated(reason: "Moved to details")
  counterpartyId: String
  currentBalance: Float @deprecated(reason: "Moved to details")
  details: SpendBankAccountDetails
  entityId: String!
  id: String
  institution: SpendInstitution @deprecated(reason: "Moved to details")
  mask: String @deprecated(reason: "Moved to details")
  name: String @deprecated(reason: "Moved to details")
  officialName: String @deprecated(reason: "Moved to details")
  status: String
  subtype: String @deprecated(reason: "Moved to details")
  type: String @deprecated(reason: "Moved to details")
}
type SpendBankAccountDetails {
  accountId: String
  availableBalance: Float
  currentBalance: Float
  institution: SpendInstitution
  mask: String
  name: String
  officialName: String
  subtype: String
  type: String
}
type SpendBankAccountsListResponse {
  externalAccounts: [SpendBankAccount!]
}
type SpendBankAccountsResponse {
  externalAccounts: [SpendBankAccount]
  status: String
}
type SpendBankLinkCreateResponse {
  expiration: String
  linkToken: String
  requestId: String
}
type SpendBankTransaction {
  amount: Int
  attachments: [TransactionAttachment!]
  canCancel: Boolean
  correlationId: String
  created: String
  creditMemo: SpendMemoTransaction
  description: String @deprecated(reason: "Can be found in metadata")
  destination: String
  direction: String
  effective: String
  external: SpendJoinedExternal
  externalId: String
  groupRoster: SpendGroupRoster
  hasAttachments: Boolean
  history: [SpendBankTransactionHistory]
  id: String
  isReconciled: Boolean
  metadata: SpendBankTransactionMetaData
  payee: SpendJoinedPayee
  processor: String
  reconciliation: SpendReconciledTransaction
  snapAmount: Int
  source: String
  status: String
  totalApplied: Int
  totalReconciled: Int
  transactionNote: String
  transactionStatus: String
  transactionType: String
  type: String
  typeReconciled: [String]
}
type SpendBankTransactionDetail {
  amount: Int
  canCancel: Boolean
  correlationId: String
  description: String @deprecated(reason: "Can be found in metadata")
  destination: String
  direction: String
  disputeDetail: SpendDisputeDetail
  effective: String
  externalId: String
  groupRoster: SpendGroupRoster
  history: [SpendBankTransactionHistory]
  id: String
  metadata: SpendBankTransactionMetaData
  paymentStatus: String
  processor: String
  reconciliation: SpendReconciledTransaction
  snapAmount: Int
  source: String
  status: String
  transactionType: String
  type: String
}
type SpendBankTransactionHistory {
  date: String
  status: String
  transactionId: String
}
type SpendBankTransactionMetaData {
  account: String
  addenda: String
  balance: String
  checkNumber: String
  customer: String
  description: String
  destination: String
  externalId: String
  invoiceId: String @deprecated(reason: "Can be found in tags as SpendInvoiceIds")
  originalTransaction: String
  payment: String
  processor: String
  product: String @deprecated(reason: "Can be found in tags")
  returnedTransaction: String
  snapAmount: Int
  source: String
  status: String
  summary: String
  tags: SpendTransactionTags
}
type SpendBankTransactionResponse {
  transactionDetail: SpendBankTransactionDetail
}
type SpendBankTransactionsResponse {
  count: Int
  transactions: [SpendBankTransaction]
}
type SpendBookTransferLimitsData {
  limits: SpendAchLimits!
}
type SpendBudget {
  category: SpendCategory!
  createdAt: String!
  description: String!
  id: String!
  invoices: [SpendInvoice!]
  isDefault: Boolean
  reconciledBudgetTotal: Int
  reconciledInvoicesTotal: Int
  reconciledTotal: Int
  reconciledTransactions: [SpendReconciledBudgetTransaction!]
  season: SpendSeason
  targetAmount: Int!
  targetDateAt: String!
  updatedAt: String
  vaultId: String
}
type SpendBudgetID {
  id: String
}
type SpendBudgetReconciledTotal {
  id: String!
  reconciledAmount: Int!
  seasonId: String
  targetAmount: Int!
}
type SpendBudgetResponse {
  budget: SpendBudget
}
type SpendBudgetSummaryResponse {
  summaryByCategory: [SpendCategory]
  summaryUnreconciled: SpendUnreconciledSummary
}
type SpendBudgetSummaryV2Response {
  reconciledTotals: [SpendReconciledTotals]
  summaryByCategory: [SpendCategory]
  summaryUnreconciled: SpendUnreconciledSummary
}
type SpendBudgetTransactionsOutput {
  budgetTransactions: [BudgetTransaction]!
}
type SpendBudgetsResponse {
  budgets: [SpendBudget]
  count: Int
}
type SpendCancelCheckID {
  id: String!
}
type SpendCardLimits {
  dailyCardTransaction: Int!
  dailyDeposit: Int!
  dailyPurchase: Int!
  dailyWithdrawal: Int!
}
type SpendCardLimitsData {
  limits: SpendCardLimits!
}
type SpendCategory {
  budgets: [SpendBudget]
  createdAt: String!
  id: String
  isDefault: Boolean!
  isHidden: Boolean!
  name: String!
  organizationId: String!
  type: String!
  updatedAt: String
}
type SpendCategoryID {
  id: String
}
type SpendCategoryResponse {
  categories: [SpendCategory]
  count: Int
}
type SpendCheckDepositLimits {
  daily: Int!
  monthly: Int!
}
type SpendCheckDepositLimitsData {
  limits: SpendCheckDepositLimits!
}
type SpendCheckDepositTagsResponse {
  success: Boolean!
}
type SpendCheckImageAccess {
  checkId: String!
  checkType: SpendCheckImageType!
  id: String!
}
type SpendCheckImageAllowanceResponse {
  createdAt: String
  id: String
  success: Boolean
}
type SpendCheckPaymentLimit {
  dailySent: Int!
  monthlySent: Int!
}
type SpendCheckPaymentLimitData {
  limits: SpendCheckPaymentLimit
}
type SpendCheckSendResponse {
  approvalId: String
  id: String
  status: String
  type: String
}
type SpendCounterpartiesResponse {
  counterparties: [SpendCounterparty!]!
}
type SpendCounterparty {
  accountLastFour: String!
  bankName: String!
  einNumber: String
  id: String!
  name: String!
  routingNumber: String!
}
type SpendCounterpartyCreateResponse {
  id: String!
}
type SpendCreditMemoCreateResponse {
  ids: [String!]!
}
type SpendCreditMemoUpdateResponse {
  id: String!
}
type SpendDeauthorizeResponse {
  id: String
  notifSuccess: Boolean
}
type SpendDebitCard {
  expiration: String
  id: String
  lastFour: String
  status: String
  userId: String
}
type SpendDebitCardActionResponse {
  cardId: String!
}
type SpendDebitCardID {
  approvalId: String
  id: String
}
type SpendDebitCardPlus {
  expiration: String
  groupOrOrgName: String
  id: String
  lastFour: String
  status: String
  userId: String
  userName: String
}
type SpendDebitCardResponse {
  cards: [SpendDebitCard]
  count: Int
}
type SpendDebitCardResponsePlus {
  cards: [SpendDebitCardPlus]
  count: Int
}
type SpendDebitCardShippingType {
  city: String!
  postalCode: String!
  state: String!
  street: String!
  street2: String
}
type SpendDeleteCounterpartyResponse {
  success: Boolean!
}
type SpendDismissInviteResponse {
  inviteId: String!
}
type SpendDisputeDetail {
  description: String
  id: String
  originalMetadata: SpendTransactionTags
  status: String
}
type SpendDisputes {
  accountName: String!
  amount: Int!
  createdAt: String!
  dueBy: String
  hasEvidence: Boolean!
  id: String!
  reason: String!
  status: String!
}
type SpendExternalTransferFee {
  base: Float!
  description: String!
  id: String!
  maxAverageBalance: Float
  minAverageBalance: Float
  name: String
  percentage: Float!
}
type SpendExternalTransferResponse {
  amount: Int
  approvalId: String
  descriptor: String
  id: String
  status: String
}
type SpendFileResponse {
  content: String
  fileName: String
}
type SpendGetDisputesResponse {
  disputes: [SpendDisputes!]!
}
type SpendGroup {
  accountId: String
  archivedAt: String
  createdAt: String
  discountAmount: Int
  discountCutOffDate: String
  enableDiscount: Boolean
  hasAccount: Boolean
  id: String
  isArchived: Boolean
  isRequireAgreement: Boolean
  latestSeason: SpendSeason
  legacyAccountId: String
  minimumDiscountPurchase: Int
  name: String
  organizationFees: SpendOrganizationFee
  organizationId: String
  programId: String
  seasons: [SpendSeason]
  seasonsList: [SpendSeason]
  sharedAccount: Boolean
  unitAmount: Int
}
type SpendGroupAccountBalances {
  active: Int
  archived: Int
}
type SpendGroupArchiveResponse {
  archiveResult: SpendGroupArchiveResult
  id: String
}
type SpendGroupArchiveResult {
  group: Boolean!
  invites: Boolean
  invoiceAndPms: Boolean
  rosters: Boolean
}
type SpendGroupID {
  id: String
}
type SpendGroupResponse {
  count: Int
  groups: [SpendGroup]
}
type SpendGroupRoster {
  archivedAt: String
  createdAt: String
  group: SpendGroup
  groupId: String
  guardianName: String
  id: String
  invite: SpendInvite
  inviteId: String
  invoices: [SpendInvoice]
  isArchived: Boolean
  joinedAt: String
  pastDueDays: Int
  paymentScheduleStatus: String
  phoneNumber: String
  roster: SpendRoster
  rosterId: String
  season: SpendSeason
  seasonId: String
  settings: SpendGroupRosterSettings
  status: SpendGroupRosterStatusEnum
  total: SpendTransactionTotals
  userId: String
}
type SpendGroupRosterID {
  id: String
}
type SpendGroupRosterResponse {
  count: Int
  groupRosters: [SpendGroupRoster]
}
type SpendGroupRosterSettings {
  isAutoPayAuthorized: Boolean
}
type SpendGroupRosterSettingsResponse {
  groupRosterId: String
  settingsId: String
}
type SpendGroupsOverview {
  balance: Int
  cards: [SpendDebitCard]
  collected: Int
  groupId: String
  name: String
  pastDue: Int
  paymentScheduleInvoices: [SpendPaymentSchedule]
  seasonEndAt: String
  seasonName: String
  seasonStartAt: String
  sharedAccount: Boolean
}
type SpendGroupsResponse {
  count: Int
  groups: [SpendGroup]
  hasNext: Boolean
}
type SpendGuardianActiveGroup {
  dueDate: String @deprecated(reason: "No longer required")
  group: SpendGuardianGroup
  roster: [SpendRoster] @deprecated(reason: "No longer required")
}
type SpendGuardianComingSoonInvoice {
  authorizedAt: String
  creditMemos: [SpendMemo!]
  groupId: String
  groupName: String
  groupRosterId: String
  invoiceAmountDue: String
  invoiceAutoPayAuthorized: Boolean
  invoiceAutoPayStopped: Boolean
  invoiceDescription: String
  invoiceDueDate: String
  invoiceId: String
  invoiceNote: String
  invoiceOptedOutAt: String
  invoicePaymentMethod: String
  invoiceStatus: String
  isAuthorized: Boolean
  isOptional: Boolean
  optedIn: Boolean
  rosterName: String
  seasonId: String
  seasonName: String
}
type SpendGuardianCounts {
  activeGroups: Int
  comingSoonInvoices: Int
  unauthorizedInvoices: Int
}
type SpendGuardianGroup {
  accountId: String
  archivedAt: String
  createdAt: String
  hasAccount: Boolean
  id: String
  isArchived: Boolean
  latestSeason: SpendSeason
  name: String
  organizationId: String
  programId: String
  seasons: [SpendGuardianSeason]
}
type SpendGuardianHighlight {
  activeGroups: [SpendGuardianActiveGroup]
  comingSoonInvoices: [SpendGuardianComingSoonInvoice]
  counts: SpendGuardianCounts
  unauthorizedInvoices: [SpendGuardianComingSoonInvoice]
}
type SpendGuardianInvoice {
  amount: Int!
  authorizedAt: String
  balanceDue: Int!
  creditMemos: [SpendMemo!]
  description: String!
  discountAmount: Int!
  dueDate: String!
  groupId: String!
  groupName: String!
  groupRosterId: String!
  id: String!
  isArchived: Boolean!
  isAutoPayAuthorized: Boolean!
  isAutoPayStopped: Boolean!
  isOptional: Boolean!
  isRefunded: Boolean!
  note: String
  optedIn: Boolean!
  optedOutAt: String
  paid: Boolean!
  paidDate: String
  paymentMethodId: String
  paymentMethodSource: String
  refundDate: String
  rosterId: String!
  rosterName: String!
  seasonId: String!
  seasonName: String!
  status: String!
}
type SpendGuardianInvoiceResponse {
  count: Int!
  invoices: [SpendGuardianInvoice!]
}
type SpendGuardianRecentTransaction {
  amountDue: String
  dueDate: String
  groupName: String
  invoiceDescription: String
  paymentMethod: String
  paymentStatus: String
  paymentType: String
  rosterName: String
  transactionId: String
}
type SpendGuardianSeason {
  endDateAt: String
  id: String
  isBudgetShared: Boolean
  name: String
  roster: [SpendRoster]
  startDateAt: String
}
type SpendImageResponse {
  imageContent: String
  imageFound: Boolean
  imageId: String
  imageName: String
}
type SpendInstitution {
  id: String
  name: String
}
type SpendInstitutionResponse {
  address: String!
  isACHSupported: Boolean!
  isWireSupported: Boolean!
  name: String!
  routingNumber: String!
}
type SpendInvite {
  createdAt: String
  debitCard: String
  debitCards: [SpendDebitCard!]
  email: String
  expiresAt: String
  firstName: String
  group: SpendGroup
  groupId: String
  id: String
  isApprover: Boolean
  isArchived: Boolean
  isDeliverable: Boolean
  isDismissed: Boolean
  isUser: Boolean
  lastName: String
  orgName: String
  organizationId: String
  seasonId: String
  status: String
  type: String
  user: User
  userId: String
}
type SpendInviteID {
  id: String
}
type SpendInviteResponse {
  count: Int
  invites: [SpendInvite]
}
type SpendInvoice {
  amount: Int
  authorizedAt: String
  balanceDue: Int
  budgetItem: SpendBudget
  budgetItemId: String
  createdAt: String
  creditMemos: [SpendMemo!]
  description: String
  discountAmount: Int
  dueDate: String
  feesAmount: Int
  groupRoster: SpendGroupRoster
  groupRosterId: String
  id: String
  isArchived: Boolean
  isAutoPayAuthorized: Boolean
  isAutoPayStopped: Boolean
  isOptional: Boolean
  isPending: Boolean
  isReconciled: Boolean
  isRefunded: Boolean
  lastNotifyDate: String
  lastNotifyId: String
  note: String
  notificationAttempts: Int
  optedIn: Boolean
  optedOutAt: String
  paid: Boolean
  paidDate: String
  paymentMethodId: String
  paymentMethodSource: String
  paymentScheduleInvoiceId: String
  paymentScheduleStatus: String
  pendingMessage: SpendInvoicePendingMessage
  reconciledTransactions: [SpendReconciledInvoiceTransaction]
  refundDate: String
  status: String
  transaction: SpendTransaction
  transactionRejectReason: String
  transactionSettledDate: String
  transactionStatus: String
  updatedAt: String
}
type SpendInvoiceID {
  id: String
}
type SpendInvoiceIDs {
  ids: [String]
}
type SpendInvoicePendingMessage {
  nextStep: String
  status: String!
}
type SpendInvoiceRefundResponse {
  invoiceId: String
  spendTransactionId: String
}
type SpendInvoiceResponse {
  count: Int
  hasNext: Boolean
  invoices: [SpendInvoice]
}
type SpendInvoiceUpdateResponse {
  success: Boolean!
}
type SpendJoinedExternal {
  externalId: String!
  groupName: String!
  invoiceDescription: String!
  playerName: String!
  seasonName: String!
}
type SpendJoinedPayee {
  groupName: String
  organizationName: String
  payeeId: String!
  payeeName: String!
  referenceId: String!
}
type SpendLegacyTransaction {
  amount: Int
  attachments: [TransactionAttachment!]
  correlationId: String
  created: Timestamp
  description: String
  destination: String
  direction: String
  effective: Timestamp!
  externalId: String!
  id: UUID!
  metadata: JSON
  note: TransactionNotes
  processor: String
  reconciliation: SpendReconciledTransaction
  snapAmount: Int
  source: String
  status: String!
  transactionNote: String
  type: String
}
type SpendLegacyTransactionsResponse {
  count: Int
  transactions: [SpendLegacyTransaction]
}
type SpendMember {
  child: SpendMemberChild
  email: String
  firstName: String
  id: String
  lastName: String
  phoneNumber: String
  signedUp: Boolean
}
type SpendMemberChild {
  id: String
  name: String
}
type SpendMemo {
  creditAmount: Int!
  creditApplied: Int!
  dateToApply: String!
  groupName: String
  id: String!
  isArchived: Boolean!
  note: String
  title: String!
}
type SpendMemoInvoice {
  amount: Int!
  balanceDue: Int!
  description: String!
  discountAmount: Int!
  dueDate: String!
  groupId: String!
  groupName: String!
  id: String!
  participantName: String!
  rosterId: String!
  seasonId: String!
  seasonName: String!
  status: String!
  userId: String!
}
type SpendMemoTransaction {
  creditAmount: Int!
  creditApplied: Int!
  dateToApply: String!
  groupName: String
  id: String!
  invoice: SpendMemoInvoice
  isArchived: Boolean!
  note: String
  title: String!
}
type SpendNotificationID {
  id: String
}
type SpendNotificationStatus {
  status: String
}
type SpendOrgAdminUpdateResponse {
  createdAuthorizedUser: Boolean
  id: String
}
type SpendOrganization {
  accountId: String
  achBaseFee: Int
  achPercent: Float
  budgets: [SpendBudget]
  cardBaseFee: Int
  cardPercent: Float
  checkImageAccess: [SpendCheckImageAccess!]
  city: String!
  customerDetail: SpendOrganizationCustomerDetail
  debitCards: SpendOrganizationDebitCardCount
  email: String!
  externalId: String
  externalTransferFee: SpendExternalTransferFee
  externalTransferFeeId: String
  externalTransferOutEnabled: Boolean
  groupBanksEnabled: Boolean
  groups: [SpendGroup]
  hasLinkedAccount: Boolean
  id: String
  isApproverEnabled: Boolean
  isLite: Boolean!
  isVerified: Boolean
  legacyAccountId: String
  legacyExternalId: String
  legalName: String!
  logo: String
  nickname: String
  orgId: String
  owner: User
  phone: String!
  spendBaseFee: Int
  spendPercent: Float
  state: String!
  street: String!
  street2: String
  userId: String
  website: String
  zip: String!
}
type SpendOrganizationCustomerDetail {
  activeAccounts: Int
  averageDaily: Int
  days: Int
}
type SpendOrganizationDebitCardCount {
  activated: Int
  assigned: Int
  shipped: Int
  total: Int
}
type SpendOrganizationFee {
  achBaseFee: Int
  achPercent: Float
  cardBaseFee: Int
  cardPercent: Float
  spendBaseFee: Int
  spendPercent: Float
}
type SpendOrganizationID {
  id: String
}
type SpendOrganizationOwner {
  firstName: String
  lastName: String
}
type SpendOrganizationPayoutResponse {
  id: String
  message: String
  success: Boolean!
}
type SpendOrganizationRecurringPayoutResponse {
  id: String
  message: String
  success: Boolean!
}
type SpendOrganizationStatus {
  status: Boolean
}
type SpendOrganizationsResponse {
  count: Int
  hasNext: Boolean
  organizations: [SpendOrganization!]
}
type SpendPastDueInvoice {
  amount: Int!
  creditMemos: [creditMemoAmounts]
  description: String!
  discountAmount: Int!
  dueDate: String!
  email: String!
  groupId: String!
  groupName: String!
  guardianStatus: String!
  id: String!
  name: String!
  rosterId: String!
  seasonId: String!
  seasonName: String!
  status: String!
}
type SpendPastDueInvoicesOutput {
  count: Int!
  invoices: [SpendPastDueInvoice]!
}
type SpendPayNowResponse {
  paymentId: String
  status: String
}
type SpendPayeeCreateResponse {
  payeeId: String
}
type SpendPayeeResponse {
  payees: [SpendPayees!]
}
type SpendPayees {
  address1: String!
  address2: String
  city: String!
  einNumber: String
  id: String!
  name: String!
  state: String!
  zipCode: String!
}
type SpendPaymentMethodDetachResponse {
  paymentMethodId: String
  updatedInvoiceIdList: [String!]
}
type SpendPaymentMethodResponse {
  groupRosterSetting: SpendGroupRosterSettingsResponse
  payment: SpendPayNowResponse
  updatedInvoiceIds: [String]!
}
type SpendPaymentSchedule {
  amountDue: Int
  budgetItem: SpendBudget
  budgetItemId: String
  description: String
  dueDate: String
  group: SpendGroup
  groupId: String
  id: String
  isArchived: Boolean
  isOptional: Boolean
  lastPublishedState: String
  note: String
  season: SpendSeason
  seasonId: String
  status: String
}
type SpendPaymentScheduleID {
  id: String
}
type SpendPendingInvite {
  expiresAt: String!
  groupId: String
  groupName: String
  id: String!
  isDismissed: Boolean!
  organizationId: String!
  organizationName: String!
  rosterName: String
  seasonId: String
  seasonName: String
  status: String
  type: String!
}
type SpendReconcileTransactionID {
  id: String
}
type SpendReconciledBudget {
  amount: Int!
  budgetId: String!
  budgetName: String!
  categoryId: String!
  categoryName: String!
  description: String!
  groupId: String
  groupName: String!
  invoiceId: String
  playerName: String!
  seasonId: String!
  seasonName: String!
  type: String!
}
type SpendReconciledBudgetTransaction {
  amount: Int
  budgetItemId: String
  id: String
  invoiceId: String
  reconciledTransaction: SpendReconciledTransaction
  transactionId: String
}
type SpendReconciledInvoiceTransaction {
  amount: Int
  creditMemos: [SpendMemo]
  id: String
  invoiceId: String
  note: String
  reconciledTransaction: SpendReconciledTransaction
  rosterId: String
  rosterName: String
  transactionId: String
}
type SpendReconciledTotals {
  budgets: [SpendBudgetReconciledTotal!]
  id: String!
  name: String!
  orgId: String!
  type: String!
}
type SpendReconciledTransaction {
  amount: Int
  budgetTransactions: [SpendReconciledBudgetTransaction]
  createdAt: String
  id: String
  invoiceTransactions: [SpendReconciledInvoiceTransaction]
  legacyTransaction: SpendLegacyTransaction
  paymentId: String
  reconciledTo: [SpendReconciledBudget]
  transaction: SpendBankTransaction
  type: String
  updatedAt: String
}
type SpendRemoveSeasonMemberResponse {
  success: Boolean!
}
type SpendRole {
  groupId: String
  groupName: String
  id: String!
  isApprover: Boolean
  isArchived: Boolean
  isNotSignedUp: Boolean
  name: String!
  organizationId: String
  organizationName: String
  permissions: [String]!
  seasonId: String
}
type SpendRoleID {
  id: String
}
type SpendRoleResponse {
  count: Int
  roles: [SpendRole]
}
type SpendRoster {
  email: String
  groupRosters: [SpendGroupRoster]
  id: String
  name: String
  rosterEmail: String
  total: SpendTransactionTotals
}
type SpendRosterResponse {
  count: Int
  rosters: [SpendRoster]
}
type SpendRosterUpdate {
  id: String
  invitesUpdated: [String!]
}
type SpendSeason {
  budgets: [SpendBudget]
  endDateAt: String
  groupId: String
  groupRoster: [SpendGroupRoster]
  id: String
  isActive: Boolean
  isBudgetShared: Boolean
  isLinkEnabled: Boolean
  name: String
  paymentScheduleInvoices: [SpendPaymentSchedule]
  paymentScheduleStatus: String
  playerCount: Int
  startDateAt: String
  transactionTotals: SpendTransactionTotals
}
type SpendSeasonID {
  id: String
}
type SpendSession {
  applicationId: String
  expiresAt: String
  id: String
  inviteId: String
  isDismissed: Boolean
  message: String
  newInvite: Boolean
  pendingInvites: [SpendPendingInvite!]
  role: SpendRole
  status: String
  systemNotifications: [SpendSystemNotification!]
  url: String
  userId: String
}
type SpendSessionID {
  id: String
}
type SpendSettings {
  approversRequired: Int
  debitCardApproval: Boolean
  enableGroupBanks: Boolean @deprecated(reason: "duplicate of SpendOrganization.groupBanksEnabled")
  enableProgramAgreements: Boolean
  externalTransferApproval: Boolean
  externalTransferLimit: Int
  fileUploadEnabled: Boolean
  internalTransferApproval: Boolean
  internalTransferLimit: Int
  notifyBankActivityAdmins: Boolean
  notifyDueFrequencies: [String!]
  notifyFailedAchAdmins: Boolean
  notifyPastDueNonUsers: Boolean
  notifyUpcomingNonUsers: Boolean
  pastDueFrequency: Int @deprecated(reason: "replaced by notifyDueFrequencies")
  requirePaymentMethod: Boolean
  sendAchApproval: Boolean
  sendAchLimit: Int
  sendCheckApproval: Boolean
  sendCheckLimit: Int
  signUpAgreement: SignupAgreement
  signUpLink: String
}
type SpendSignUpResponse {
  id: String
}
type SpendSignupFormResponse {
  id: String
  status: String
  type: String
  url: String
}
type SpendStopCheckPaymentResponse {
  id: String!
}
type SpendSystemNotification {
  createdAt: String
  description: String
  groupStaff: Boolean
  guardian: Boolean
  id: String
  organizationId: String
  organizationName: String
  programAdmin: Boolean
  programStaff: Boolean
  status: SystemNotificationStatus
  title: String
}
type SpendSystemNotificationIdResponse {
  id: String
}
type SpendSystemNotificationsResponse {
  systemNotifications: [SpendSystemNotification!]
}
type SpendTransaction {
  externalId: String
  id: String
  invoiceId: String
  source: String
}
type SpendTransactionAttachmentID {
  id: String
}
type SpendTransactionNoteID {
  id: String
}
type SpendTransactionResponse {
  count: Int
  transactions: [SpendTransaction]
}
type SpendTransactionTags {
  achPaymentAmount: String
  achPaymentId: String
  amount: String
  attachmentKey: String
  disputeId: String
  disputeTransferId: String
  fee: String
  spendAutoPay: Boolean
  spendDestinationId: String
  spendDisputeId: String
  spendDisputedChargeId: String
  spendExternalId: String
  spendGroupRosterId: String
  spendInvoiceIds: String @deprecated(reason: "No longer used due to being too long")
  spendOriginalPaymentId: String
  spendPaymentType: String
  spendSourceId: String
  spendUserId: String
  stopPaymentIssued: String
  submittedBy: String
}
type SpendTransactionTotals {
  credited: Int
  dueToday: Int
  paid: Int
  pastDue: Int
  pastDueDays: Int
  processing: Int
  statuses: [String]
  upcoming: Int
}
type SpendTransactionsIdList {
  transactionIdList: [String]
}
type SpendUnitApplicationResponse {
  url: String
}
type SpendUnreconciledSummary {
  credits: TransactionTotalCount
  debits: TransactionTotalCount
}
type SpendUpdateResponse {
  success: Boolean
}
type SpendUserAccountUpdateResponse {
  invitesUpdatedCount: Int
  rostersUpdatedCount: Int
  unitAccountUpdated: Boolean
}
type SpendUserAcknowledgeNotificationResponse {
  success: Boolean!
}
type SpendUserEmailUpdateResponse {
  invitesUpdatedCount: Int
  rostersUpdatedCount: Int
}
type SpendUserID {
  id: String
}
type SpendUserLoginAuditResponse {
  id: String!
}
type SpendUserNotificationSetting {
  copyPastDueInvoices: Boolean!
  groupId: String
  id: String!
  notifyOnBankActivity: Boolean!
  notifyOnFailedCardPayments: Boolean!
  notifyOnInvoicePayment: Boolean!
  organizationId: String
  roleName: String
  userRoleId: String!
}
type SpendUserNotificationSettingID {
  id: String
}
type SpendUserNotificationSettingResponse {
  notificationSetting: SpendUserNotificationSetting
}
type SpendUserRoleID {
  id: String
}
type SpendVaultApplicationCreateResponse {
  id: String
}
type SpendVerificationTokenResponse {
  verificationToken: String
}
type SpendWireLimits {
  dailyTransfer: Int!
  dailyTransferSoft: Int!
  monthlyTransfer: Int!
  monthlyTransferSoft: Int!
}
type SpendWireLimitsData {
  limits: SpendWireLimits
}
type Sport {
  createdAt: ISO8601DateTime
  createdBy: Int
  hasSelectWinner: Boolean
  hasTies: Boolean
  id: ID!
  lowScoreWin: Boolean
  name: String
  updatedAt: ISO8601DateTime
  updatedBy: Int
}
type Standing {
  division: String
  name: String
  played: String
  record: String
  winPercentage: String
}
type Store {
  code: String
  domain: String
  enabled: Boolean
  faviconUrl: String
  headerLogoUrl: String
  id: ID!
  name: String
}
type StoreBestSeller {
  productId: Int
  productImage: String
  productName: String
  productPrice: Float
  productQuantity: Int
  productType: String
}
type StoreBrands {
  brands: [Brand]
}
type StoreBuildRequest {
  email: String
  referenceTicket: Int
  storeName: String
  storeRequest: Boolean
  userSsoId: String
}
type StoreCampaign {
  accountManagerId: Int
  commissionEligibleSalesrepId: Int
  endDate: String
  entityId: Int
  fullAddress: String
  gool: Int
  groupLeaderEmail: String
  id: Int
  initialGoalCents: Int
  joinCode: Int
  name: String
  originalSalesrepId: Int
  salesrepId: Int
  settlementConfirmationStatus: String
  settlementMethod: String
  settlementStatus: String
  slug: String
  startDate: String
  status: String
  teamSize: Int
  totalRaisedCents: Int
  userDirectoryId: String
}
type StoreColors {
  colors: [ScopeProductColor]
}
type StoreEditInfo {
  code: String
  scopeId: Int
}
type StoreOrderItemImage {
  imageUrl: String
  itemId: BigInt
  selectedLogo: String
}
type StoreOrderItems {
  items: [StoreOrderItemImage]
}
type StoreOrderResult {
  createdAt: DateTime!
  id: String!
  netsuiteId: String
  shippingReceivedAt: DateTime
  status: StoreOrderStatus
  trackingNumber: String
  trackingUrl: String
  updatedAt: DateTime!
}
type StoreOrderSalesGraphs {
  graphs: [storeOrderSalesGraph]
}
type StoreParticipant {
  email: String
  firstName: String
  lastName: String
  phoneNumber: String
}
type StorePayableFilter {
  pagination: SDPagination
  payables: [PayableInfo]
}
type StorePaymentIntent {
  clientSecret: String
  email: String
  userSsoId: String
}
type StorePointsWithdrawalFilter {
  pagination: SDPagination
  withdrawalRequests: [StorePointsWithdrawalRequest]
}
type StorePointsWithdrawalRequest {
  amount: Int
  createdAt: String
  expactedWithdrawalDate: String
  payableInfo: PayableInfo
  pointsType: String
  requesterInfo: StorePointsWithdrawalRequester
  scopeInfo: StoresWithdrawalPointsScope
  ticketId: Int
  updatedAt: String
}
type StorePointsWithdrawalRequester {
  email: String
  name: String
  userSsoId: String
}
type StoreProfitPercentageUpdated {
  updated_at: String
  updated_product_price_percentage: String
}
type StoreReceiverData {
  email: String
  points: Float
  receiverId: BigInt
}
type StoreResult {
  errors: [String]
  message: String
  status: StoreStatus!
  storeUrl: String
}
type StoreScopeBestSeller {
  bestSellers: [StoreBestSeller]
}
type StoreSearchResponse {
  limit: Int!
  offset: Int!
  stores: [StoreType!]!
  total: Int!
}
type StoreSenderData {
  email: String
  points: Float
  senderId: BigInt
}
type StoreSubscribedUser {
  isSubscribed: Boolean
}
type StoreTicket {
  createdAt: String
  id: String
  message: String
  scopeId: Int
  subject: String
  ticketId: String
  updatedAt: String
}
type StoreTickets {
  tickets: [StoreTicket]
}
type StoreTransaction {
  amount: Float
  email: String
  id: String
  points: Float
  scopeId: Int
  status: String
  transactionId: String
  userSsoId: String
}
type StoreTransferReturn {
  points: Float
  receiver: StoreReceiverData
  sender: StoreSenderData
  status: String
}
type StoreType {
  city: String
  fundraiserId: Int
  id: String!
  name: String!
  organizationId: Int
  organizationLegalName: String
  organizationName: String
  schoolId: String
  schoolName: String
  state: String
  storeCode: String
  storeUrl: String!
  teamId: String
  zip: String
}
type StoreUser {
  email: String
  firstName: String
  lastName: String
  points: Float
  userId: BigInt
}
type StoreUserCampaigns {
  campaigns: [StoreCampaign]
  pagination: SDPagination
}
type StoreUserParticipants {
  pagination: SDPagination
  participants: [StoreParticipant]
}
type StoresFilter {
  pagination: SDPagination
  stores: [MagentoStore]
}
type StoresSummary {
  baseSales: Float
  discountAmount: Float
  orders: BigInt
  points: Float
  sales: Float
  stores: BigInt
}
type StoresWithdrawalPointsScope {
  managerEmail: String
  managerId: Int
  scopeCode: String
  scopeId: Int
  teamName: String
}
type StripeAccount {
  accountId: String!
  accountType: AccountType
  campaignId: String!
}
"metadata passed to Stripe API"
type StripeMetadata {
  activityType: String
  entity: String
  fundraiserId: String
  fundraiserId_legacy: String
  paymentType: String
  transactionId: String
}
type Summary {
  date: ISO8601DateTime
  id: ID
  record: String
  schoolId: String
  schoolName: String
  team1: School
  team1Name: String
  team1Score: String
  team2: School
  team2Name: String
  team2Score: String
}
"See CampaignMembership for details"
type SupporterCampaign {
  id: ID!
}
type Team {
  facility: String
  gender: String
  groupval: String
  home_field: String
  is_deleted: Boolean
  level1: String
  sport_code: String!
  sport_description: String
  sport_name: String!
}
type TeamLeadership {
  org: Org!
  teams: [TeamWithTitle!]!
}
type TeamWithTitle {
  team: Org!
  titles: [Leader!]!
}
type TemplateOfficial {
  id: Int!
  pay: Float
  pay_code: String
  sport: String
  worker_duty: String
}
type TemplatePreparation {
  id: Int!
  prep: Int
  preparation_id: String
  preparation_name: String
  qty: String
  sport: String
}
type TemplateWorker {
  home_field: String
  id: Int!
  pay: Float
  pay_code: String
  sport: String
  worker_duty: String
  worker_name: String
}
type TopDonation {
  donorName: String
  subtotalCents: Float
}
type Total {
  salary: String
  woker_name: String
}
type TotalDonationsRaised {
  numberOfDonations: Int
  subtotalCents: Float
}
type TrackingResult {
  status: OrderStatus
}
type Transaction {
  amount: Decimal
  correlationId: String
  created: Timestamp
  description: String
  destination: String
  direction: String
  effective: Timestamp!
  externalId: String!
  id: UUID!
  metadata: JSON
  processor: String
  snapAmount: Decimal
  source: String
  status: String!
  type: String
}
type TransactionAttachment {
  createdAt: String
  description: String
  id: String
  isLegacy: Boolean
  lastUpdatedByUserId: String
  name: String
  paymentId: String
  updatedAt: String
  url: String
}
type TransactionAttachmentsResponse {
  attachments: [TransactionAttachment]
}
type TransactionDetail {
  amount: Decimal
  correlationId: String
  description: String!
  destination: String
  direction: String
  effective: Timestamp!
  externalId: String!
  history: [TransactionHistoryEvent]!
  id: UUID!
  metadata: JSON
  processor: String
  snapAmount: Decimal
  source: String
  status: Status!
  type: String
}
type TransactionHistoryEvent {
  date: Timestamp!
  status: String!
  transactionId: UUID!
}
type TransactionNotes {
  content: String
  createdAt: String
  id: String
  lastUpdatedByUserId: String
  paymentId: String
  updatedAt: String
}
type TransactionNotesResponse {
  notes: [TransactionNotes]
}
type TransactionTotalCount {
  count: Int
  total: Int
}
type TransformedDailyCalendarBusSchedule {
  calendar: [String]
  events: [DailyCalendarBusScheduleEvents]
  exportdata: [DailyCalendarBusScheduleExportData]
}
type TransformedDailyCalendarEventReturn {
  activity: String
  activityLevel: String
  activityType: String
  author: String
  bus_fee: Int
  bus_time: String
  comments: String
  conference: String
  conference_event_id: Int
  conference_id: Int
  confirmed: String
  confirmedStatusBoolean: Boolean
  contract: String
  departure_location: String
  directions: String
  duty: String
  early_dismissal_required: String
  early_dismissal_time: String
  email: String
  end_time: String
  estimated_return_time: String
  event: String
  eventOfficial: String
  eventOfficialCell: String
  eventOfficialEmail: String
  eventTiming: String
  event_date: String
  event_id: Int
  exists_in_mls: Int
  fee: Int
  formatedEventDate: String
  formatedEventDateSystem: String
  formatedEventDay: String
  g_s: String
  gate_revenue: Int
  groupval: String
  headline: String
  impact_event: String
  isDuplicate: String
  lead: String
  location: String
  loss_points: Int
  noofgames: String
  num_buses: Int
  num_buses_text: String
  opponent: String
  opponent_code: String
  opponent_score: String
  pay_code: String
  picture: String
  place: String
  prep_setup: String
  promote: String
  results: String
  revenue: Int
  rollover: String
  rolloverStatusBoolean: Boolean
  salary: Int
  seasonInfo: String
  seasonSportCode: String
  season_team: Int
  serialnumber: String
  sportCode: String
  sportDescription: String
  sportGender: String
  sportLevel: String
  sportName: String
  start_time: String
  team_score: String
  title: String
  tournament: String
  trans_id: Int
  transportDetails: String
  transport_comments: String
  transportation: String
  vehicle_count: String
  vehiclesTransportDetails: [DailyCalendarEventTransportDetails]
  web_dir: String
  weekdayname: String
  win_points: Int
  years: String
}
type TransformedDailyCalendarOfficials {
  exportdata: [DailyCalendarOfficialExport]
  message: [DailyCalendarOfficialMessage]
}
type TransformedDailyCalendarPreparation {
  exportdata: [DailyCalendarPreparationExportData]
  message: [DailyCalendarPreparationMessage]
}
type TransformedDailyCalendarWorkers {
  exportdata: [DailyCalendarWorkerExportData]
  message: [DailyCalendarWorkerMessage]
}
type TransformedEventReturn {
  activity: String
  activityLevel: String
  activityType: String
  author: String
  bus_fee: Int
  bus_time: String
  comments: String
  conference: String
  conference_event_id: Int
  conference_id: Int
  confirmed: String
  confirmedStatusBoolean: Boolean
  contract: String
  departure_location: String
  directions: String
  duty: String
  early_dismissal_required: String
  early_dismissal_time: String
  email: String
  end_time: String
  estimated_return_time: String
  event: String
  eventOfficial: String
  eventOfficialCell: String
  eventOfficialEmail: String
  eventTiming: String
  event_date: String
  event_id: Int
  exists_in_mls: Int
  fee: Int
  formatedEventDate: String
  formatedEventDateSystem: String
  formatedEventDay: String
  g_s: String
  gate_revenue: Int
  groupval: String
  headline: String
  impact_event: String
  isDuplicate: String
  lead: String
  location: String
  loss_points: Int
  noofgames: String
  num_buses: Int
  num_buses_text: String
  opponent: String
  opponent_code: String
  opponent_score: String
  pay_code: String
  picture: String
  place: String
  prep_setup: String
  promote: String
  results: String
  revenue: Int
  rollover: String
  rolloverStatusBoolean: Boolean
  salary: Int
  seasonInfo: String
  seasonSportCode: String
  season_team: Int
  serialnumber: String
  sportCode: String
  sportDescription: String
  sportGender: String
  sportLevel: String
  sportName: String
  start_time: String
  team_score: String
  title: String
  tournament: String
  trans_id: Int
  transportDetails: String
  transport_comments: String
  transportation: String
  vehicle_count: String
  web_dir: String
  weekdayname: String
  win_points: Int
  years: String
}
type TransformedOfficialAssignment {
  duty: String
  eventActivity: String
  eventDate: String
  eventHomeAway: String
  eventLocation: String
  eventOpponent: String
  eventTime: String
  event_id: Int
  id: Int
  name: String
  organization: String
  paid: String
  pay_code: String
  salary: Int
  seasonSportCode: String
  sportDescription: String
  ssn: String
}
type TransportType {
  recipient: String
  transport: TransportEnum
}
type UnconfirmedEvents {
  activity: String
  bus_count: Int
  comments: String
  confirmed: String
  driver_name: String
  driver_phone: String
  end_time: String
  event_date: String
  facility: String
  g_s: String
  gender: String
  home_field: String
  id: Int
  levels: String
  opponent: String
  place: String
  season_id: Int
  sports_code: String
  sports_description: String
  sports_group: String
  sports_name: String
  start_time: String
  vehicle_id: String
  vehicle_type: String
  year: String
}
type UnconfirmedEventsList {
  items: [UnconfirmedEvents]
  limit: Int
  offset: Int
  totalFilteredItems: Int
  totalPages: Int
  totalRows: Int
}
type UnprocessableSubmission implements Error {
  message: String!
}
type UnreadCount {
  totalUnreadCount: Int!
}
type UnreadCountError implements Error {
  message: String!
}
type UpdatedFundraiserStoreUrl {
  id: Int
}
type UpdatedParticipantGroupCount {
  count: Int
}
type UpsertEventParticipantsCount {
  count: Int
}
type UpsertEventPreparationsCount {
  count: Int
}
type UpsertEventTransportDetailsCount {
  count: Int
}
type User {
  apps: [String]
  email: String
  firstName: String
  id: String
  isConfirmed: Boolean
  isDisabled: Boolean
  kyc: VaultKyc
  language: String
  lastLoginAt: Date
  lastName: String
  occupation: UserOccupation
  phoneNumber: String
  profilePicture: String
  snapRaiseId: String @deprecated(reason: "will be removed in the next release")
  snapSpendId: String @deprecated(reason: "will be removed in the next release")
}
type UserAffiliation {
  createdAt: DateTime!
  description: String
  isAdmin: Boolean
  isConfirmed: Boolean
  title: AffiliationTitle!
  updatedAt: DateTime
}
type UserAffiliationCounts {
  affiliatedCount: Int!
  impliedCount: Int!
  leadsCount: Int!
  total: Int!
}
type UserChallenge {
  completedAt: String
  id: String!
  name: String!
  params: JSONObject
  skippedAt: String
  status: UserChallengeStatus!
}
type UserChannelMembership {
  channelId: ID!
  role: ChannelRolesEnum!
}
type UserFitting {
  fitting: String
  id: ID!
}
type UserInsightsConfig {
  createdAt: DateTime!
  id: Int!
  name: NonEmptyString!
  updatedAt: DateTime!
  userId: NonEmptyString!
  value: NonEmptyString!
}
type UserInsightsConfigResult {
  messages: [String]!
  status: String!
}
type UserInviteParams {
  firstName: String
  lastName: String
  occupation: String
  orgsAffiliation: JSON
  phoneNumber: String
}
type UserInviteResponse {
  redirect: String
}
type UserLoginStepResponse {
  exists: Boolean!
  pendingPasswordReset: Boolean!
}
type UserNode {
  id: ID!
  internal: Boolean
  role: ID
  type: [String]
}
type UserOrgInvitationResult {
  id: String
  info: JSON
  meta: JSON
  success: Boolean
}
type UserOrgRelationships {
  governingOrgs: [Org!]!
  orgRelationships: [OrgRelationships!]!
}
type UserParentsError implements Error {
  message: String!
}
type UserParentsInfo {
  parents: [User!]!
  user: User
}
type UserParentsPublicError implements Error {
  message: String!
}
type UserParentsPublicInfo {
  parents: [UserPublic!]!
  user: UserPublic
}
type UserPermission {
  id: String!
  scope: String
}
type UserPermissionsList {
  id: String
  negativePermissions: [UserPermission]
  permissions: [UserPermission]
  roleIds: [String]
}
type UserPreference {
  featureTourCompleted: Boolean
  id: String
  userId: String
}
type UserPublic {
  createdAt: Date
  email: String
  firstName: String
  id: String
  isInternal: Boolean
  lastName: String
  notMigrated: Boolean
  occupation: UserOccupation
  phoneNumber: String
  profilePicture: String
  role: String
}
type UserPublicInfo {
  isInternal: Boolean
  notMigrated: Boolean
}
type UserPublicInfoResult {
  info: UserPublicInfo
  user: UserPublic
}
type UserResetPasswordByAdminResponse {
  user: User
}
type UserResetPasswordResponseWithAvailableTransport {
  availableTransport: [TransportEnum!]!
  info: String
  result: [TransportType!]!
}
type UserSavedRep {
  snapRepEmail: String
  uuId: String
}
type UserWithPermissions {
  apps: [String]
  email: String
  firstName: String
  hasFamily: Boolean
  hsGradYear: String
  id: String
  kyc: VaultKyc
  language: String
  lastLoginAt: Date
  lastName: String
  occupation: UserOccupation
  parent: String @deprecated(reason: "use parents instead of parent")
  parents: [String]
  permissions: [String]
  phoneNumber: String
  profilePicture: String
  snapRaiseId: String @deprecated(reason: "will be removed in the next release")
  snapSpendId: String @deprecated(reason: "will be removed in the next release")
}
type Users {
  nextPage: Boolean
  users: [User]
}
type VaultAccountPayoutFrequencySetAuditEntry {
  created: DateTime!
  id: ID!
  orgId: ID
  updateAccountResponse: JSONObject!
  userId: ID
}
type VaultAccountStatus {
  accountRepresentativeRequirements: VaultEntityRequirements
  beneficialOwnersRequirements: [VaultEntityRequirements!]
  kybRequirements: VaultEntityRequirements
  pending: Boolean
  valid: Boolean!
}
"Used for addresses in VaultKyb and VaultKyc"
type VaultAddress {
  city: String!
  postalCode: String!
  region: String!
  street: String!
  unit: String
}
type VaultBankAccount {
  account_holder_name: String
  account_holder_type: String
  bank_name: String!
  country: String!
  currency: String!
  default_for_currency: Boolean!
  fingerprint: String!
  id: ID!
  last4: String!
  routing_number: String!
  status: String!
}
"Additional data passed into the Stripe api Can be read in Stripe dashboard"
type VaultCardMetadata {
  description: String
  walletId: String
}
type VaultCreateKybKycBeneficialOwnerAuditEntry {
  addressCity: String!
  addressPostalCode: String!
  addressRegion: String!
  addressStreet: String!
  addressUnit: String
  createStripePersonResponse: JSONObject!
  director: Boolean
  dobDay: String!
  dobMonth: String!
  dobYear: String!
  email: String!
  executive: Boolean
  firstName: String!
  id: ID!
  kycId: ID
  lastName: String!
  owner: Boolean
  percentOwnership: Float
  phoneNumber: String!
  representative: Boolean
  ssn: String
  title: String
}
type VaultCreateKybKycSubmitAuditEntry {
  accountId: String
  beneficialOwners: [VaultCreateKybKycBeneficialOwnerAuditEntry!]
  createStripeConnectAccountPayableOrgResponse: JSONObject
  createStripeConnectAccountResponse: JSONObject
  createStripeRepresentativePayableOrgResponse: JSONObject
  createStripeRepresentativeResponse: JSONObject
  created: DateTime!
  financialAccountNodeId: ID
  id: ID!
  kybAddressCity: String!
  kybAddressPostalCode: String!
  kybAddressRegion: String!
  kybAddressStreet: String!
  kybAddressUnit: String
  kybCustomerFacingName: String!
  kybDescription: String
  kybEmail: String
  kybId: ID
  kybLegalName: String!
  kybPhoneNumber: String!
  kybRaiseFundraiserId: ID
  kybStructure: KYB_STRUCTURE!
  kybTaxId: String!
  kybType: KYB_TYPE!
  kybUrl: String
  orgId: ID
  ownersProvidedPayableOrgResponse: JSONObject
  ownersProvidedResponse: JSONObject
  representativeAddressCity: String
  representativeAddressPostalCode: String
  representativeAddressRegion: String
  representativeAddressStreet: String
  representativeAddressUnit: String
  representativeDirector: Boolean
  representativeDobDay: String
  representativeDobMonth: String
  representativeDobYear: String
  representativeEmail: String
  representativeExecutive: Boolean
  representativeFirstName: String
  representativeKycId: ID
  representativeLastName: String
  representativeOwner: Boolean
  representativePercentOwnership: Float
  representativePhoneNumber: String
  representativeRepresentative: Boolean
  representativeSsn: String
  representativeStripePersonId: ID
  representativeTitle: String
  saveToPayableOrgId: ID
  statementDescriptor: String
  stripeEnv: String
  stripeMetadataActivityType: String
  stripeMetadataEntity: String
  stripeMetadataFundraiserId: String
  stripeMetadataFundraiserIdLegacy: String
  stripeMetadataPaymentType: String
  stripeMetadataTransactionId: String
  userId: ID
}
"Day, Month and Year of birth"
type VaultDob {
  day: String
  month: String
  year: String
}
type VaultEntityRequirements {
  missingFields: [String!]!
  requiredDocumentIds: [String!]
  requiredDocumentOwnerNames: [String!]
  requiredDocuments: [VaultRequiredDocument!]!
}
type VaultFormMutationError {
  details: JSONObject
  field: String
  fieldMessage: String
  message: String!
  retryable: Boolean!
  type: String!
}
type VaultFormMutationResponse {
  errors: [VaultFormMutationError!]
  success: Boolean!
  successData: VaultFormSuccessData
}
type VaultFormSuccessData {
  beneficialOwners: [VaultKycSuccessData!]
  kybData: VaultKybSuccessData!
  payableOrgNode: VaultPayableOrgSuccessData
  representative: VaultKycSuccessData!
  stripeEnv: VaultStripeEnv
}
"Organizations/Companies"
type VaultKyb {
  address: VaultAddress!
  businessVertical: String
  customerFacingName: String!
  description: String!
  email: String
  entityDocument: String
  id: ID!
  legalName: String!
  phoneNumber: String!
  stateOfIncorporation: String
  structure: KYB_STRUCTURE!
  taxId: String!
  type: KYB_TYPE!
  url: String
  yearOfIncorporation: String
}
type VaultKybSuccessData {
  applicationId: String
  financialAccountId: ID
  kybId: ID!
  metadata: StripeMetadata
  orgId: ID
  stripeConnectAccountId: String
}
"Individuals"
type VaultKyc {
  address: VaultAddress!
  director: Boolean
  dob: VaultDob!
  email: String!
  executive: Boolean
  firstName: String!
  id: ID!
  lastName: String!
  owner: Boolean
  "If owner=true, percentOwnership is required"
  percentOwnership: Int
  phoneNumber: String!
  representative: Boolean
  ssn: String
  title: String
}
type VaultKycSuccessData {
  kycId: ID!
  stripePersonId: String
  userId: ID
}
type VaultMutationError {
  message: String!
  type: String
}
type VaultMutationResponse {
  errors: [VaultMutationError]
  success: Boolean!
}
type VaultOrgConfirmationBeneficialOwnerAuditEntry {
  addressCity: String!
  addressPostalCode: String!
  addressRegion: String!
  addressStreet: String!
  addressUnit: String
  createStripePersonResponse: JSONObject!
  director: Boolean
  dobDay: String!
  dobMonth: String!
  dobYear: String!
  email: String!
  executive: Boolean
  firstName: String!
  id: ID!
  kycId: ID
  lastName: String!
  owner: Boolean
  percentOwnership: Float
  phoneNumber: String!
  representative: Boolean
  ssn: String
  title: String
}
type VaultOrgConfirmationSubmitAuditEntry {
  accountId: ID!
  beneficialOwners: [VaultOrgConfirmationBeneficialOwnerAuditEntry!]
  created: DateTime!
  financialAccountNodeId: ID
  id: ID!
  kybId: ID!
  orgId: ID
  replaceWorkflowCreateStripeRepresentativeResponse: JSONObject
  replaceWorkflowDeleteStripePersonResponse: JSONObject
  representativeAddressCity: String
  representativeAddressPostalCode: String
  representativeAddressRegion: String
  representativeAddressStreet: String
  representativeAddressUnit: String
  representativeDirector: Boolean
  representativeDobDay: String
  representativeDobMonth: String
  representativeDobYear: String
  representativeEmail: String
  representativeExecutive: Boolean
  representativeFirstName: String
  representativeKycId: ID
  representativeLastName: String
  representativeOwner: Boolean
  representativePercentOwnership: Int
  representativePhoneNumber: String
  representativeRepresentative: Boolean
  representativeSsn: String
  representativeStripePersonId: ID
  representativeTitle: String
  representativeToReplaceKycId: ID
  representativeToReplaceStripePersonId: ID
  representativeToReplaceUserId: ID
  representativeUserId: ID
  saveToPayableOrgId: ID
  statementDescriptor: String
  stripeEnv: String
  stripeMetadataActivityType: String
  stripeMetadataEntity: String
  stripeMetadataFundraiserId: String
  stripeMetadataFundraiserIdLegacy: String
  stripeMetadataPaymentType: String
  stripeMetadataTransactionId: String
  submittedOnYourBehalf: Boolean!
  submittedOnYourBehalfCreateStripeConnectAccountResponse: JSONObject
  submittedOnYourBehalfCreateStripeRepresentativeResponse: JSONObject
  updateStatementDescriptorResponse: JSONObject
  userId: ID
}
type VaultOwner {
  createdAt: DateTime!
  financialAccountId: String
  id: ID!
  kycId: ID!
  orgId: String!
  stripeConnectAccountId: String
  stripePersonId: String
  updatedAt: DateTime!
}
type VaultPayableOrgSuccessData {
  beneficialOwners: [VaultKycSuccessData!]
  kybData: VaultKybSuccessData!
  representative: VaultKycSuccessData
}
type VaultStripeConnectAccountPaymentMethodDefaultSetAuditEntry {
  createTokenResponse: JSONObject
  created: DateTime!
  id: ID!
  orgId: String
  updateAccountResponse: JSONObject
  userId: String
}
type Vehicle {
  id: Int!
  is_deleted: Boolean
  status: String
  vehicle_type: String
}
type WinbackDeals {
  activity: String
  amount: String
  closedLost: String
  dealName: String
  dealStage: String
  hubspotId: String
  id: String
  isWinbackPinned: Boolean
  lastLaunch: String
  leaderFirstName: String
  leaderLastName: String
  previousDealId: String
  previousFundId: String
}
type Worker {
  duty: String
  email: String
  event: Event
  event_id: Int
  id: Int!
  organization: String
  paid: String
  pay_code: String
  pay_rate: Float
  salary: Float
  ssn: String
  woker_name: String
  worker_end_time: String
  worker_start_time: String
  worker_type: String
}
type WorkerDeleteManyCount {
  count: Int
}
type WorkerPool {
  Address: String
  City: String
  First: String
  Home_Phone: String
  ID: Int!
  Last: String
  SSN: String
  State: String
  Work_Phone: String
  Zip: String
  cell_phone: String
  email: String
  is_deleted: String
  pay_rate: Float
  worker_type: String
}
type WorkerUpsertManyCount {
  count: Int
}
type approvedFunds {
  approvedFundsCents: Int
  isPaidBack: Boolean
}
type busSchedules {
  activity: String
  bus_count: Int
  bus_departure_location: String
  bus_early_dismissal_time: String
  bus_estimated_return_time: String
  bus_time: String
  cancellation_status: String
  comments: String
  confirmed: String
  end_time: String
  event_date: String
  event_transport_details: [DailyCalendarEventTransportDetails]
  facility: String
  g_s: String
  gender: String
  home_field: String
  id: Int
  levels: String
  opponent: String
  place: String
  season_id: Int
  sports_code: String
  sports_description: String
  sports_group: String
  sports_name: String
  start_time: String
  vehicle_type: String
}
type completeFundraiserApprovalSubmissionDocumentUploadResult {
  filename: String!
  s3FileName: String!
}
type creditMemoAmounts {
  creditApplied: Int
}
type driveGetListCurrentCustomersReturn {
  offset: Int
  results: [CurrentCustomersDeals]
  totalCount: Int
}
type driveGetListPotentialCustomersReturn {
  offset: Int
  results: [PotentialCustomersDeals]
  totalCount: Int
}
"Last clicked tracking information used by Drive's Get List Application"
type driveOrgUserTrackingReturn {
  "ID of the organization."
  orgId: String
  "A unique tracking id"
  trackingId: String
  trackingType: String
  "The value of the tracking type"
  trackingValue: String
  trackingValueType: String
  "ID of the user."
  userId: String
}
type earlyAccess {
  activityType: String
  earlyAccessApprovedFundsMax: Int
  earlyAccessApprovedFundsMin: Int
  isNationalRpk: Boolean
  location: String
  participantCount: Int
  projectedRaisedMax: Int
  projectedRaisedMin: Int
}
type insightsUserOrgsNotificationPreferencesInput {
  userId: String
}
type insightsUserOrgsNotificationPreferencesResponse {
  errors: [String]
  orgNotificationPreferences: [orgNotificationPreference]!
  status: String!
  userNotificationPreferences: [userNotificationPreference]!
}
type orgNotificationPreference {
  id: Int
  org_id: String
}
type participantCampaignConfiguration {
  autoImportEmails: ParticipantCampaignConfigStates!
  autoImportTexts: ParticipantCampaignConfigStates!
  campaignID: String!
  giftShop: ParticipantCampaignConfigStates!
  guardianSetup: ParticipantCampaignConfigStates!
  id: ID!
  profileSetup: ParticipantCampaignConfigStates!
  rewards: ParticipantCampaignConfigStates!
  updatedAt: DateTime!
  userID: String!
}
type spendAdminApprovalUpdateResponse {
  success: Boolean!
}
type spendAdminCustomerCloseResponse {
  success: Boolean!
}
type spendOrganizationPieChartResponse {
  credited: Int!
  paid: Int!
  pastDue: Int!
  processing: Int!
  upcoming: Int!
}
type storeOrderSalesGraph {
  baseTotalSales: Float
  endDate: String
  scopeId: BigInt
  startDate: String
  totalDiscountAmount: Float
  totalOrders: BigInt
  totalSales: Float
}
""
type twispCardBalances {
  ""
  allTxs: [twispTransactions]
  ""
  authorization_balance: Int64
  ""
  balance: Int64
  ""
  card_id: String
  ""
  card_limit: Int64
  ""
  history(limit: Int32 = "1000", sort: Sort): [twispCardBalances]
  ""
  seq: Uint64
  ""
  settled_balance: Int64
  ""
  visibleTxs: [twispTransactions]
}
""
type twispCards {
  ""
  card_id: String
  ""
  history(limit: Int32 = "1000", sort: Sort): [twispCards]
  ""
  last4: String
  ""
  seq: Uint64
}
""
type twispStripeHooks {
  ""
  account: String
  ""
  api_version: String
  ""
  created: Int64
  ""
  data(expression: String = "this"): JSON
  ""
  history(limit: Int32 = "1000", sort: Sort): [twispStripeHooks]
  ""
  id: String
  ""
  livemode: Boolean
  ""
  object: String
  ""
  pending_webhooks: Int64
  ""
  processed: Timestamp
  ""
  request(expression: String = "this"): JSON
  ""
  seq: Uint64
  ""
  type: String
}
""
type twispTransactions {
  ""
  amount: Int64
  ""
  card_id: String
  ""
  correlation: String
  ""
  created: Int64
  ""
  description: String
  ""
  event_created: Int64
  ""
  history(limit: Int32 = "1000", sort: Sort): [twispTransactions]
  ""
  id: String
  ""
  last4: String
  ""
  meta(expression: String = "this"): JSON
  ""
  seq: Uint64
  ""
  status: String
  ""
  visible: Boolean
}
type userNotificationPreference {
  id: Int
  name: String
}
interface DriveUser {
  email: String
  firstName: String
  lastName: String
}
interface Error {
  message: String!
}
interface FundraisersError {
  message: String!
}
interface GroupLeaderDonationInvite {
  createdAt: Date!
  deliveryStatus: EmailDeliveryStatus!
  id: ID!
  status: DonationInviteStatus
}
interface GroupLeaderErrorMessage {
  message: String!
}
interface IFundraisersDonationInvite {
  emailAddress: String
  fundraiserId: ID!
  id: ID!
  lastEmailNumber: Int
  lastEmailSentAt: String
  status: DonationInviteStatus
}
interface Tokens {
  accessToken: String
  refreshToken: String
}
union Account = ExternalBankAccount | StripeAccount
union ApproveFundraiserApprovalSubmissionResult = ApproveFundraiserApprovalSubmissionData | InvalidToken
"CampaignMembership is our way of securing information based on a users membership type. When a user queries campaignMemberships they can get information on their memberships based on who they are as defined in the JWT. Example of how to use:https://www.notion.so/snap-mobile/Campaign-1b4c4a055bc84aaf8290f078f57a5193"
union CampaignMembership = AdminCampaign | GroupLeaderCampaign | ParticipantCampaign | SupporterCampaign
union ChatChannelAddModeratorsResult = Channel | ChannelAddModeratorsError | ChannelNotFound | NotAuthenticated | NotAuthorized
union ChatChannelAddUsersResult = Channel | ChannelAddUsersError | ChannelNotFound | NotAuthenticated | NotAuthorized
union ChatChannelCreateResult = Channel | ChannelCreateError | NotAuthorized
union ChatChannelResult = Channel | ChannelNotFound | MissingArguments | NotAuthenticated
union ChatChannelUpdateMembersResult = Channel | ChannelNotFound | ChannelUpdateMembersError | MissingArguments | NotAuthorized
union ChatChannelUpdateResult = Channel | ChannelNotFound | ChannelUpdateError | NotAuthenticated | NotAuthorized
union ChatChannelsResult = Channels | NotAuthenticated
union ChatTokenResult = ChatToken | NotAuthenticated
union ChatUnreadMessagesResult = NotAuthenticated | UnreadCount | UnreadCountError
union DonorFundraiserPayable = DonorDonation | DonorFundraiserPurchase | DonorOfflineDonation
union FundraiserApprovalSubmissionsOrgNamesResult = FundraiserApprovalSubmissionsOrgNames | InvalidToken
union FundraisersDonationInvite = FundraisersGroupLeaderDonationInvite | FundraisersParticipantDonationInvite
union InviteSendResult = InviteError | InviteInfo
union MagentoOrderUnionType = MagentoOrders | OnException
union MagentoPointActivityUnionType = MagentoPointActivities | OnException
union MagentoStorePointsEarnedUnionType = MagentoStoresPointsEarned | OnException
union MagentoStoreUnionType = MagentoStore | OnException
union MessagesChannelInviteAcceptResult = Channel | ChannelInviteAcceptError | InviteNotFound | NotAuthenticated
union MessagesChannelInviteLinkRevokeResult = ChannelInviteLink | ChannelInviteLinkRevokeError | InviteNotFound | NotAuthenticated
union MessagesChannelInviteLinksResult = ChannelInviteLinks | ChannelNotFound | NotAuthenticated | NotAuthorized
union MessagesChannelInviteMembersResult = ChannelInviteMembers | ChannelInviteMembersError | ChannelNotFound | NotAuthenticated | NotAuthorized
union MessagesChannelInviteResendResult = ChannelInvite | ChannelInviteResendError | ChannelNotFound | NotAuthenticated | NotAuthorized
union MessagesChannelInvitesResult = ChannelInvites | ChannelNotFound | NotAuthenticated | NotAuthorized
union MessagesChannelJoinResult = Channel | ChannelJoinError | InviteNotFound | NotAuthenticated
union MessagesChannelMembersResult = ChannelMembers | ChannelNotFound | NotAuthenticated
union MessagesChannelOrgResult = ChannelNotFound | ChannelOrg | ChannelOrgError
union MessagesChannelRemoveMembersResult = ChannelMembers | ChannelNotFound | ChannelRemoveMembersError | NotAuthenticated | NotAuthorized
union MessagesMyChannelsResult = MyChannels | NotAuthenticated
union MessagesMyModeratedChannelsResult = MyModeratedChannels | NotAuthenticated
union MessagesUpsertUsersResult = ChannelUpsertUsersError | MessagesUsers | NotAuthorized
union OrdersFilterUnionType = OnException | OrdersFilter
union OrdersSummaryUnionType = OnException | OrdersSummary
union ParticipantGroupMutationResult = GroupLeaderFundraiserNotFound | GroupLeaderNotFound | GroupLeaderParticipantGroup | GroupLeaderValidationError
union Payout = Check | Deposit
union PointActivityFilterUnionType = OnException | PointActivityFilter
union RevisionFundraiserApprovalSubmissionResult = InvalidToken | RevisionFundraiserApprovalSubmissionData | UnprocessableSubmission
union ShipmentExternalId = ShipmentCampaignId | ShipmentOtherId
union StoreBaseSellerUnionType = OnException | StoreScopeBestSeller
union StoreBrandsUnionType = OnException | StoreBrands
union StoreBuildRequestUnionType = OnException | StoreBuildRequest
union StoreCampaignPayableInfoUnionType = OnException | PayableInfo
union StoreColorsUnionType = OnException | StoreColors
union StoreEditInfoUnionType = OnException | StoreEditInfo
union StoreFilterUnionType = OnException | StoresFilter
union StoreManagerUpdatePointsUnionType = MagentoStoreManager | OnException
union StoreOrderItemsUnionType = OnException | StoreOrderItems
union StorePayableFilterUnionType = OnException | StorePayableFilter
union StorePayableInfoUnionType = OnException | PayableInfo
union StorePaymantIntentUnionType = OnException | StorePaymentIntent
union StorePointsWithdrawalFilterUnionType = OnException | StorePointsWithdrawalFilter
union StorePointsWithdrawalRequestUnionType = OnException | StorePointsWithdrawalRequest
union StoreSubscribedUserUnionType = OnException | StoreSubscribedUser
union StoreTicketUnionType = OnException | StoreTickets
union StoreTicketsUnionType = OnException | StoreTicket
union StoreTransactionUnionType = OnException | StoreTransaction
union StoreTransferPointsUnionType = OnException | StoreTransferReturn
union StoreUserCampaignUnionType = OnException | StoreUserCampaigns
union StoreUserInfoUnionType = OnException | StoreUser
union StoreUserParticipantUnionType = OnException | StoreUserParticipants
union StoresSummaryUnionType = OnException | StoresSummary
union UserParentsInfoResult = UserParentsError | UserParentsInfo
union UserParentsPublicInfoResult = UserParentsPublicError | UserParentsPublicInfo
union storeOrderSalesGraphUnionType = OnException | StoreOrderSalesGraphs
enum AccountType {
  EXTERNAL_BANK
  "more TK"
  STRIPE
}
enum ActivityStatus {
  BLOCKED
  BOUNCE
  CLICK
  DEFERRED
  DELIVERED
  DROPPED
  FAILED
  OPEN
  PENDING
  RESUBSCRIBE
  SENT
  SPAM_REPORT
  UNSUBSCRIBE
}
enum Affiliate {
  BOOSTER
  FOUNDATION
  OTHER
  PARENT_ASSOCIATION
  PARENT_ORGANIZATION
  PARENT_TEACHER_ASSOCIATION
  PARENT_TEACHER_ORGANIZATION
  PARENT_TEACHER_STUDENT_ASSOCIATION
  PARENT_TEACHER_STUDENT_ORGANIZATION
}
enum AffiliationTitle {
  ACTIVITIES_DIRECTOR
  ADMINISTRATIVE_ASSISTANT
  ASSISTANT_COACH
  ATHLETIC_DIRECTOR
  BOOSTER_CLUB_LEADER
  BOOSTER_CLUB_MEMBER
  BUSINESS_OWNER
  CLUB_SPORTS_ADMINISTRATOR
  CLUB_SPORTS_DIRECTOR
  CLUB_SPORT_EMPLOYEE
  COACH
  DISTRICT_ADMINISTRATOR
  FINANCIAL_ADMINISTRATOR
  FINE_ARTS_DIRECTOR
  GROUP_LEADER
  OTHER
  PARENT
  PRESIDENT
  SCHOOL_ADMINISTRATOR
  SCHOOL_CLUB_ADVISOR
  SPONSOR
  STATE_ADMINISTRATOR
  STUDENT
  TEACHER_INSTRUCTOR
  VOLUNTEER
}
enum Audience {
  GUARDIANS
}
enum AuditLogService {
  Connect
  Donors
  Drive
  Home
  Insights
  Manage
  Orgs
  Raise
  Spend
  Sponsor
  Store
  UserDirectory
  Vault
  Wallet
}
enum AuditLogSource {
  Backend
  Db
  Frontend
}
enum AuditLogType {
  Debug
  Error
  Info
  Warning
}
enum BasicCampaignStatus {
  ACTIVE
  CLOSED
  UPCOMING
}
"Owned by Vault Only used in legacy createCard"
enum CARD_STATUS {
  active
  canceled
  inactive
}
"Owned by Vault Only used in legacy createCard"
enum CARD_TYPE {
  physical
  virtual
}
enum CacheControlScope {
  PRIVATE
  PUBLIC
}
"Fixed statuses for a campaigns kyc"
enum CampaignKycStatus {
  action_required
  in_review
  require_docs
  unsubmitted
  unverified
  verified
}
enum CampaignKycStatusEnum {
  ACTION_NEEDED
  PENDING
  UNSUBMITTED
  VERIFIED
}
"Fixed type for a campaigns kyc"
enum CampaignKycType {
  stripe
  wepay
}
enum CampaignMemberAssociation {
  ADMIN
  COACH
  COCOACH
  COPARTICIPANT
  PARTICIPANT
  SUPPORTER
}
enum CampaignSmsInviteStatus {
  PRELOADED
  PRELOAD_QUEUED
  QUEUED
}
enum CampaignStatus {
  ACTIVE
  APPROVED
  ARCHIVE
  CLOSED
  DELETED
  FINAL
  PENDING_APPROVAL
  SETTLED
}
enum CategoryFilterEnum {
  name
  type
}
enum CategoryTypeEnum {
  expense
  income
}
enum ChannelAudienceEnum {
  GUARDIANS
  STUDENTS
}
enum ChannelInviteStatusEnum {
  ACCEPTED
  AUTO_ACCEPTED
  PENDING
}
enum ChannelRolesEnum {
  channel_member
  channel_moderator
}
enum ChannelStatusEnum {
  ACTIVE
  ARCHIVED
  DISABLED
  FROZEN
}
enum ChannelTypesEnum {
  fundraiser
  private_channel
  roster_messaging
  roster_messaging_guardians
}
enum CheckStatus {
  DEPOSITED
  "TODO(SHR): update this once we get actual statuses from bill.com"
  PROCESSING
  SENT
}
enum Color {
  BLACK
  BLUE
  BROWN
  GRAY
  GREEN
  LIGHT_BLUE
  ORANGE
  PINK
  PURPLE
  RED
  WHITE
  YELLOW
}
enum ContactMethodEnum {
  EMAIL
  PHONE_NUMBER
}
enum ContactSupportInquiryType {
  GENERAL_INQUIRY
  INSIGHTS
  LEAGUES
  MANAGE_REGISTRATION
  MANAGE_SCHEDULE
  MANAGE_SITES
  MESSAGING
  RAISE_FUNDRAISING
  SNAP_MOBILE_APP
  SPEND
  STORE
}
enum ContactTemplateMedium {
  EMAIL
  TEXT_MESSAGE
}
enum ContactTemplateType {
  GROUP_LEADER
  PARTICIPANT
}
enum DebitCardActions {
  activate
  close
  freeze
  replace
  report_lost
  report_stolen
  unfreeze
}
enum DonateIntentStatus {
  CANCELLED
  CHARGED_BACK
  FAILED
  INITIATED
  PENDING
  PROCESSING
  REFUNDED
  SUCCEEDED
}
enum DonationInviteStatus {
  COMPLETED
  DELETED
  DONATED
  LOADED
  PRELOADED
  RELEASED
}
enum DonationLevels {
  FIVE
  FOUR
  ONE
  SIX
  THREE
  TWO
}
enum DonationsAndPurchasesEnum {
  donation
  purchase
}
enum DonorDonationCheckoutState {
  AUTHORIZED
  CANCELLED
  CAPTURED
  CHARGED_BACK
  EXPIRED
  FAILED
  NEW
  REFUNDED
  RELEASED
}
enum DonorDonationStatus {
  ANONYMOUS
  CHARGED_BACK
  COMPLETE
  OFFLINE
  ONLINE
  PAID
  REFUNDED
  REFUNDING
  RELEASED
  UNPAID
  UPDATED1
  UPDATED2
}
enum DonorFundraiserPayableTypeEnum {
  donation
  purchase
}
enum DriveAttendanceStatus {
  ATTENDING
  NOT_ATTENDING
}
enum DriveEventStatus {
  CLOSED
  EMAIL_SENT
  NOT_ATTENDING
  PENDING
  REGISTERED
}
enum DriveFieldType {
  HTML
  LINK
  TEXT
}
enum DriveScheduleTimeFrame {
  AFTERNOON
  EVENING
  MORNING
}
enum EmailDeliveryStatus {
  DELIVERED
  FAILED
  IN_PROGRESS
  PRELOADED
}
enum EventAttendeeRequestStatus {
  NO_REQUEST
  PROCESSED
  REQUEST
}
enum EventDateStatus {
  Active
  Closed
  Upcoming
}
enum EventType {
  EVENT
  FUNDRAISER
  GAME
  MEETING
  OTHER
  PRACTICE
}
enum ExternalTransferDirectionEnum {
  Credit
  Debit
}
"Owned by Vault Only used in legacy createCard"
enum FINANCIAL_GATEWAY {
  stripe
}
enum FinAcctStatus {
  ACTION_REQUIRED
  ACTIVE
  APPROVED
  AWAITING_REAPPROVAL
  DECLINED
  PENDING
  TERMINATED
  UNDER_REVIEW
}
enum FinAcctStripeEnv {
  RAISE
  SPONSOR
}
enum FundraiserAcknowledgementStatus {
  ARCHIVED
  DRAFT
  SIGNED
}
enum FundraiserAddressType {
  CHECK
  MAILING
  SHIPPING
}
enum FundraiserApprovalSubmissionDocumentInputAction {
  ADD
  REMOVE
}
enum FundraiserApprovalSubmissionFormType {
  ATHON
  EVENTS
  ONLINE
  OTHER
  PRODUCT
  SNAP
}
enum FundraiserApprovalSubmissionStatus {
  APPROVED
  DRAFT
  REVISION_REQUESTED
  SUBMITTED
}
enum FundraiserApproverInputAction {
  ADD
  REMOVE
  UPDATE
}
enum FundraiserApproverType {
  PRIMARY
  SECONDARY
}
enum FundraiserBasicStatus {
  ACTIVE
  CLOSED
  UPCOMING
}
enum FundraiserCreationOrigin {
  FUNDRAISERS_SERVICE
  RAISE_CLASSIC
}
enum FundraiserGroupLeaderRoles {
  GROUP_LEADER
  PARTICIPANT
  PRIMARY_GROUP_LEADER
}
enum FundraiserInvitesReleaseType {
  EMAIL
  SMS
}
enum FundraiserKycStatus {
  RESUBMIT
  SUBMITTED
  UNSUBMITTED
}
enum FundraiserManagerAlertType {
  FIRST_LOAD_FUNDRAISER_ACTIVE
  FIRST_LOAD_FUNDRAISER_CLOSED
}
enum FundraiserManagersRole {
  GROUP_LEADER
  PRIMARY_GROUP_LEADER
}
enum FundraiserMediaType {
  IMAGE
  VIDEO
}
enum FundraiserOrigin {
  FUNDRAISERS_SERVICE
  RAISE_CLASSIC
}
enum FundraiserSettlementMethod {
  CHECK_FUNDRAISER
  "deprecated value"
  CHECK_OFFICE
  CHECK_SALESREP
  "deprecated value"
  DEFAULT
  DIRECT_DEPOSIT
}
enum FundraiserSetupProgressStatus {
  COMPLETED
  IN_PROGRESS
  NOT_APPLICABLE
  NOT_STARTED
}
enum FundraiserSmsInvitesReleaseTimeframe {
  AFTERNOON
  EVENING
  MORNING
}
enum FundraiserStatus {
  ACTIVE
  APPROVED
  ARCHIVE
  CLOSED
  DELETED
  FINAL
  PENDING_APPROVAL
  SETTLED
}
enum FundraiserUserImportType {
  csv_import
  rollover
}
enum FundraiserVaultKycStatus {
  ACCOUNT_NOT_FOUND
  ADDITIONAL_INFO_REQUIRED
  VERIFIED
}
enum GroupLeaderDonationInviteType {
  EMAIL
  SMS
}
enum GroupLeaderEmailType {
  DONOR_THANK_YOU
  PARTICIPANT_CONGRATULATION
}
enum GroupLeaderFundraiserPdfType {
  PARTICIPANT_JOIN_FUNDRAISER
}
enum GroupRosterFilterEnum {
  groupId
  id
  memberId
  rosterId
  seasonId
}
enum HubspotSortDirection {
  ASCENDING
  DESCENDING
}
enum InsSetNotificationPreferenceFlag {
  OFF
  ON
}
enum InsSetNotificationType {
  ORGANIZATION
  USER
}
enum InsUserNotificationType {
  CHECK_SENT
  DEPOSIT_MADE
  FUNDRAISER_CREATED
  FUNDRAISER_FINALIZED
}
enum InviteFilterEnum {
  group
  program
  status
  type
}
enum InviteType {
  channel
  org
  parent
}
enum InvoiceFilterEnum {
  date
  groupId
  groupName
  groupRosterId
  inviteId
  playerName
  seasonId
  status
  unreconciled
}
enum InvoiceGuardianFilterEnum {
  groupId
  rosterId
}
enum InvoiceReportFilterEnum {
  dateAfter
  dateBefore
  excludeArchived
  groupId
  groupRosterId
  inviteId
  orgId
  pastDueRange
  playerName
  status
}
enum JoinCodeType {
  GROUP_LEADER
  INVALID
  PARTICIPANT
}
enum JoinFundraiserInviteSource {
  CSV_IMPORT
  ROLLOVER
  SNAP_EMAIL
  SNAP_TEXT
}
enum JoinFundraiserInviteStatus {
  DELIVERED
  FAILED
  PENDING
}
enum JoinFundraiserInviteType {
  EMAIL
  EXISTING_USER
  SMS
}
enum JoinFundraiserInviteUpdateType {
  REMOVE
  RESEND
}
enum JoinType {
  COPY_LINK
  COPY_MESSAGE
  CSV_IMPORT
  FANX
  QR_CODE
  ROLLOVER
  SMA
  SNAP_EMAIL
  SNAP_TEXT
}
enum KYB_STRUCTURE {
  free_zone_establishment
  free_zone_llc
  government_instrumentality
  governmental_unit
  incorporated_non_profit
  limited_liability_partnership
  llc
  multi_member_llc
  private_company
  private_corporation
  private_partnership
  public_company
  public_corporation
  public_partnership
  single_member_llc
  sole_establishment
  sole_proprietorship
  tax_exempt_government_instrumentality
  unincorporated_association
  unincorporated_non_profit
}
enum KYB_TYPE {
  company
  government_entity
  individual
  non_profit
}
enum LogoPosition {
  FRONT_CENTER
  FRONT_LEFT_ARM
  FRONT_LEFT_CHEST
  FRONT_LEFT_LEG
  FRONT_RIGHT_ARM
  FRONT_RIGHT_CHEST
  FRONT_RIGHT_LEG
}
enum MacroServiceRequestWorkflowState {
  approved
  canceled
  closed
  completed
  open
  rejected
}
enum MarcoItemWorkflowState {
  canceled
  completed
  intake_hold
  open
  production_hold
  shipped
}
enum MarcoOrderStatus {
  canceled
  completed
  intake_hold
  open
  production_hold
  received
  shipped
}
enum MatchStyle {
  EXACT
  FUZZY
  PREDICTIVE
}
enum MessageTransportType {
  EMAIL
  SMS
}
enum MessageType {
  PROMOTIONAL
  TRANSACTIONAL
}
enum ModType {
  AFFILIATE
  BUSINESS
  CLUB
  DISTRICT
  INDIVIDUAL
  OTHER
  SCHOOL
  TEAM
}
enum OrderStatus {
  FAILURE
  SUCCESS
}
enum OrderType {
  ASC
  DESC
}
enum OrgType {
  BOOSTER
  BUSINESS
  CLUB
  DISTRICT
  FOUNDATION
  PROGRAM
  PTA
  SCHOOL
  SHELLSCHOOL
  TEAM
}
enum OrgTypeWithCampaign {
  BOOSTER
  BUSINESS
  CAMPAIGN
  CLUB
  DISTRICT
  FOUNDATION
  PROGRAM
  PTA
  SCHOOL
  SHELLSCHOOL
  TEAM
}
enum OrganizationFilterEnum {
  pending
}
enum PagesNameEnum {
  AboutUs
  Mission
  SchoolDirections
  Sponsors
  Staff
}
enum ParticipantCampaignConfigStates {
  COMPLETED
  NOT_APPLICABLE
  NOT_COMPLETED
  SKIPPED
}
enum ParticipantDonationInviteStatus {
  COMPLETED
  DELETED
  DONATED
  LOADED
  PRELOADED
  RELEASED
}
enum ParticipantFundraiserConfigStates {
  COMPLETED
  NOT_APPLICABLE
  NOT_COMPLETED
  SKIPPED
}
enum PastDueSortOptions {
  amount
  dueDate
  groups
  parentStatus
  participantName
}
enum PaymentMethod {
  PAYPAL
  STRIPE
}
enum PaymentProvider {
  PAYPAL
  STRIPE
}
enum PaymentScheduleStatus {
  DRAFT
  ON_HOLD
  PUBLISHED
}
enum PaymentsApiCustomerPaymentMethodType {
  BANK
  CARD
  OTHER
}
enum Priority {
  NORMAL
  RUSH
}
enum Processor {
  STRIPE
  UNIT
}
enum Product {
  CONNECT
  INSIGHTS
  MANAGE
  RAISE
  SPEND
  SPONSOR
  STORE
}
enum ProductSize {
  L
  M
  NO_SIZE
  OSFA
  S
  XL
  XS
  XXL
  XXS
  XXXL
  XXXXL
  YL
  YM
  YS
  YXL
  YXS
  YXXL
  YXXXL
}
enum ProductStatus {
  FAILURE
  SUCCESS
}
enum ProgramActivity {
  ACTION_SPORTS
  ARCHERY
  ART
  ASB
  AVID
  BADMINTON
  BAND
  BASEBALL
  BASKETBALL
  BOWLING
  BOXING
  BUSINESS
  CAMP
  CHEERLEADING
  CHESS
  CHOIR
  CREW
  CROSS_COUNTRY
  CULTURE_AND_LANGUAGE
  CYCLING
  DANCE
  DEBATE
  DECA
  DRAMA
  EQUESTRIAN
  FASHION
  FBLA
  FCCLA
  FENCING
  FIELD_HOCKEY
  FILM_AND_TV_PRODUCTION
  FOOTBALL
  FRESHMAN_CLASS
  GOLF
  GSA_AND_LGBTQI
  GYMNASTICS
  HOCKEY
  HOSA
  JOURNALISM
  JUNIOR_CLASS
  KEY_AND_LINK
  LACROSSE
  MARTIAL_ARTS
  MODEL_UN_AND_TRIAL
  MUSIC
  NATIONAL_HONORS_SOCIETY
  NON_PROFIT
  ORCHESTRA_AND_SYMPHONY
  OTHER
  OUTDOORS
  PHOTOGRAPHY
  POLO
  POWERLIFTING
  PROM_AND_HOMECOMING
  ROBOTICS
  ROTC
  RUGBY
  SAILING
  SCHOLARSHIP
  SENIOR_CLASS
  SHOOTING
  SKIING
  SOCCER
  SOFTBALL
  SOPHOMORE_CLASS
  STEM
  STUDENT_GOVERNMENT
  SUMMER_CAMP
  SURF
  SWIM_AND_DIVE
  TABLE_TENNIS
  TENNIS
  TRACK_AND_FIELD
  ULTIMATE_FRISBEE
  VIDEO_GAME
  VOLLEYBALL
  WATER_POLO
  WINTER_SPORTS
  WRESTLING
  WRITING
  YEARBOOK
}
enum PurchaseIntentStatus {
  CART
  PURCHASED
  REFUNDED
  REMOVED
  SUPERMARKET
}
enum PurchaseStatus {
  CANCELLED
  FAILED
  INITIATED
  PROCESSING
  REFUNDED
  REFUNDING
  SUCCEEDED
}
enum RawFileTypeEnum {
  Attachment
  LegacyAttachment
  Statement
}
enum RequestStatus {
  ACTIVE
  BLOCKED
  BOUNCE
  CANCELED
  CLICK
  DEFERRED
  DELIVERED
  DELIVERED_WITH_FAILED
  DROPPED
  FAILED
  OPEN
  PENDING
  RESUBSCRIBE
  SENT
  SPAM_REPORT
  UNSUBSCRIBE
}
enum ResponseStatus {
  FAILURE
  SUCCESS
}
enum RewardStatus {
  DELIVERED
  FAILED
  PENDING
  SENT
}
enum RosterCreator {
  ADMIN
  MESSAGING
  RAISE
  REGISTRATION
  SPEND
}
enum RosterFilterEnum {
  groupName
  memberName
  rosterName
  seasonName
}
"Owned by Vault Only used in legacy createCard"
enum SHIPPING_SERVICE {
  express
  priority
  standard
}
"Owned by Vault Only used in legacy createCard"
enum SPENDING_LIMIT_INTERVAL {
  all_time
  daily
  monthly
  per_authorization
  weekly
  yearly
}
enum SUPPORT_TICKET_DEPARTMENT {
  ACCOUNTS
  HUBSPOT
  IT
  PEP
  SALES
  SPONSOR
  STORE
  WALLET
  WAREHOUSE
}
enum SalesrepType {
  ACCOUNT_MANAGER
  SALES_REP
}
enum SchoolCategory {
  CHARTER
  MAGNET
  PRIVATE
  PUBLIC
}
enum SetUserPreferenceFlag {
  OFF
  ON
}
enum SettlementMethod {
  DIRECT_DEPOSIT
  MAILED_CHECK
}
"Fixed statuses for a campaigns settlement status"
enum SettlementStatus {
  IN_REVIEW
  UNSUBMITTED
  VERIFIED
}
enum ShipmentCarrier {
  FEDEX
  UNKNOWN
  USPS
}
enum ShipmentSource {
  OTF
  OTK_INCENTIVE
  STORE
}
enum ShipmentStatus {
  PROCESSING
  SHIPPED
}
enum ShippingProvider {
  DHL
  FedEx
  OSM
  UPS
  USPS
}
enum SnapFeePayer {
  CUSTOMER
  SNAP
}
enum SnapMobileOneProduct {
  AD_ASSIST
  APP_PORTAL
  CONTROL_PANEL
  INSIGHTS
  LEAGUE_KEEPER
  RAISE
  REGISTRATION
  SPEND
  STORE
}
enum SnapService {
  campaigns
  comms
  connect
  donors
  drive
  fanx
  fundraisers
  home
  insights
  manage
  messages
  orgs
  raise
  spend
  sponsor
  store
  userdirectory
  vault
  wallet
}
""
enum Sort {
  ""
  ASC
  ""
  DESC
}
enum SortDirection {
  ASC
  DESC
}
enum SortField {
  ADVANCED_FUNDS
  CLOSED_STATUS
  DAYS_LEFT
  END_DATE
  KYC_STATUS
  NAME
  PARTICIPATION
  PRELOAD
  SETTLEMENT_DETAILS
  START_DATE
  STATUS
  TOTAL_RAISED
}
enum SortOrderEnum {
  asc
  desc
}
enum SpendCheckImageSide {
  BACK
  FRONT
}
enum SpendCheckImageType {
  CHECK_DEPOSIT
  CHECK_PAYMENT
}
enum SpendGroupRosterStatusEnum {
  accepted
  archived
  autopay_stop
  awaiting_approval
  canceled
  expired
  no_invite_sent
  not_signed_up
  paid
  past_due
}
enum SpendInviteAllType {
  group
  guardian
  program
}
enum SpendInviteType {
  group_observer
  group_staff
  guardian
  program_admin
  program_observer
  program_staff
}
enum SpendPastDueInterval {
  days1_15
  days16_30
  days31plus
}
enum SpendPayoutInterval {
  DAILY
  WEEKLY_FRI
  WEEKLY_MON
  WEEKLY_WED
}
enum SpendPayoutType {
  ACH
  CHECK
}
enum SpendRoleNameEnum {
  group_observer
  group_staff
  guardian
  program_observer
  program_staff
}
enum SpendSortOrderEnum {
  ASC
  DESC
}
enum StateCode {
  AK
  AL
  AR
  AS
  AZ
  CA
  CO
  CT
  DC
  DE
  FL
  GA
  GU
  HI
  IA
  ID
  IL
  IN
  KS
  KY
  LA
  MA
  MD
  ME
  MI
  MN
  MO
  MP
  MS
  MT
  NC
  ND
  NE
  NH
  NJ
  NM
  NV
  NY
  OH
  OK
  OR
  PA
  PR
  RI
  SC
  SD
  TN
  TX
  UM
  UT
  VA
  VI
  VT
  WA
  WI
  WV
  WY
}
enum Status {
  Canceled
  Clearing
  PENDING
  PendingReview
  Rejected
  Returned
  SETTLED
}
enum StoreOrderStatus {
  CLOSED
  CREATED
  DELIVERED
  DESIGN
  HOLD
  MARCO_REJECTED
  REJECTED
  SHIPPED
}
enum StoreStatus {
  FAILURE
  SUCCESS
}
enum StripeEnv {
  RAISE
  SPEND
  SPONSOR
  STORE
}
enum SupportedEmailProviders {
  sendgrid
  test
}
enum SupportedSmsProviders {
  test
  twilio
}
enum SystemNotificationStatus {
  draft
  published
}
enum TeamAge {
  ADULT
  PRETEEN
  TEEN
  YOUTH
}
enum TeamGender {
  COED
  FEMALE
  MALE
}
enum TeamGroupType {
  CLUB
  SPORT
}
enum TeamTier {
  FIRST
  FRESHMAN
  FULL_PROGRAM
  JUNIOR_VARSITY
  JUNIOR_VARSITY_C
  SECOND
  VARSITY
}
enum TeamTitle {
  ASSISTANT_COACH
  BOOSTER_CLUB_LEADER
  BOOSTER_CLUB_MEMBER
  CLUB_SPORTS_ADMINISTRATOR
  CLUB_SPORTS_DIRECTOR
  CLUB_SPORTS_EMPLOYEE
  COACH
  FINE_ARTS_DIRECTOR
  GROUP_LEADER
  SCHOOL_CLUB_ADVISOR
  TEACHER_INSTRUCTOR
}
enum TransactionFilterEnum {
  date
  groupName
  method
  reconciledStatus
  seasonName
  status
  type
}
enum TransactionFilterType {
  ApplicationFee
  Transfer
  UserPayment
}
enum TransactionReportFilterEnum {
  dateAfter
  dateBefore
  groupIdOrgId
  method
  nameIncludes
  status
  type
}
enum TransactionType {
  Donation
  Purchase
}
enum TransportEnum {
  email
  sms
}
enum UserApps {
  connect
  drive
  fanx
  home
  insights
  manage
  raise
  spend
  spend_new
  sponsor
  store
  wallet
}
enum UserChallengeStatus {
  AWAITING
  COMPLETED
  SKIPPED
}
enum UserFittingPreference {
  MENS
  WOMENS
}
enum UserInviteStatus {
  ACCEPTED
  DECLINED
  PENDING
}
enum UserOccupation {
  district_administrator
  financial_administrator
  group_leader
  other
  parent
  school_director
  state_administrator
  student_or_participant
  unknown
}
enum UserPreferenceQueryType {
  ALL
  CAMPAIGN
  ORG
}
enum UserProviders {
  google
}
enum UserTypeConfiguration {
  DEFAULT
  GUARDIAN_LEAD
}
"Current status of card"
enum VaultCardStatus {
  active
  "To deactivate a card set it to canceled"
  canceled
  "Cards ship inactive"
  inactive
}
"Is the card physical (needs to mailed out) or virtual"
enum VaultCardType {
  physical
  virtual
}
enum VaultDocumentProvider {
  stripe
  unit
}
"Third party financial providers"
enum VaultFinancialProvider {
  stripe
  unit
}
enum VaultPayoutInterval {
  daily
  manual
  monthly
  weekly
}
enum VaultRequiredDocument {
  "Utility bill, bank statement, lease agreement or current pay stub"
  address_verification
  "Bank Statement or Void Check"
  bank_account_document
  "Certificate of incorporation or certificate of assumed name"
  certificate_of_incorporation
  "IRS form 147c or IRS form CP-575"
  ein_confirmation
  "Passport or Drivers License"
  id_document
  "Passport, Drivers License or State ID"
  id_document_or_passport
  "501C3 or SS4"
  irs_determination_letter
  "Passport"
  passport
  "Power of attorney"
  power_of_attorney
  "SSN Card"
  ssn_card
}
"How to send a physical VaultCard"
enum VaultShippingService {
  express
  priority
  standard
}
"The interval that the spending limit interval of a card is reset"
enum VaultSpendingLimitInterval {
  all_time
  daily
  monthly
  per_authorization
  weekly
  yearly
}
enum VaultStripeEnv {
  RAISE
  SPONSOR
}
enum VaultWeeklyAnchor {
  friday
  monday
  saturday
  sunday
  thursday
  tuesday
  wednesday
}
enum Vendor {
  MARCO
  RAISE
  RAISE_MARCO
  STORE
}
enum WorkerTypes {
  c
  h
}
enum dateRangeEnum {
  eighteenMonths
  sixMonths
  thirtyDays
  threeMonths
  twelveMonths
}
enum responses {
  FAILED
  OK
}
"Indexes for table twispCardBalances"
enum twispCardBalances_indexes {
  ""
  card_id
}
"Indexes for table twispCards"
enum twispCards_indexes {
  ""
  card_id
}
"Indexes for table twispStripeHooks"
enum twispStripeHooks_indexes {
  ""
  id
}
"Indexes for table twispTransactions"
enum twispTransactions_indexes {
  ""
  card_id
  ""
  card_id_viz
  ""
  id
}
input AccountTransactionsInput {
  accountId: String!
  createdAfter: Timestamp
  createdBefore: Timestamp
  exclude: [TransactionFilterType]
  externalId: String
  metadata: JSONObject
  status: Status
}
input AccountTransactionsPaginatedInput {
  accountId: String!
  createdAfter: Timestamp
  createdBefore: Timestamp!
  cursor: String
  "Currently only support 1000"
  pageSize: Int
  status: Status
}
input AdhocGiftShopIntentInput {
  fundraiserId: Int!
  fundraiserUserId: Int
  participantUserDirectoryId: String
  status: PurchaseIntentStatus!
  userId: Int
}
input AdminUser {
  email: String!
  firstName: String!
  lastName: String!
  phoneNumber: String!
}
input AlumniDonor {
  donateIntentId: String!
  orgId: String
  schoolAddress: String
  schoolName: String
}
input AndroidNotificationInput {
  channelId: String
  priority: Int
  sound: String
}
input ApprovalDateFilterInput {
  endDate: String
  startDate: String
}
input ApprovalFilterInput {
  date: ApprovalDateFilterInput
  groupName: String
  staffName: String
  type: [String!]
}
input AssignmentInput {
  id: String!
  type: ChannelTypesEnum!
}
input BaseOrgInput {
  city: String = null
  description: String = null
  einNumber: String!
  formationYear: Int = null
  hubspotId: String = null
  legalName: String!
  name: String!
  phoneNumber: String = null
  placeId: String = null
  stateCode: StateCode = null
  streetAddress: String = null
  url: String = null
  zipCode: String = null
}
"BoolFilter is used for filtering based on boolean values."
input BoolFilter {
  "Checks if the boolean field is equal to the specified value."
  equals: Boolean
  "Nested filter for negating a condition."
  not: NestedBoolFilter
}
"Filtering options for nullable Boolean type"
input BoolNullableFilter {
  "Equals operation"
  equals: Boolean
  "Not Equal operation"
  not: Boolean
}
input ChannelInviteInput {
  channelId: String!
  email: String
  firstName: String
  lastName: String
  messagesInviteId: String!
  phoneNumber: String
  role: String!
}
input ChannelUpdateInput {
  description: String
  isReadOnly: Boolean
  name: String
  status: ChannelStatusEnum
}
input CommonCalendarFiltersInput {
  activity: String
  endDate: Date
  facilities: [String]
  gender: String
  group: String
  homeAway: String
  levels: [String]
  opponents: [String]
  startDate: Date
  teams: [String]
  vehicleType: String
}
input CommonGridFiltersInput {
  endDate: Date
  facilities: [String]
  gender: String
  levels: [String]
  startDate: Date
  teams: [String]
}
input CommonSchedulesFiltersInput {
  activity: String
  activityName: [String]
  endDate: Date
  facilities: [String]
  gender: String
  group: String
  homeAway: String
  levels: [String]
  opponents: [String]
  startDate: Date
  vehicleType: String
}
input CommonSheetFiltersInput {
  activity: String
  endDate: Date
  facilities: [String]
  gender: String
  group: String
  homeAway: String
  levels: [String]
  opponents: [String]
  startDate: Date
  teams: [String]
  vehicleType: String
}
"Autogenerated input type of CpDeleteEventResult"
input CpDeleteEventResultInput {
  "A unique identifier for the client performing the mutation."
  clientMutationId: String
  schedResultId: ID!
}
input CpEventResultInput {
  author: String
  eventComplete: Boolean
  eventStory: String
  eventTitle: String
  isApproved: Boolean
  onFrontPage: Boolean
  opponentScore: String
  outcome: String
  schedId: ID!
  score: String
}
"Autogenerated input type of CpUpdateEventResult"
input CpUpdateEventResultInput {
  "A unique identifier for the client performing the mutation."
  clientMutationId: String
  eventResultData: CpEventResultInput!
  orgsId: String!
  schedResultId: ID!
}
input CreateChildFundraiserParticipantInput {
  email: String!
  firstName: String!
  fundraiserId: ID!
  groupId: ID
  joinType: JoinType
  lastName: String!
  udid: String!
}
input CreateEventInput {
  activity: String
  author: String
  bus_fee: Float
  bus_time: String
  cancellation_status: String
  comments: String
  conference: String
  conference_event_id: Int
  conference_id: Int
  confirmed: String
  contract: String
  created_at: Date
  departure_location: String
  directions: String
  early_dismissal_required: String
  early_dismissal_time: String
  end_time: String
  estimated_return_time: String
  event_date: Date
  event_id: Int
  exists_in_mls: Int
  fee: Float
  g_s: String
  gate_revenue: Float
  headline: String
  impact_event: String
  lead: String
  location: String
  loss_points: Int
  num_buses: Int
  opponent: String
  opponent_code: String
  opponent_score: String
  picture: String
  place: String
  prep_setup: String
  promote: String
  results: String
  revenue: Float
  rollover: String
  season_team: Int
  start_time: String
  team_score: String
  title: String
  tournament: String
  trans_id: Int
  transport_comments: String
  transportation: String
  update_at: Date
  web_dir: String
  win_points: Int
  years: String
}
input CreateEventParticipantsInput {
  contract_received: String
  event_id: Int
  id: Int
  notes: String
  paid: String
  school_code: String
}
input CreateEventPreparationInput {
  comments: String
  event: Int
  id: Int!
  prep: String
  qty: String
}
input CreateEventTransportDetailsInput {
  driver_name: String
  driver_phone: String
  event_id: Int
  id: Int!
  vehicle_id: String
  vehicle_type: String
}
input CreateFacilityInput {
  Address1: String!
  Address2: String
  City: String!
  State: String!
  ZipCode: String!
  directions: String
  indoor: String
  ml_site_id: String
  ml_space: String
  ml_space_id: String
  picture: String
  place_name: String!
  show: String
  web: String
}
input CreateLevelInput {
  Level: String
  is_deleted: Boolean
}
input CreateOfficialDutyInput {
  duty: String
  is_deleted: Boolean
}
input CreateOfficialInput {
  duty: String
  event_id: Int
  id: Int!
  offic_id: String
  organization: String
  paid: String
  pay_code: String
  received: String
  salary: Float
  ssn: String
  voucher_number: String
  worker_name: String
}
input CreateOfficialPoolByIdInput {
  Address: String
  City: String
  First: String!
  Home_Phone: String
  ID: String!
  Last: String!
  SSN: String
  State: String
  Work_Phone: String
  Zip: String
  cell_phone: String
  email: String
  vendor_number: String
}
input CreateOpponentInput {
  Address: String
  Phone: String
  SchoolCode: String!
  SchoolName: String
  State: String
  Zip: String
  ad_name: String
  city: String
  email: String
  fax: String
  nces_id: String
  non_school: Boolean
}
input CreatePreparationInput {
  duty: String
  id: Int!
  is_deleted: Boolean
  qty: String
}
input CreateSchoolInfoInput {
  Schoolname: String
  ad: String
  ad_contract_signee: String
  address: String
  ccemail: String
  city: String
  conf_text_type: String
  email: String
  email_reminder: String
  email_reminder_officials: String
  email_reminder_workers: String
  enable_cc_email_as_origin: String
  enable_ml_updates: String
  fax: String
  icon: String
  id: String!
  mascot: String
  message_board_read_at: Date
  ml_key: String
  phone: String
  principal: String
  school_timezone: String
  secondary_ad_email: String
  secondary_ad_name: String
  signed_contract_notification: String
  ssn_on_file: String
  state: String
  state_org: String
  state_org_abbreviation: String
  use_security: String
  web_password: String
  zip: String
}
input CreateVehicleInput {
  is_deleted: Boolean
  status: String
  vehicle_type: String
}
input CreateWorkerInput {
  duty: String
  email: String
  event_id: Int
  id: Int!
  organization: String
  paid: String
  pay_code: String
  pay_rate: Float
  salary: Float
  ssn: String
  woker_name: String
  worker_end_time: String
  worker_start_time: String
  worker_type: String
}
input CreateWorkerPool {
  Address: String
  City: String
  First: String!
  Home_Phone: String
  Last: String!
  SSN: String
  State: String
  Work_Phone: String
  Zip: String
  cell_phone: String
  email: String
  pay_rate: Float
  worker_type: String
}
input DateRange {
  endDate: Date!
  startDate: Date!
}
"Filtering options for nullable DateTime type"
input DateTimeNullableFilter {
  "Equals operation"
  equals: String
  "Greater Than operation"
  gt: String
  "Greater Than or Equal operation"
  gte: String
  "In operation for multiple values"
  in: [String!]
  "Less Than operation"
  lt: String
  "Less Than or Equal operation"
  lte: String
  "Not Equal operation"
  not: String
  "Not In operation for multiple values"
  notIn: [String!]
}
input DeleteEventInput {
  event_id: Int!
}
input DeleteEventParticipantsInput {
  id: Int!
}
input DeleteEventPreparationInput {
  id: Int!
}
input DeleteEventPreparationsInput {
  id: Int!
}
input DeleteEventTransportDetailsInput {
  id: Int!
}
input DeleteFacilityInput {
  place_name: String!
}
input DeleteLevelInput {
  ID: Int!
}
input DeleteManyEventParticipantsInput {
  ids: [Int]!
}
input DeleteManyEventPreparationsInput {
  ids: [Int]!
}
input DeleteManyEventTransportDetailsInput {
  ids: [Int]!
}
input DeleteManyWorkerInput {
  ids: [Int]!
}
input DeleteOfficialDutiesInput {
  ids: [Int!]!
}
input DeleteOfficialInput {
  id: Int!
}
input DeleteOfficialPoolInput {
  ids: [String]!
}
input DeleteOpponentInput {
  SchoolCode: String!
}
input DeletePreparationInput {
  id: Int!
}
input DeleteSchoolInfoInput {
  id: String!
}
input DeleteVehicleInput {
  id: Int!
}
input DeleteWorkerInput {
  id: Int!
}
input DeleteWorkerPoolInput {
  ids: [Int]!
}
input DonorDonateIntentCreateInput {
  amount: Int!
  "This is preference of the donors to be anonymous"
  anonymous: Boolean
  "This is a session browser secret"
  browserSecret: String
  "Flag to indicate if the Credit card fee is covered by the donor"
  ccFeeCovered: Boolean
  ccFeePercentage: Float
  "This is the Donation Level ID that will be passed thru the URL on Checkout."
  donationLevelId: String
  donorEmail: String
  donorFirstName: String
  donorLastName: String
  errorType: String
  fundraiserId: String!
  groupLeaderId: String
  matchDon: String
  "This is the IP address of the donor"
  originIp: String
  participantId: Int
  participantUserDirectoryId: String
  "Share Type of donation will be passed as a search query to the URL on Checkout. Valid values can be found at https://www.notion.so/snap-mobile/508f9d741b434cef85cb2c5cbdf15eb7?v=804cce5f998a415b97d672037112b4ae&pvs=4 under the New Stack Share Types column"
  shareType: String
  tipAmount: Int
}
input DonorDonateIntentUpdateInput {
  amount: Int
  "This is preference of the donors to be anonymous"
  anonymous: Boolean
  "Flag to indicate if the Credit card fee is covered by the donor"
  ccFeeCovered: Boolean
  ccFeePercentage: Float
  cheerwallName: String
  "This is the Donation Level ID that will be passed thru the URL on Checkout."
  donationLevelId: String
  donorEmail: String
  donorFirstName: String
  donorLastName: String
  donorMessage: String
  errorType: String
  groupLeaderId: String
  matchDon: String
  participantId: Int
  participantUserDirectoryId: String
  paymentMethodId: String
  paymentProvider: PaymentProvider
  tipAmount: Int
}
input DonorDonationOfflineInput {
  "The amount of the donation in cents"
  amount: Int!
  anonymous: Boolean
  donorName: String
  email: String
  fundraiserId: String!
  participantId: Int
  participantUserDirectoryId: String
}
input DonorDonationUpdateInput {
  anonymous: Boolean
  cheerwallName: String
  donorId: ID
  donorMessage: String
  donorName: String
  participantId: Int
  participantUserDirectoryId: String
  status: DonorDonationStatus
  subtotalCents: Int
}
input DonorFundraiserPayableRefundInput {
  "The id of the donation or purchase"
  id: ID!
  "Optional: The type of fundraiser payable. If not provided, the type will be determined by the id."
  type: DonorFundraiserPayableTypeEnum
}
input DonorFundraiserPayablesInput {
  fundraiserId: String
  fundraiserRaiseId: Int
}
input DonorsDonationStatsWhereInput {
  fundraiserId: ID
  fundraiserIds: [ID!]
  groupLeaderId: String
  participantId: Int
  shareType: [String!]
}
input DonorsOrgsSearchInput {
  name: String
  payableId: String!
  stateCode: String!
}
input DonorsOrgsSearchPagination {
  limit: Int
  offset: Int
}
input DriveCampaignUpdateInput {
  notes: String
}
input DriveDateCompareInput {
  equals: String
  gt: String
  gte: String
  lt: String
  lte: String
}
input DriveEventActivityInput {
  featured: Boolean
  message: String
  parentId: String
}
input DriveEventAgendaInput {
  description: String
  endTime: String
  location: String
  startTime: String
  title: String
}
input DriveEventAttendeeInput {
  department: String
  flightRequest: EventAttendeeRequestStatus
  hotelRequest: EventAttendeeRequestStatus
  userId: ID
}
input DriveEventBoothInput {
  boothNotes: String
  breakdownEndTime: String
  breakdownStartTime: String
  electricityProvided: Boolean
  flooringProvided: Boolean
  internetProvided: Boolean
  setupEndTime: String
  setupStartTime: String
}
input DriveEventInput {
  activityTypes: [String!]
  agenda: [DriveEventAgendaInput!]
  agendaConfig: JSON
  attendees: [DriveEventAttendeeInput!]
  booth: DriveEventBoothInput
  clinicLeaderId: String
  description: String
  endDate: String
  hotelAddress: String
  hotelName: String
  hubspotTrackingLink: String
  notes: String
  planningCompleted: Boolean
  preferedTimezone: String
  sponsor: DriveEventSponsorInput
  startDate: String
  status: DriveEventStatus
  tier: Int
  title: String
  venue: DriveEventVenueInput
  websiteLink: String
}
input DriveEventSearchInput {
  activityTypes: [String!]
  attendees: [String!]
  boothOptions: DriveEventBoothInput
  dateRange: [String]
  endDate: DriveDateCompareInput
  eventStatus: EventDateStatus
  id: ID
  sponsor: DriveEventSponsorInput
  startDate: DriveDateCompareInput
  states: [String!]
  statuses: [DriveEventStatus!]
  tiers: [Int!]
  title: String
}
input DriveEventSponsorInput {
  doorPrized: Boolean
  doorPrizedRequired: Boolean
  sponsorDescription: String
}
input DriveEventVenueInput {
  buildingOrRoom: String
  city: String
  id: ID
  name: String
  state: String
}
input DriveFieldInput {
  label: String
  linkText: String
  name: String!
  type: DriveFieldType!
  value: String!
}
input DriveFileInput {
  content: String!
  description: String
  name: String!
}
input DriveOrgUserTrackingSearchInput {
  trackingIdStartsWith: String
  trackingType: String
}
"Input use for create schedule for preload email"
input DriveScheduleInput {
  "Date to send out. Valid format is YYYY-MM-DD"
  date: String!
  "Timeframe of schedule release."
  timeframe: DriveScheduleTimeFrame!
  "Timezone of datetime to send out. e.g. American/New_York"
  timezone: String
}
input DriveScheduleUpdateInput {
  "Date to send out. Valid format is YYYY-MM-DD"
  date: String
  "Timeframe of schedule release."
  timeframe: DriveScheduleTimeFrame
  "Timezone of datetime to send out. e.g. American/New_York"
  timezone: String
}
input DriveTrackingInput {
  trackingId: String!
  trackingType: String
  trackingValue: String!
  trackingValueType: String
}
input DriveUserSearchInput {
  isInternal: Boolean
  searchTerm: String
  userId: String
}
input DriveVenueSearchInput {
  id: ID
  name: String
  state: String
}
input EasypostEventInput {
  eventId: String!
  trackingNumber: String!
  trackerId: String!
  shipmentId: String!
  referenceId: String!
  status: String!
  carrier: String!
  estDeliveryDate: DateTime
  statusDetail: String
  orderId: String!
  trackingUrl: String
  vendor: Vendor!
}
input EventCalendarFiltersInput {
  activity: String
  activityName: [String]
  endDate: Date
  facilities: [String]
  gender: String
  group: String
  homeAway: String
  levels: [String]
  opponents: [String]
  startDate: Date
  teams: [String]
  vehicleType: String
}
input EventDataInput {
  equals: String
  path: [String]
}
input EventFilter {
  dataFilter: [EventDataInput]
  endDatetime: DateTime
  eventType: EventType
  location: String
  startDatetime: DateTime
}
input EventOfficialReportFilter {
  event_date: Date!
}
input EventParticipantsInput {
  contract_received: String
  event_id: Int
  id: Int!
  notes: String
  paid: String
  school_code: String
}
input EventPreparationsInput {
  comments: String
  event: Int
  id: Int
  prep: String
  qty: String
}
input EventResultInput {
  data: JSON
}
input EventTransportDetailsInput {
  driver_name: String
  driver_phone: String
  event_id: Int
  id: Int!
  vehicle_id: String
  vehicle_type: String
}
"Owned by Vault Only used in legacy createCard"
input FinancialAddressInput {
  city: String
  line1: String
  line2: String
  state: String
  zip: String
}
"Filtering options for nullable Float type"
input FloatNullableFilter {
  "Equals operation"
  equals: Float
  "Greater Than operation"
  gt: Float
  "Greater Than or Equal operation"
  gte: Float
  "In operation for multiple values"
  in: [Float!]
  "Less Than operation"
  lt: Float
  "Less Than or Equal operation"
  lte: Float
  "Not Equal operation"
  not: Float
  "Not In operation for multiple values"
  notIn: [Float!]
}
input FundraiserApprovalSubmissionDocDataInput {
  filename: NonEmptyString!
  filetype: NonEmptyString!
  id: Int
  location: NonEmptyString!
}
input FundraiserApprovalSubmissionDocumentInput {
  action: FundraiserApprovalSubmissionDocumentInputAction!
  document: FundraiserApprovalSubmissionDocDataInput!
}
input FundraiserApproverDataInput {
  approverType: FundraiserApproverType
  email: String
  firstName: String
  id: Int
  lastName: String
  phone: String
  roleId: String
  userId: String
}
input FundraiserApproverInput {
  action: FundraiserApproverInputAction!
  approver: FundraiserApproverDataInput!
}
input FundraiserParticipantUpdateInput {
  lastLoginAt: Date
}
input FundraiserReleaseSmsInvitesScheduleInput {
  """
  Accept values: "2025-05-01", "2025-05-02", etc.
  """
  date: String!
  """
  Accept values: "MORNING", "AFTERNOON", "EVENING".
  """
  timeframe: FundraiserSmsInvitesReleaseTimeframe!
  """
  Accept values: "America/New_York", "America/Chicago", "America/Los_Angeles", etc.
  """
  timezone: String!
}
input FundraiserSetupProgressInput {
  currentStep: Int!
  id: ID!
  section: String
  status: FundraiserSetupProgressStatus!
}
input FundraisersDonationInviteUpdateInput {
  status: DonationInviteStatus
}
input FundraisersGroupLeadersWhereInput {
  isPrimary: Boolean
}
input GetCompetitionListInput {
  from_date: String
  opponents: [String]
  to_date: String
}
input GetDailyCalendarBusScheduleByDateInput {
  from_date: String
  to_date: String
}
input GetDailyCalendarEventsByDateInput {
  from_date: String
  group: String
  to_date: String
}
input GetDailyCalendarOfficialsByDateInput {
  from_date: String
  to_date: String
}
input GetDailyCalendarPreparationsByDateInput {
  from_date: String
  to_date: String
}
input GetDailyCalendarWorkersByDateInput {
  from_date: String
  to_date: String
}
input GetEventByIdInput {
  event_id: Int!
}
input GetEventParticipantsByIdInput {
  id: Int!
}
input GetEventPreparationsByEventIdInput {
  event_id: Int!
}
input GetEventPreparationsByIdInput {
  id: Int!
}
input GetEventTransportDetailsByEventOrDatesInput {
  event_date_end: Date
  event_date_start: Date
  event_id: Int
}
input GetEventsFilteredByOpponentStartAndEndDate {
  endDate: String!
  opponent: String!
  startDate: String!
}
input GetFacilityByPlaceNameInput {
  place_name: String!
}
input GetLevelByIdInput {
  ID: Int!
}
input GetOfficialAssignmentsInput {
  from_date: String
  gender: String
  levels: [String]
  official_name: String
  teams: [String]
  to_date: String
}
input GetOfficialByIdInput {
  id: Int!
}
input GetOfficialDutyByIdInput {
  id: Int!
}
input GetOfficialPoolByIdInput {
  ID: String
}
input GetOpponentBySchoolCodeInput {
  SchoolCode: String!
}
input GetPreparationByIdInput {
  id: Int!
}
input GetSchoolInfoByIdInput {
  id: String!
}
input GetVehicleByIdInput {
  id: Int!
}
input GetWorkerByIdInput {
  id: Int!
}
input GetWorkerPoolByIdInput {
  ID: Int!
}
input GetWorkersByEventIdInput {
  event_id: Int!
}
input GiftShopIntentInput {
  donateIntentId: String!
  fundraiserId: Int!
  fundraiserUserId: Int!
  participantUserDirectoryId: String!
  purchaseIntentId: String
  status: PurchaseIntentStatus!
  userId: Int!
}
input GiftShopIntentUpdateInput {
  donorEmail: String
  donorFirstName: String
  donorLastName: String
  fundraiserUserId: Int
  participantUserDirectoryId: String
  paymentProvider: String
  userId: Int
}
input GiftShopPurchaseItemUpdate {
  id: String!
  size: String!
}
input GroupLeaderBlockedDomainsUpdateInput {
  add: [String!]
  remove: [String!]
}
input GroupLeaderDonationInviteContactInput {
  contact: String!
  inviteType: GroupLeaderDonationInviteType!
}
input GroupLeaderDonationInviteContactUpdateInput {
  contactInput: GroupLeaderDonationInviteContactInput!
  id: ID!
}
input GroupLeaderDonationLevelsInput {
  id: ID!
  impact: String
  rewardDescription: String
}
input GroupLeaderEditFundraiserFinancialsInput {
  id: ID
  kycSubmitted: Boolean
  payoutSubmitted: Boolean
  settlementMethod: FundraiserSettlementMethod
  setupCompleted: Boolean
}
input GroupLeaderEditFundraiserInput {
  earlyStartDateRequested: Boolean
  howDonationsAreUsed: String
  "will always be logo.${extension}, the full url will be constructed in the resolver"
  logo: String
  name: String
  personalMessage: String
  primaryColor: String
  secondaryColor: String
  "pass when group leader completes a setup step"
  setupCompleted: Boolean
  whyDonations: String
}
input GroupLeaderEmailInput {
  emailBody: String!
  emailSubject: String!
  emailType: GroupLeaderEmailType!
}
input GroupLeaderFundraiserAcknowledgementSignInput {
  agreedToTerms: Boolean!
  id: ID!
  setupCompleted: Boolean
  signedAt: Date!
  typedSignature: String!
}
input GroupLeaderFundraiserAddressInput {
  attentionTo: String
  city: String!
  country: String!
  id: ID
  region: String!
  setupCompleted: Boolean
  streetLine1: String!
  streetLine2: String
  zip: String!
}
input GroupLeaderFundraiserManagerInput {
  lastLoginAt: Date
  role: FundraiserManagersRole
}
input GroupLeaderFundraiserManagerUpdateInput {
  id: ID!
  update: GroupLeaderFundraiserManagerInput
}
input GroupLeaderFundraiserMediaGalleryUpdateInput {
  add: [GroupLeaderMediaGalleryInput!]
  remove: [ID!]
  update: GroupLeaderMediaGalleryUpdateInput
}
input GroupLeaderFundraiserParticipantUpdateInput {
  id: ID!
  participantGroupId: String
}
input GroupLeaderGearLogoUpdateInput {
  approvedAt: Date
  changeRequestDescription: String
  changesRequestedAt: Date
  setupCompleted: Boolean
}
input GroupLeaderMediaGalleryInput {
  fundraiserId: ID!
  id: ID
  image: String
  mediaType: FundraiserMediaType!
  position: Int
  url: String
}
input GroupLeaderMediaGalleryUpdateInput {
  items: [GroupLeaderMediaGalleryInput!]
  setupCompleted: Boolean
}
input GroupLeaderParticipantsUpdateInput {
  delete: [ID!]
  update: [GroupLeaderFundraiserParticipantUpdateInput!]
}
input HelpDocumentDeleteInput {
  key: String!
}
input HelpDocumentUploadInput {
  base64File: String!
}
input IOSNotificationInput {
  badge: Int
  category: String
  sound: String
  threadId: String
}
input ImageInput {
  url: String!
}
input InsAddPreApprovedContactApproverInput {
  email: String
  firstName: String
  id: String
  lastName: String
  phone: String
}
input InsAddPreApprovedContactOrgInput {
  id: String
  modId: String
  name: String
  raiseId: Int
  zipcode: String
}
input InsEditPreApprovedContactInput {
  activity: String
  email: String
  firstName: String
  lastName: String
  phoneNumber: String
}
input InsOrgCampaignSummaryInput {
  campaignIds: [Int]
  orgId: String
}
input InsPreApprovedContactInput {
  activity: String!
  email: String!
  firstName: String!
  lastName: String!
  phoneNumber: String
}
input InsSetNotificationPreferences {
  notificationIds: [InsUserNotificationType]
  organizationIds: [String]
  setTo: InsSetNotificationPreferenceFlag!
  type: InsSetNotificationType!
}
"Filtering options for Int type"
input IntFilter {
  "Equals operation"
  equals: Int
  "Greater Than operation"
  gt: Int
  "Greater Than or Equal operation"
  gte: Int
  "In operation for multiple values"
  in: [Int!]
  "Less Than operation"
  lt: Int
  "Less Than or Equal operation"
  lte: Int
  "Not Equal operation"
  not: Int
  "Not In operation for multiple values"
  notIn: [Int!]
}
"Filtering options for nullable Int type"
input IntNullableFilter {
  "Equals operation"
  equals: Int
  "Greater Than operation"
  gt: Int
  "Greater Than or Equal operation"
  gte: Int
  "In operation for multiple values"
  in: [Int!]
  "Less Than operation"
  lt: Int
  "Less Than or Equal operation"
  lte: Int
  "Not Equal operation"
  not: Int
  "Not In operation for multiple values"
  notIn: [Int!]
}
input InviteFilter {
  objectId: String!
  type: InviteType!
}
input InviteMemberContactInput {
  contact: String!
  firstName: String
  lastName: String
  meta: JSON
  method: ContactMethodEnum!
  role: ChannelRolesEnum!
}
input InviteMemberUsersInput {
  id: ID!
  role: ChannelRolesEnum!
}
input InviteParentArguments {
  joincode: String
}
input InviteUserArguments {
  apps: [UserApps]
  consumer: UserApps
  fundraiserId: Int
  occupation: UserOccupation
  orgsAffiliation: [UserOrgAffiliationPayload!]
  permissionIds: [String]
  phoneNumber: String
  redirectUrl: String
  roleIds: [String]
}
input InviteUserOrgsArguments {
  orgId: String
  product: UserApps
  roleId: String
}
"Owned by Vault Only used in legacy createCard"
input IssueCardInput {
  altName: String
  cardType: CARD_TYPE!
  metadata: VaultCardMetadataInput
  recipientName: String
  shippingAddress: FinancialAddressInput
  shippingService: SHIPPING_SERVICE
  spendingLimitAmount: Int!
  spendingLimitInterval: SPENDING_LIMIT_INTERVAL!
}
input JoinFundraiserInviteUpdateInput {
  invites: [ID!]!
  updateType: JoinFundraiserInviteUpdateType!
}
input JoinFundraiserUserInput {
  email: String!
  firstName: String!
  lastName: String!
  phoneNumber: String
}
input LegacyTransactionsInput {
  accountId: String
  limit: Int
  offset: Int
  status: String
}
input MagentoConsumerPointsLiabilityInput {
  comment: String
  createdAt: DateTime
  customerBalanceId: Int
  customerId: Int!
  customerLiability: Float
  entityId: Int
  eventCode: String
  eventData: JSON
  expirationDate: DateTime
  expirationPeriod: Int
  isExpired: Boolean
  isNeedSendNotification: Boolean
  isNotificationSent: Boolean
  netsuiteExternalId: String
  netsuiteInternalId: Int
  orderId: Int
  organizationId: Int
  parentNetsuiteExternalId: String
  parentNetsuiteInternalId: Int
  parentTransactionId: Int
  parentTransactionType: String
  paymentEntityId: Int
  paymentMethod: PaymentMethod
  pointsBalance: Float
  pointsDelta: Float!
  pointsToUse: Float
  receiverId: Int
  ruleId: Int
  senderId: Int
  storeId: Int
  transactionId: Int!
  transactionParentGroup: String
  transactionType: String!
  websiteId: Int
}
input MagentoRefundInput {
  amount: Float!
  netsuiteOrderId: Int!
  orderId: Int!
  status: String!
}
input ManageCoachListFilter {
  orderBy: ManageCoachListOrderBy
  skip: Int = 0
  take: Int
  where: ManageCoachListWhere
}
input ManageCoachListOrderBy {
  address: SortOrderEnum
  city: SortOrderEnum
  firstName: SortOrderEnum
  lastName: SortOrderEnum
  postalCode: SortOrderEnum
}
input ManageCoachListWhere {
  adId: Int
  coachId: Int
  createdAt: Date
  createdBy: Int
  firstName: String
  headCoach: Boolean
  isApproved: Boolean
  lastName: String
  photoId: Int
  schoolId: Int
  seasonId: Int
  summary: String
  title: String
  updatedAt: Date
  updatedBy: Int
}
input ManageEventListFilter {
  orderBy: ManageEventListOrderBy
  skip: Int = 0
  take: Int = 10
  where: ManageEventListWhere
}
input ManageEventListOrderBy {
  confirmed: String
  opponent: String
}
input ManageEventListWhere {
  confirmed: String
  month: Int
  opponent: String
  year: Int
}
input ManageOrganizationFilter {
  orderBy: ManageOrganizationOrderBy
  skip: Int
  take: Int
  where: ManageOrganizationWhere
}
input ManageOrganizationOrderBy {
  name: SortOrderEnum
  state: SortOrderEnum
}
input ManageOrganizationWhere {
  isDeleted: Boolean
  name: String
  show: Int
  state: String
}
input ManagePlayerListFilter {
  orderBy: ManagePlayerListOrderBy
  skip: Int = 0
  take: Int
  where: ManagePlayerListWhere
}
input ManagePlayerListOrderBy {
  city: SortOrderEnum
  dob: SortOrderEnum
  fName: SortOrderEnum
  gender: SortOrderEnum
  gradYear: SortOrderEnum
  height: SortOrderEnum
  jersey: SortOrderEnum
  lName: SortOrderEnum
  permission: SortOrderEnum
  position: SortOrderEnum
  rosterId: SortOrderEnum
  schoolId: SortOrderEnum
  seasonId: SortOrderEnum
  siteStudentId: SortOrderEnum
  state: SortOrderEnum
  status: SortOrderEnum
  weight: SortOrderEnum
  zip: SortOrderEnum
}
input ManagePlayerListWhere {
  ch1: Int
  ch2: Int
  ch3: Int
  city: String
  custodyCode: Int
  dayPhone: String
  dob: Date
  fName: String
  feePaid: Int
  gender: String
  gradYear: String
  hatsize: String
  height: String
  homePhone: String
  hospitalPhone: String
  insuranceCompany: String
  insurancePolicyNum: String
  jersey: String
  lName: String
  livesWithCode: Int
  noPress: Int
  permission: Int
  physical: Int
  physicalDate: Date
  physician: String
  physicianTelephone: String
  position: String
  preferredHospital: String
  rosterId: Int
  schoolId: Int
  seasonId: Int
  shirtsize: String
  shortsize: String
  siteStudentId: Int
  stAddress: String
  state: String
  status: String
  studentId: String
  weight: String
  zip: String
}
input ManageProgramListFilter {
  orderBy: ManageProgramListOrderBy
  skip: Int = 0
  take: Int
  where: ManageProgramListWhere
}
input ManageProgramListOrderBy {
  gender: OrderType
  level1: OrderType
  sportName: OrderType
}
input ManageProgramListWhere {
  gender: String
  groupVal: String
  level1: String
  sportName: String
}
input ManageResourceAnnouncementFilter {
  orderBy: ManageResourceAnnouncementOrderBy
  skip: Int
  take: Int
  where: ManageResourceAnnouncementWhere
}
input ManageResourceAnnouncementOrderBy {
  endDate: SortOrderEnum
  id: SortOrderEnum
  sortVal: SortOrderEnum
  startDate: SortOrderEnum
  title: SortOrderEnum
}
input ManageResourceAnnouncementWhere {
  id: Int
  onFront: Int
  title: String
}
input ManageResourceEventLocationFilter {
  orderBy: ManageResourceEventLocationOrderBy
  skip: Int
  take: Int
  where: ManageResourceEventLocationWhere
}
input ManageResourceEventLocationOrderBy {
  fileName: SortOrderEnum
  name: SortOrderEnum
}
input ManageResourceEventLocationWhere {
  fileName: PagesNameEnum
  name: SortOrderEnum
}
input ManageResourcePagesFilter {
  orderBy: ManageResourcePagesOrderBy
  skip: Int
  take: Int
  where: ManageResourcePagesWhere
}
input ManageResourcePagesOrderBy {
  name: SortOrderEnum
  pageTitle: SortOrderEnum
}
input ManageResourcePagesWhere {
  name: PagesNameEnum
}
input ManageResourcePhotosFilter {
  orderBy: ManageResourcePhotosOrderBy
  skip: Int = 0
  take: Int
  where: ManageResourcePhotosWhere
}
input ManageResourcePhotosOrderBy {
  caption: SortOrderEnum
  filename: SortOrderEnum
  id: SortOrderEnum
  title: SortOrderEnum
}
input ManageResourcePhotosWhere {
  group: String
  sport: String
}
input ManageSeasonListFilter {
  orderBy: ManageSeasonListOrderBy
  skip: Int = 0
  take: Int
  where: ManageSeasonListWhere
}
input ManageSeasonListOrderBy {
  sport_code: OrderType
  year: OrderType
}
input ManageSeasonListWhere {
  sport_code: String
  year: String
}
input ManageUserCreateInput {
  contracts: String!
  events: String!
  groups: [String!]
  maintenance: String!
  password: String!
  seasons: String!
  teams: String!
  user_email: String!
  user_id: String!
  user_level: String!
}
input ManageUserDeleteInput {
  id: Int!
}
input ManageUserInput {
  id: Int!
}
input ManageUserUpdateInput {
  contracts: String
  events: String
  groups: [String!]
  id: Int!
  maintenance: String
  password: String
  seasons: String
  teams: String
  user_email: String
  user_id: String
  user_level: String
}
input ManageUsersListOptions {
  keyword: String
  limit: Int
  offset: Int
}
input MarcoEventInput {
  address: MarcoShippingAddress
  created_at: String
  id: Int!
  purchase_order: String!
  received_at: String
  shipments: [MarcoShipment]
  shipped_at: String
  status: MarcoOrderStatus
}
input MarcoOrderItem {
  client_item_num: String
  customer_sku: String
  description: String
  id: Int!
  quantity: Int!
  sku_id: Int
  workflow_state: MarcoItemWorkflowState
}
input MarcoOrderStatusNote {
  created_at: String!
  note: String!
}
input MarcoServiceRequest {
  created_at: String
  items_count: Int
  reason: String
  workflow_state: MacroServiceRequestWorkflowState
}
input MarcoShipment {
  delivered: Boolean
  search_link: String
  shipping_carrier: ShippingProvider
  shipping_method: String
  tracking_number: String
}
input MarcoShippingAddress {
  ship_to_address: String
  ship_to_address_2: String
  ship_to_city: String
  ship_to_company_name: String
  ship_to_country: String
  ship_to_email: String
  ship_to_first_name: String
  ship_to_last_name: String
  ship_to_state: String
  ship_to_telephone: String
  ship_to_zip_code: String
}
input MessageContact {
  attributes: JSON
  to: String!
}
input MessagesChannelsFilterInput {
  orgId: String!
}
input MessagesMyChannelsFilterInput {
  orgId: String!
}
input ModifyEventInput {
  activity: String
  author: String
  bus_fee: Float
  bus_time: String
  cancellation_status: String
  comments: String
  conference: String
  conference_event_id: Int
  conference_id: Int
  confirmed: String
  contract: String
  created_at: Date
  departure_location: String
  directions: String
  early_dismissal_required: String
  early_dismissal_time: String
  end_time: String
  estimated_return_time: String
  event_date: Date
  event_id: Int!
  exists_in_mls: Int
  fee: Float
  g_s: String
  gate_revenue: Float
  headline: String
  impact_event: String
  lead: String
  location: String
  loss_points: Int
  num_buses: Int
  opponent: String
  opponent_code: String
  opponent_score: String
  picture: String
  place: String
  prep_setup: String
  promote: String
  results: String
  revenue: Float
  rollover: String
  season_team: Int
  start_time: String
  team_score: String
  title: String
  tournament: String
  trans_id: Int
  transport_comments: String
  transportation: String
  update_at: Date
  web_dir: String
  win_points: Int
  years: String
}
input ModifyEventParticipantsInput {
  contract_received: String
  event_id: Int
  id: Int!
  notes: String
  paid: String
  school_code: String
}
input ModifyEventPreparationInput {
  comments: String
  event: Int
  id: Int!
  prep: String
  qty: String
}
input ModifyEventPreparationsInput {
  comments: String
  event: Int
  id: Int!
  prep: String
  qty: String
}
input ModifyEventTransportDetailsInput {
  driver_name: String
  driver_phone: String
  event_id: Int
  id: Int!
  vehicle_id: String
  vehicle_type: String
}
input ModifyFacilityInput {
  Address1: String
  Address2: String
  City: String
  State: String
  ZipCode: String
  directions: String
  indoor: String
  ml_site_id: String
  ml_space: String
  ml_space_id: String
  picture: String
  place_name: String!
  place_name_old: String!
  show: String
  web: String
}
input ModifyLevelInput {
  ID: Int!
  Level: String
  is_deleted: Boolean
}
input ModifyManyEventPreparationsByEventInput {
  comments: String
  event: Int!
  prep: String
  qty: String
}
input ModifyOfficialDutyInput {
  duty: String
  id: Int!
  is_deleted: Boolean
}
input ModifyOfficialInput {
  duty: String
  event_id: Int
  id: Int!
  offic_id: String
  organization: String
  paid: String
  pay_code: String
  received: String
  salary: Float
  ssn: String
  voucher_number: String
  worker_name: String
}
input ModifyOfficialPoolByIdInput {
  Address: String
  City: String
  First: String
  Home_Phone: String
  ID: String!
  ID_old: String!
  Last: String
  SSN: String
  State: String
  Work_Phone: String
  Zip: String
  cell_phone: String
  email: String
  is_deleted: Boolean
  vendor_number: String
}
input ModifyOpponentInput {
  Address: String
  Phone: String
  SchoolCode: String!
  SchoolName: String
  State: String
  Zip: String
  ad_name: String
  city: String
  email: String
  fax: String
  is_deleted: Boolean
  nces_id: String
  non_school: Boolean
}
input ModifyPreparationInput {
  duty: String
  id: Int!
  is_deleted: Boolean
  qty: String
}
input ModifySchoolInfoInput {
  Schoolname: String
  ad: String
  ad_contract_signee: String
  address: String
  ccemail: String
  city: String
  conf_text_type: String
  email: String
  email_reminder: String
  email_reminder_officials: String
  email_reminder_workers: String
  enable_cc_email_as_origin: String
  enable_ml_updates: String
  fax: String
  icon: String
  id: String!
  mascot: String
  message_board_read_at: Date
  ml_key: String
  phone: String
  principal: String
  school_timezone: String
  secondary_ad_email: String
  secondary_ad_name: String
  signed_contract_notification: String
  ssn_on_file: String
  state: String
  state_org: String
  state_org_abbreviation: String
  use_security: String
  web_password: String
  zip: String
}
input ModifyVehicleInput {
  id: Int!
  is_deleted: Boolean
  status: String
  vehicle_type: String
}
input ModifyWorkerInput {
  duty: String
  email: String
  event_id: Int
  id: Int!
  organization: String
  paid: String
  pay_code: String
  pay_rate: Float
  salary: Float
  ssn: String
  woker_name: String
  worker_end_time: String
  worker_start_time: String
  worker_type: String
}
input ModifyWorkerPoolInput {
  Address: String
  City: String
  First: String
  Home_Phone: String
  ID: Int!
  Last: String
  SSN: String
  State: String
  Work_Phone: String
  Zip: String
  cell_phone: String
  email: String
  is_deleted: Boolean
  pay_rate: Float
  worker_type: String
}
"NestedBoolFilter is used for nested filtering on boolean fields."
input NestedBoolFilter {
  "Checks if the boolean field is equal to the specified value."
  equals: Boolean
  "Nested filter for negating a condition."
  not: NestedBoolFilter
}
input NetsuiteEventInput {
  eventType: String
  record: JSON
}
input NoticeCreateInput {
  body: String
  isUrgent: Int
  title: String
}
input NoticeDeleteInput {
  ids: [Int]
}
input NoticeModifyInput {
  body: String
  id: Int!
  isUrgent: Int
  title: String
}
input OfficialByIdInput {
  id: Int!
}
input OrderFilterInput {
  limit: Int
  offset: Int
  orderBy: String
  orderBySort: String
  scopeId: Int!
  searchBy: String
  searchValue: String
  status: String
}
input OrderGraphInput {
  interval: Int
  scopeId: Int!
}
input OrderInput {
  baseSubtotal: Float
  billingCity: String
  billingState: String
  billingStreet: String
  billingStreet2: String
  billingZipCode: String
  carrier: ShippingProvider!
  city: String!
  confirmationId: String
  designEps: String
  discountAmount: Float
  fundraiserId: String
  giftCardAmount: Float
  line2: String
  netsuiteId: String
  orderId: String
  packingSlipId: String!
  packingSlipTitle: String!
  paymentMethod: String
  pointAmount: Float
  priority: Priority
  products: [OrderProductInput!]!
  scheduleAt: String
  shipTo: String!
  shipToEmail: String
  shipToPhone: String
  shippingCost: Float
  source: String
  state: String!
  street: String!
  street2: String
  taxAmount: Float
  transactionId: String
  update: Boolean
  vendor: Vendor!
  zipCode: String!
}
input OrderProductInput {
  amount: Float
  color: String
  discountAmount: Float
  logo: String
  magentoItemId: String
  name: String
  netsuiteId: String!
  printAttributes: PrintAttributesInput
  quantity: Int
  receiverName: String!
  sku: String
  taxAmount: Float
  thumbnailUrl: String
}
input OrgAffiliateInput {
  base: BaseOrgInput!
  parentOrgId: ID
  type: OrgType!
}
input OrgBusinessInput {
  base: BaseOrgInput!
}
input OrgClubInput {
  base: BaseOrgInput!
}
input OrgClubUpdateInput {
  base: BaseOrgInput!
  orgId: ID!
}
input OrgDistrictInput {
  base: BaseOrgInput!
  ncesId: String
}
input OrgInviteArguments {
  orgId: String!
  title: String!
}
input OrgInviteInput {
  email: String
  orgId: String!
  phoneNumber: String
  title: String!
}
input OrgProgramInput {
  activity: ProgramActivity!
  orgId: ID!
}
input OrgSchoolInput {
  base: BaseOrgInput!
  ncesId: String
  parentDistrictId: ID
}
input OrgSchoolUpdateInput {
  base: BaseOrgInput!
  ncesId: String = null
  orgId: ID!
}
input OrgStaffRosterUserDeleteInput {
  orgId: ID!
  userId: ID!
}
input OrgTeamInput {
  activity: ProgramActivity!
  name: String!
  orgId: ID!
  teamAge: TeamAge!
  teamGender: TeamGender!
  teamGroupType: TeamGroupType!
  teamSize: Int
  teamTier: TeamTier!
}
input OrgsSearchPaginationInput {
  limit: Int = 25
  offset: Int = 0
}
input OrgsSearchWhereInput {
  "List of organization ids to filter from."
  ids: [ID!]
  "The string to search in name of organization."
  nameIncludes: String
  """
  Organization type. Valid values are "Booster" | "Club" | "District" | "Program" | "School".
  """
  orgTypes: [OrgType!]
  "Allow to search any properties via JSON query. The key will auto convert to snake_case."
  properties: JSON
}
input ParentInviteInput {
  email: String
  joincode: String!
  phoneNumber: String
}
input ParticipantCampaignConfigInput {
  autoImportEmails: ParticipantCampaignConfigStates
  autoImportTexts: ParticipantCampaignConfigStates
  giftShop: ParticipantCampaignConfigStates
  guardianSetup: ParticipantCampaignConfigStates
  profileSetup: ParticipantCampaignConfigStates
  rewards: ParticipantCampaignConfigStates
}
input ParticipantDonationInvitesAction {
  add: [String!]
  remove: [ID!]
}
input ParticipantRewardsInput {
  incentiveId: Int!
  size: String!
  tier: Int!
}
input ParticipantSmsDonationInvitesWhereInput {
  fundraiserId: String
  raiseFundraiserId: Int
  status: ParticipantDonationInviteStatus
}
input PaymentsApiCustomerInput {
  email: String!
  stripeEnv: StripeEnv = RAISE
}
input PaymentsApiDetachPaymentMethodInput {
  paymentMethodId: String!
  stripeEnv: StripeEnv = RAISE
}
input PaymentsApiEntityInput {
  entityId: String!
}
input PaymentsApiPaymentIput {
  amount: Int!
  description: String
  finalDestination: String!
  idempotencyKey: String
  metadata: JSONObject
  paymentMethodId: String!
  snapAmount: Int = 0
  snapId: String!
  stripeEnv: StripeEnv = RAISE
}
input PaymentsApiRefundInput {
  amount: Int
  idempotencyKey: String
  metadata: JSONObject
  refundApplicationFee: Boolean = false
  snapAmount: Int = 0
  stripeEnv: StripeEnv = RAISE
  transactionId: String!
}
input PointsActivityFilterInput {
  limit: Int
  offset: Int
  orderBy: String
  orderBySort: String
  scopeId: Int!
  searchBy: String
  searchValue: ID
  status: String
  type: String
}
input PreparationByIdInput {
  id: Int!
}
input PrintAttributesInput {
  name: PrintValueInput
  number: PrintValueInput
}
input PrintValueInput {
  color: Color
  font: String
  value: String
}
input PrivateChannelCreateInput {
  orgId: String!
}
input ProductInput {
  color: String
  logoPosition: LogoPosition!
  magentoSku: String
  name: String!
  netsuiteId: String
  size: ProductSize!
  sku: String!
}
input Providers {
  email: SupportedEmailProviders
  sms: SupportedSmsProviders
}
input PurchaseItemInput {
  costCents: Int!
  image: String!
  incentiveId: Int!
  name: String!
  netEarnedCents: Float!
  priceCents: Int!
  size: String!
}
input PurchaseParticipantUpdateData {
  fundraiserUserId: Int!
  participantUserDirectoryId: String!
}
input PurchaseUpdateInput {
  "Update donor full name"
  donorName: String
  "Update purchased gift shop item sizes"
  lineItems: [GiftShopPurchaseItemUpdate!]
  "Update with different Participant Data"
  participant: PurchaseParticipantUpdateData
}
input RaiseAdminParticipantFilter {
  "Conditions to order by"
  orderBy: RaiseAdminParticipantOrderInput
  "Conditions to filter by"
  where: RaiseAdminParticipantWhereInput
}
input RaiseAdminParticipantOrderInput {
  email: SortOrderEnum
  id: SortOrderEnum
  name: SortOrderEnum
}
input RaiseAdminParticipantWhereInput {
  "Nullable String Filter for Participant email"
  email: String
  "Nullable String Filter for Participant name"
  name: String
  "Nullable Boolean Filter for Participant Rollover"
  rollover: Boolean
}
input RaiseFundraiserUsersCreateManyInput {
  fundraiser_id: Int!
  user_ids: [Int!]!
}
input ReconcileBudget {
  amount: Int!
  budgetItemId: String!
  invoiceId: String
}
input ReconcileInvoice {
  amount: Int!
  budgetItemId: String
  invoiceId: String!
}
input ReconcileInvoiceWithBudget {
  amount: Int!
  budgetItemId: String!
  invoiceId: String!
  note: String
}
"Attributes for finding a roster"
input RegistrationRosterInput {
  gender: String!
  level: String!
  orgsId: ID!
  season: String
  sportName: String!
}
input RoleArguments {
  description: String
  name: String!
  scope: String
  title: String!
}
input RosterCloneInput {
  activity: ProgramActivity = null
  audience: Audience = null
  description: String = null
  gender: TeamGender = null
  name: String!
  orgId: ID!
  product: RosterCreator!
  rosterId: ID!
  startYear: Int!
  tier: TeamTier = null
}
input RosterCreateInput {
  activity: ProgramActivity = null
  audience: Audience = null
  description: String = null
  gender: TeamGender = null
  members: [RosterUserInput!]! = []
  name: String!
  orgId: ID!
  product: RosterCreator!
  startYear: Int!
  tier: TeamTier = null
}
input RosterUpdateInput {
  activity: ProgramActivity = null
  description: String = null
  gender: TeamGender = null
  members: [RosterUserInput!]! = []
  name: String = null
  rosterId: ID!
  startYear: Int = null
}
input RosterUserInput {
  id: ID!
  isAdmin: Boolean
  isDependent: Boolean
  isGuardian: Boolean
}
input ScheduleEventInput {
  data: JSON
  endDatetime: DateTime
  eventType: EventType!
  isArchived: Boolean
  location: String
  orgsId: String!
  programId: String
  startDatetime: DateTime!
}
input SeasonByIdInput {
  season_id: Int!
}
input SeasonByYearTeamInput {
  sport_code: String
  year: String
}
input SeasonCreateInput {
  budget: Float
  created_at: Date
  default_time_for_event: String
  default_time_for_practice: String
  home_field: String
  is_deleted: Boolean
  preview: String
  season: String
  season_id: Int!
  sport_code: String
  web_password: String
  year: String
}
input SeasonDeleteInput {
  season_ids: [Int]
}
input SeasonEventWizardInput {
  season_ids: [Int]
}
input SeasonModifyInput {
  budget: Float
  created_at: Date
  default_time_for_event: String
  default_time_for_practice: String
  home_field: String
  is_deleted: Boolean
  preview: String
  season: String
  season_id: Int!
  sport_code: String
  web_password: String
  year: String
}
input SeasonPostponeUnpostponeInput {
  event_ids: [Int]
  season_id: Int
}
input SeasonScheduleTeamsCreateInput {
  sport_codes: [String]
  year: String
}
input SendNotificationInput {
  android: AndroidNotificationInput
  appIcon: String!
  ios: IOSNotificationInput
  message: String!
  title: String!
  userId: String!
  web: WebNotificationInput
}
input ShipmentInput {
  campaignId: Int
  externalId: String
}
input SnapMobileOneAccessInput {
  accessLevel: Int!
  orgId: ID!
}
input SnapMobileOneContactSupportInput {
  description: NonEmptyString!
  details: NonEmptyString!
  inquiryType: ContactSupportInquiryType!
  orgId: ID!
}
input SnapMobileOneProductInput {
  orgId: ID!
  products: [SnapMobileOneProduct!]!
}
input SpendAccountBearerToken {
  verificationCode: String
  verificationToken: String
}
input SpendAccountTransferInput {
  amount: Int!
  description: String!
  fromGroupIdOrOrgId: String!
  toGroupIdOrOrgId: String!
}
input SpendAchPaymentCancelInput {
  externalId: String
  id: String!
}
input SpendAchPaymentInput {
  authorizedAt: String!
  description: String
  externalId: String!
  groupId: String!
  invoiceIds: [String]!
  paymentAmount: Int!
  paymentMethodId: String
}
input SpendAddressInput {
  city: String!
  state: String!
  street: String!
  street2: String
  zip: String!
}
input SpendAdminBulkAddUnitInput {
  customerId: String!
  users: [AdminUser!]!
}
input SpendAppliedInvoices {
  amount: Int!
  invoiceId: String!
}
input SpendApprovalUpdateInput {
  id: String!
  reason: String
  status: String!
}
input SpendArchiveSeasonMembersInput {
  groupRosterIds: [String]!
  isArchived: Boolean!
}
input SpendAutoPayInput {
  cardId: String
  counterpartyId: String
  groupRosterId: String!
  invoiceIds: [String!]!
  paymentMethodSource: String!
}
input SpendBudgetInput {
  categoryId: String!
  description: String!
  isDefault: Boolean
  seasonId: String
  targetAmount: Int!
  targetDateAt: String!
  vaultId: String
}
input SpendBudgetTransactionInput {
  transactionIds: [String]!
}
input SpendCategoryInput {
  isDefault: Boolean
  isHidden: Boolean
  name: String
  type: CategoryTypeEnum
}
input SpendCheckImageAllowance {
  checkId: String!
  checkType: SpendCheckImageType!
}
input SpendCheckImageRequest {
  id: String!
  side: SpendCheckImageSide!
}
input SpendCheckInput {
  address: SpendAddressInput!
  amount: Int!
  attachmentKey: String
  description: String!
  name: String!
  note: String
  payeeId: String!
  referenceId: String!
  sendAt: String
}
input SpendCounterpartyCreateInput {
  accountNumber: String!
  accountType: String!
  einNumber: String
  entityType: String!
  groupOrOrgId: String!
  name: String!
  routingNumber: String!
}
input SpendCreditMemoInput {
  creditAmount: Int!
  dateToApply: String!
  invoicesApplied: [SpendAppliedInvoices!]!
  note: String
}
input SpendCreditMemoUpdateInput {
  creditApplied: Int!
  dateToApply: String!
  id: String!
  invoiceId: String!
  isArchived: Boolean!
  note: String
}
input SpendDebitCardActionsInput {
  action: DebitCardActions!
  cardId: String!
}
input SpendDebitCardInput {
  dateOfBirth: String!
  shipping: SpendDebitCardShippingInput
  userId: String!
}
input SpendDebitCardShippingInput {
  city: String!
  postalCode: String!
  state: String!
  street: String!
  street2: String
}
input SpendExportEmailsInput {
  emails: [String]!
}
input SpendGetApprovalInput {
  filter: ApprovalFilterInput
  groupId: String
  pagination: SpendPaginationInput
  sort: SpendSortInput
  tab: String!
}
input SpendGroupACHCredit {
  amount: Int!
  counterpartyId: String!
  counterpartyName: String!
  groupId: String!
  note: String!
}
input SpendGroupCheckDepositTagsInput {
  checkDepositId: String!
  groupId: String
}
input SpendGroupExternalTransferInput {
  accountId: String!
  amount: Int!
  direction: ExternalTransferDirectionEnum!
  id: String!
  note: String!
}
input SpendGroupInput {
  discountAmount: Int
  discountCutOffDate: String
  enableDiscount: Boolean!
  hasAccount: Boolean
  isBudgetShared: Boolean
  isLinkEnabled: Boolean!
  isRequireAgreement: Boolean
  minimumDiscountPurchase: Int
  name: String!
  seasonEndAt: String!
  seasonName: String
  seasonStartAt: String!
  shareAccount: Boolean!
}
input SpendGroupRosterInput {
  email: String!
  groupId: String!
  name: String!
  seasonId: String!
}
input SpendGroupRostersInput {
  groupId: String!
  rosters: [SpendRosterUserInput]
  seasonId: String!
}
input SpendGroupRostersWhereInput {
  groupNameIncludes: String
  ids: [String!]
  nameIncludes: String
  status: SpendGroupRosterStatusEnum
}
input SpendGroupsWhereInput {
  "Search archived groups only"
  archived: Boolean
  "Search by group name only"
  groupNameIncludes: String
  "Search by list of group id"
  ids: [String!]
  "Search by group name or latest season name in group"
  nameIncludes: String
  orgId: String
  "Search by payment schedule status of latest season in group. Support multiple values. (OR operation)"
  paymentScheduleStatus: [String!]
}
input SpendGuardianInvoiceFilter {
  by: InvoiceGuardianFilterEnum!
  value: String!
}
input SpendGuardianSignupInput {
  groupId: String!
  rosterName: String!
  seasonId: String!
}
input SpendInviteInput {
  email: String!
  firstName: String!
  groupId: String
  lastName: String!
  seasonId: String
  type: SpendInviteType!
  userId: String
}
input SpendInviteResendAllInput {
  orgId: String!
  seasonId: String
  userType: SpendInviteAllType
}
input SpendInviteResendInput {
  email: String!
  groupId: String!
  groupRosterId: String!
  id: String
  seasonId: String!
  userId: String!
}
input SpendInviteWhereInput {
  "Search invitation which is archived"
  archived: Boolean
  "Search by group id"
  groupId: String
  "Search by invite name"
  nameIncludes: String
  "Search by roster name only"
  rosterNameIncludes: String
  "Search by invite status: accepted, pending, undeliverable"
  status: String
  "Search by invite types. Valid values: guardian,group_staff,program_staff, program_observer,program_admin"
  type: [SpendInviteType!]
  "Search by invite type context: group or program. This will be affect invite type filter."
  typeContext: String
}
input SpendInvoiceDiscountInput {
  discountAmount: Int!
  invoiceId: String!
}
input SpendInvoiceFilter {
  by: InvoiceReportFilterEnum!
  value: String!
}
input SpendInvoiceHistoryInput {
  email: String!
  userId: String!
}
input SpendInvoiceInput {
  amount: Int
  balanceDue: Int
  budgetItemId: String
  description: String
  dueDate: String
  groupRosterId: String
  isOptional: Boolean
  note: String
  optedIn: Boolean
  paid: Boolean
  paidDate: String
  paymentScheduleInvoiceId: String
}
input SpendInvoicePaymentDeauthorizeInput {
  invoiceId: String
  message: String
}
input SpendInvoicePaymentMethodUpdate {
  ids: [String!]!
  isAutoPayAuthorized: Boolean
  paymentMethodId: String!
  paymentMethodSource: String!
}
input SpendInvoiceRefundInput {
  amount: Int!
  invoiceId: String!
}
input SpendInvoiceReminderInput {
  email: String
  failedPayment: Boolean
  groupName: String
  invoiceId: String
  pastDue: Boolean
}
input SpendInvoiceRequestChangeInput {
  invoiceId: String!
  note: String!
}
input SpendIsApproverUpdateInput {
  groupId: String
  isEnabled: Boolean!
  type: String!
  userId: String!
}
input SpendNotificationInput {
  email: [String!]!
  groupId: String
  isEmailCCed: Boolean!
  message: String!
  subject: String
}
input SpendOptInOrOutInput {
  invoiceId: String!
  optedIn: Boolean!
}
input SpendOrgACHCredit {
  amount: Int!
  counterpartyId: String!
  counterpartyName: String!
  note: String!
}
input SpendOrgAdminUpdateInput {
  email: String!
  keepAsAuthorizedUser: Boolean!
  organizationId: String!
}
input SpendOrganizationCheckDepositTagsInput {
  checkDepositId: String!
}
input SpendOrganizationExternalTransferInput {
  accountId: String!
  amount: Int!
  direction: ExternalTransferDirectionEnum!
  note: String!
}
input SpendOrganizationInput {
  achBaseFee: Int!
  achPercent: Float!
  cardBaseFee: Int!
  cardPercent: Float!
  email: String!
  externalTransferOutEnabled: Boolean
  groupBanksEnabled: Boolean
  isVerified: Boolean!
  legalName: String!
  logo: String
  nickname: String
  spendBaseFee: Int!
  spendPercent: Float!
  website: String
}
input SpendOrganizationPayoutCreateInput {
  counterparty: SpendPayoutCounterpartyCreate
  counterpartyId: String
  orgId: String!
  payee: SpendPayoutPayeeCreate
  payeeId: String
  sourceId: String
  type: SpendPayoutType!
}
input SpendOrganizationRecurringPayoutCreateInput {
  counterparty: SpendPayoutCounterpartyCreate
  counterpartyId: String
  interval: SpendPayoutInterval!
  orgId: String!
  sourceId: String
}
input SpendOrganizationRecurringPayoutUpdateInput {
  counterparty: SpendPayoutCounterpartyCreate
  counterpartyId: String
  id: String
  interval: SpendPayoutInterval
  isActive: Boolean
  orgId: String!
  sourceId: String
}
input SpendOrganizationsWhereInput {
  ids: [String!]
  nameIncludes: String
  "orgId is equivalent to externalId or id from orgs-api"
  orgIds: [String!]
  states: [String!]
  status: OrganizationFilterEnum
}
input SpendPaginationInput {
  limit: Int
  offset: Int
}
input SpendPastDueSort {
  order: SpendSortOrderEnum
  sortBy: PastDueSortOptions
}
input SpendPayeeInput {
  address1: String!
  address2: String
  city: String!
  einNumber: String
  name: String!
  referenceId: String!
  state: String!
  zipCode: String!
}
input SpendPayeeUpdateInput {
  address1: String!
  address2: String
  city: String!
  id: String!
  isArchived: Boolean
  name: String!
  referenceId: String!
  state: String!
  zipCode: String!
}
input SpendPaymentMethodDetach {
  paymentMethodId: String!
}
input SpendPaymentMethodInput {
  amount: Int
  authorizedAt: String
  discountAmounts: [SpendInvoiceDiscountInput!]
  hasApprovedAgreement: Boolean
  inviteId: String
  invoiceIds: [String!]
  isAutoPayAuthorized: Boolean
  paymentMethodId: String
  paymentMethodSource: String
  paymentMethodTiming: String
}
input SpendPaymentScheduleBySeasonInput {
  seasonId: String
  status: String
}
input SpendPaymentScheduleInput {
  amountDue: Int
  budgetItemId: String
  description: String
  dueDate: String
  groupId: String
  isOptional: Boolean
  note: String
  seasonId: String
  status: String
}
input SpendPayoutCounterpartyCreate {
  accountNumber: String!
  accountType: String!
  einNumber: String
  entityType: String!
  name: String!
  routingNumber: String!
}
input SpendPayoutPayeeCreate {
  address1: String!
  address2: String
  city: String!
  einNumber: String
  name: String!
  state: String!
  zipCode: String!
}
input SpendRawFileInput {
  id: String!
  type: RawFileTypeEnum!
}
input SpendRemoveSeasonMemberInput {
  groupRosterIds: [String]!
}
input SpendReorderCardInput {
  address: SpendAddressInput!
  cardId: String!
}
input SpendRosterUserInput {
  email: String!
  name: String!
  rosterEmail: String
}
input SpendRosterUserUpdate {
  email: String
  name: String
  rosterEmail: String
  rosterId: String!
}
input SpendRostersFilter {
  seasonId: String
}
input SpendSeasonInput {
  copyBudgetItems: Boolean
  copyPaymentSchedule: Boolean
  copyRoster: Boolean
  endDateAt: String
  groupId: String
  isBudgetShared: Boolean
  isLinkEnabled: Boolean
  name: String
  paymentScheduleStatus: PaymentScheduleStatus
  startDateAt: String
}
input SpendSettingsInput {
  approversRequired: Int
  debitCardApproval: Boolean
  enableGroupBanks: Boolean
  enableProgramAgreements: Boolean
  externalTransferApproval: Boolean
  externalTransferLimit: Int
  internalTransferApproval: Boolean
  internalTransferLimit: Int
  notifyBankActivityAdmins: Boolean
  notifyDueFrequencies: [String!]
  notifyFailedAchAdmins: Boolean
  notifyPastDueNonUsers: Boolean
  notifyUpcomingNonUsers: Boolean
  "@deprecated: pastDueFrequency is replaced with notifyDueFrequencies"
  pastDueFrequency: Int
  requirePaymentMethod: Boolean
  sendAchApproval: Boolean
  sendAchLimit: Int
  sendCheckApproval: Boolean
  sendCheckLimit: Int
}
input SpendSignUpInput {
  groupId: String!
  orgId: String!
  participantName: String!
  seasonId: String!
}
input SpendSignupAgreementInput {
  content: String!
  name: String!
}
input SpendSortInput {
  field: String!
  order: SpendSortOrderEnum
}
input SpendSystemNotificationCreateInput {
  description: String!
  groupStaff: Boolean!
  guardian: Boolean!
  organizationId: String
  programAdmin: Boolean!
  programStaff: Boolean!
  title: String!
}
input SpendSystemNotificationUpdateInput {
  description: String
  groupStaff: Boolean
  guardian: Boolean
  id: String!
  isArchived: Boolean
  organizationId: String
  programAdmin: Boolean
  programStaff: Boolean
  status: SystemNotificationStatus
  title: String
}
input SpendTransactionAttachmentInput {
  "Base-64 encoded file"
  content: String!
  description: String
  "Filename with extension"
  name: String!
  paymentId: String!
}
input SpendTransactionBudgetUnreconcileInput {
  reconciledBudgetTransactionIds: [String!]!
  reconciledTransactionId: String!
}
input SpendTransactionFilter {
  by: TransactionReportFilterEnum!
  value: String!
}
input SpendTransactionInput {
  authorizedAt: String
  direction: String
  discountAmounts: [SpendInvoiceDiscountInput!]
  externalId: String
  externalPaymentId: String
  invoiceIdList: [String!]
  source: String
}
input SpendTransactionInvoiceReconcileInput {
  ledgerTransactionId: String!
  reverse: Boolean
}
input SpendTransactionInvoiceUnreconcileInput {
  reconciledInvoiceTransactionIds: [String!]!
  reconciledTransactionId: String!
}
input SpendTransactionReconcileInput {
  budgets: [ReconcileBudget!]
  invoices: [ReconcileInvoice!]
  ledgerTransactionAmount: Int!
  ledgerTransactionId: String!
  reverse: Boolean
}
input SpendTransactionReconcileV2Input {
  budgets: [ReconcileBudget!]
  invoices: [ReconcileInvoiceWithBudget!]
  isLegacy: Boolean
  ledgerTransactionAmount: Int!
  ledgerTransactionId: String!
  legacyTarget: String
  reverse: Boolean
}
input SpendTransactionWhereInput {
  dateAfter: String
  dateBefore: String
  direction: ExternalTransferDirectionEnum
  groupIdOrgId: String!
  hasAttachment: Boolean
  method: [String!]
  nameIncludes: String
  reconciled: Boolean
  status: [String!]
  type: [String!]
}
input SpendTranscationNoteInput {
  content: String!
  paymentId: String!
}
input SpendUpdateInvoices {
  invoices: [UpdateInvoices!]
}
input SpendUpsertCategoryInput {
  id: String
  isDefault: Boolean!
  isHidden: Boolean!
  name: String!
  type: CategoryTypeEnum!
}
input SpendUserAccountUpdateInput {
  email: String!
  firstName: String!
  lastName: String!
  phone: String!
  prevEmail: String!
  udid: String!
}
input SpendUserAcknowledgeNotificationInput {
  notificationId: String!
}
input SpendUserEmailUpdateInput {
  newEmail: String!
  oldEmail: String!
}
input SpendUserLoginAuditInput {
  action: String!
  isImpersonated: Boolean!
  udId: String!
}
input SpendUserLoginAuditInputV2 {
  action: String!
  impersonatedBy: String
  udId: String!
}
input SpendUserNotificationSettingInput {
  copyPastDueInvoices: Boolean
  id: String!
  notifyOnBankActivity: Boolean
  notifyOnFailedCardPayments: Boolean
  notifyOnInvoicePayment: Boolean
}
input SpendUserSignupInput {
  groupId: String!
  inviteId: String
  seasonId: String!
  status: String!
}
input SpendVaultApplicationCreateInput {
  unitApplicationId: String!
  unitApplicationStatus: String
  userUdId: String
}
input StoreBaseFilterInput {
  limit: Int
  offset: Int
  searchBy: String
  searchValue: ID
}
input StoreBuildRequester {
  email: String
  name: String!
}
input StoreByCampaignIdsInput {
  all: Boolean
  campaignIds: [Int!]!
  limit: Int
  offset: Int
  orderBy: String
  orderBySort: String
  scopeIds: [Int]
}
input StoreByGLEmailInput {
  all: Boolean
  email: String!
  limit: Int
  offset: Int
  orderBy: String
  orderBySort: String
  scopeIds: [Int]
}
input StoreCampaignPayableInfoInput {
  campaignId: Int!
  primary: Boolean
}
input StoreCreatePaymentIntent {
  amount: Float!
  email: String!
  paymentMethod: String
  userSsoId: String!
}
input StoreCustomerInput {
  email: String!
  firstName: String!
  lastName: String!
  phoneNumber: String
}
input StoreEditInfoInput {
  activityType: String
  brands: [Int]
  productColors: [Int]
  scopeId: Int!
  storeCode: String!
  storeName: String
  themeColor: String
}
input StoreGLInput {
  email: String!
  firstName: String!
  lastName: String!
  phoneNumber: String
}
input StoreInput {
  accountManager: String
  accountManagerEmail: String
  accountManagerUDID: String
  activityType: String!
  city: String!
  fundraiserId: Int
  groupLeader: String
  groupLeaderEmail: String
  groupLeaderUDID: String
  groupName: String!
  logoDigitalUrl: String!
  logoEmbroideryUrl: String!
  logoHatUrl: String!
  "Pair of primary colors in format #RRGGBB|#RRGGBB"
  logoPrimaryColor: String!
  logoWebHeaderUrl: String!
  name: String!
  organizationId: Int
  organizationLegalName: String!
  partnerId: Int
  pointsPercentage: Int! = 10
  salesRep: String
  salesRepEmail: String
  salesRepUDID: String
  schoolId: String
  schoolName: String
  state: String!
  storeCode: String
  storeUrl: String!
  zip: String!
}
input StoreManagerUpdatePoints {
  managerEmail: String!
  managerId: Int!
  points: Float!
  scopeId: Int!
}
input StoreParticipantInput {
  email: String!
  firstName: String!
  lastName: String!
  phoneNumber: String
}
input StorePointEarnedGraphInput {
  interval: Int
  managerId: Int!
  scopeId: Int!
}
input StorePointsWithdrawalRequestInput {
  changeCampaignPayableDetails: Boolean!
  payableDetails: StoreWithdrawalPointsPayableInput!
  payableType: String!
  pointsType: String!
  requester: StorePointsWithdrawalRequesterInput!
  scope: StoresWithdrawalPointsScopeInput!
  taxDocument: String
  withdrawalPointsAmount: Int!
}
input StorePointsWithdrawalRequesterInput {
  email: String!
  name: String!
  userSsoId: String!
}
input StoreSaveTransaction {
  amount: Float!
  email: String!
  points: Float!
  scopeId: Int!
  transactionInfo: String!
  userSsoId: String!
}
input StoreSearchPaginationInput {
  limit: Int = 24
  offset: Int = 0
}
input StoreSearchWhereInput {
  name: String
  state: String
}
input StoreSender {
  email: String!
  scopeId: Int!
  senderId: Int!
}
input StoreStatusInput {
  reasonWhy: StoreStatusReasonWhy
  scopeId: Int!
  status: Boolean!
}
input StoreStatusReasonWhy {
  comment: String
  commentOption: String
}
input StoreSummaryByCampaignIdsInput {
  campaignIds: [Int!]!
  scopeIds: [Int]
  timeline: Int
}
input StoreTicketInput {
  message: String!
  requester: StoreTicketRequester!
  scopeId: Int!
  storeCode: String!
  subject: String!
}
input StoreTicketRequester {
  email: String!
  name: String!
}
input StoreTransferCustomer {
  customer: StoreCustomerInput
  points: Float!
  sender: StoreSender
}
input StoreTransferGL {
  groupLeader: StoreGLInput
  points: Float!
  sender: StoreSender
}
input StoreTransferParticipant {
  participant: StoreParticipantInput
  points: Float!
  sender: StoreSender
}
input StoreUpdatePayableInput {
  ein: Int!
  fullAddressOne: String!
  fullAddressTwo: String
  payableName: String!
}
input StoreUserCampaignsInput {
  all: Boolean
  demo: Boolean
  limit: Int
  offset: Int
  orderBy: String
  orderBySort: String
  searchBy: String
  searchValue: ID
}
input StoreUserParticipantsInput {
  all: Boolean
  campaignIds: [Int!]!
  limit: Int
  offset: Int
}
input StoreWithdrawalPointsPayableInput {
  city: String!
  country: String!
  ein: Int!
  fullAddressOne: String!
  fullAddressTwo: String
  identityId: Int!
  name: String
  payableName: String!
  region: String!
  street: String
  zip: Int!
}
input StoresSummaryByGLEmailInput {
  email: String!
  scopeIds: [Int]
  timeline: Int
}
input StoresWithdrawalPointsScopeInput {
  scopeCode: String!
  scopeId: Int!
  teamName: String!
}
"Filtering options for nullable String type"
input StringNullableFilter {
  "Contains operation"
  contains: String
  "Ends With operation"
  endsWith: String
  "Equals operation"
  equals: String
  "Greater Than operation"
  gt: String
  "Greater Than or Equal operation"
  gte: String
  "In operation for multiple values"
  in: [String!]
  "Less Than operation"
  lt: String
  "Less Than or Equal operation"
  lte: String
  "Not Equal operation"
  not: String
  "Not In operation for multiple values"
  notIn: [String!]
  "Starts With operation"
  startsWith: String
}
"metadata to be passed to Stripe API"
input StripeMetadataInput {
  activityType: String
  entity: String
  fundraiserId: String
  fundraiserId_legacy: String
  paymentType: String
  transactionId: String
}
input TeamByCodeInput {
  sport_code: String!
}
input TeamCreateInput {
  facility: String
  gender: String
  groupval: String
  home_field: String
  is_deleted: Boolean
  level1: String
  sport_code: String
  sport_description: String
  sport_name: String
}
input TeamDeleteInput {
  sport_code: String!
}
input TeamModifyInput {
  facility: String
  gender: String
  groupval: String
  home_field: String
  is_deleted: Boolean
  level1: String
  sport_code: String
  sport_description: String
  sport_name: String
}
input TeamOfficialCreateInput {
  id: Int!
  pay: Float
  pay_code: String
  sport: String!
  worker_duty: String
}
input TeamOfficialDeleteInput {
  id: Int!
}
input TeamOfficialModifyInput {
  id: Int!
  pay: Float
  pay_code: String
  worker_duty: String
}
input TeamPreparationCreateInput {
  id: Int!
  prep: Int!
  qty: String
  sport: String!
}
input TeamPreparationDeleteInput {
  id: Int!
}
input TeamPreparationModifyInput {
  id: Int!
  prep: Int
  qty: String
}
input TeamWorkerCreateInput {
  home_field: String
  id: Int!
  pay: Float
  pay_code: String
  sport: String!
  worker_duty: String
  worker_name: String
}
input TeamWorkerDeleteInput {
  id: Int!
}
input TeamWorkerModifyInput {
  home_field: String
  id: Int!
  pay: Float
  pay_code: String
  worker_duty: String
  worker_name: String
}
input TransactionInput {
  emailOverride: String
  "It's either going to be a donationId or a purchaseId"
  transactionId: ID!
  transactionType: TransactionType!
}
input TransactionsInput {
  createdAfter: Timestamp
  createdBefore: Timestamp
  destination: String
  exclude: [TransactionFilterType]
  externalId: String
  metadata: JSONObject
  source: String
  status: Status
}
input UnconfirmedEventsFiltersInput {
  activity: String
  gender: String
  levels: [String]
  limit: Int
  offset: Int
  opponents: [String]
  teams: [String]
  year: String
}
input UnreadCountFiltersInput {
  channelIds: [String!]
}
input UpdateInvoices {
  amount: Int
  dueDate: String!
  id: String!
}
input UpdateParticipantFundraiserConfigurationInput {
  autoImportEmails: ParticipantFundraiserConfigStates
  autoImportTexts: ParticipantFundraiserConfigStates
  fundraiserId: String
  giftShop: ParticipantFundraiserConfigStates
  guardianSetup: ParticipantFundraiserConfigStates
  profileSetup: ParticipantFundraiserConfigStates
  raiseFundraiserId: Int
  rewards: ParticipantFundraiserConfigStates
}
input UpdateParticipantGroupInput {
  goal: Int
  id: ID
  name: String!
}
input UpsertEventParticipantsInput {
  eventParticipantsArray: [EventParticipantsInput]!
}
input UpsertEventPreparationsInput {
  eventPreparationsArray: [EventPreparationsInput]!
}
input UpsertEventTransportDetailsInput {
  eventTransportDetailsArray: [EventTransportDetailsInput]!
}
input UpsertFacilitiesInput {
  address1: String
  address2: String
  city: String
  directions: String
  facility_name: String!
  location_id: Int
  state: String
  zipCode: String
}
input UpsertManyWorkersInput {
  workersArray: [CreateWorkerInput]!
}
input UpsertOfficialDutiesInput {
  duty: String
  id: Int
}
input UpsertOfficialsInput {
  address: String
  cell_phone: String
  email: String
  first_name: String
  home_phone: String
  last_name: String
  official_id: String!
  ssn: String
  work_phone: String
  zip: String
}
input UpsertOpponentsInput {
  ad_name: String
  address: String
  city: String
  email: String
  fax: String
  phone: String
  school_code: String!
  school_name: String
  state: String
  zip: String
}
input UpsertWorkersInput {
  cell_phone: String
  email: String
  first_name: String
  home_phone: String
  id: Int
  last_name: String
  pay_rate: Float
  ssn: String
  worker_type: WorkerTypes
}
input UrgentAnnouncementFilter {
  orderBy: ManageResourceAnnouncementOrderBy
  skip: Int
  take: Int
}
input UserConfirmProfileChallengeData {
  firstName: String!
  lastName: String!
  occupation: UserOccupation
  phoneNumber: String!
}
input UserInitiateProfileChallengeData {
  phoneNumber: String!
}
input UserInsightsConfigInput {
  name: NonEmptyString!
  value: NonEmptyString
}
input UserOrgAffiliationPayload {
  orgId: String
  title: String
}
input UserOrgInvitationPayload {
  email: String
  orgId: String
  title: String
}
input UserPermissionAssignment {
  id: String!
  scope: String
}
input UsersFilter {
  email: [String!]
  userIds: [ID!]
}
"Used for addresses in VaultKyb, VaultKyc and latestCreateCard"
input VaultAddressInput {
  city: String!
  postalCode: String!
  region: String!
  street: String!
  unit: String
}
"used in vaultKybUpdate and vaultKycUpdate"
input VaultAddressUpdateInput {
  city: String
  postalCode: String
  region: String
  street: String
  unit: String
}
input VaultBeneficialOwnerCreateInput {
  kycData: VaultKycCreateInput
  kycId: ID
  stripePersonId: ID
}
"Additional data passed into the Stripe api Can be read in Stripe dashboard"
input VaultCardMetadataInput {
  description: String
  walletId: String
}
input VaultCreateKybKycStripeInput {
  metadata: StripeMetadataInput
  statementDescriptor: String
  stripeEnv: VaultStripeEnv = RAISE
}
"Day, Month and Year of birth"
input VaultDobInput {
  day: String!
  month: String!
  year: String!
}
input VaultFormKybInput {
  accountId: ID!
  financialAccountNodeId: ID
  kybId: ID!
  orgId: ID!
}
input VaultFormStripeInput {
  metadata: StripeMetadataInput
  statementDescriptor: String
  stripeEnv: VaultStripeEnv = RAISE
}
input VaultKybCreateInput {
  address: VaultAddressInput!
  businessVertical: String
  customerFacingName: String!
  description: String
  email: String
  legalName: String!
  numberOfEmployees: String
  orgId: ID
  phoneNumber: String!
  stateOfIncorporation: String
  structure: KYB_STRUCTURE!
  taxId: String!
  type: KYB_TYPE!
  url: String
  yearOfIncorporation: String
}
input VaultKybInput {
  accountId: ID!
  financialAccountNodeId: ID
  kybId: ID!
  orgId: ID!
}
input VaultKycCreateInput {
  address: VaultAddressInput!
  director: Boolean
  dob: VaultDobInput!
  email: String!
  executive: Boolean
  firstName: String!
  lastName: String!
  owner: Boolean
  percentOwnership: Int
  phoneNumber: String!
  representative: Boolean
  ssn: String
  title: String
}
input VaultRepresentativeCreateInput {
  kycData: VaultKycCreateInput
  kycId: ID
  stripePersonId: ID
  userId: ID
}
"ip and date of Stripe ToS acceptance"
input VaultTosAcceptance {
  date: DateTime!
  ip: String!
}
input WebNotificationInput {
  url: String
}
"Input object for index query filters."
input Where {
  ""
  and: [String]
  ""
  between: [String]
  ""
  eq: [String]
  ""
  gt: [String]
  ""
  gte: [String]
  ""
  lt: [String]
  ""
  lte: [String]
  ""
  partition: [String]
  ""
  startsWith: [String]
}
input WorkerByIdInput {
  id: Int!
}
input eventContractItemInput {
  event_id: Int!
}
input storeBuildRequestInput {
  campaignId: Int
  comment: String
  primaryColor: String!
  requester: StoreBuildRequester!
  secondaryColor: String!
  storeActivity: String!
  storeLogo: String!
  storeName: String!
}
"Exposes a URL that specifies the behavior of this scalar."
directive @specifiedBy(
    "The URL that specifies the behavior of this scalar."
    url: String!
  ) on SCALAR
