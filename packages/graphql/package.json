{"name": "@store-monorepo/graphql", "version": "0.0.1", "private": true, "type": "commonjs", "main": "./src/index.js", "typings": "./src/index.d.ts", "scripts": {"codegen": "graphql-codegen --config codegen.yml"}, "dependencies": {"graphql-request": "6.1.0", "tslib": "^2.3.0"}, "devDependencies": {"@graphql-codegen/cli": "^5.0.2", "@graphql-codegen/client-preset": "4.2.6", "@graphql-codegen/introspection": "4.0.3", "@graphql-codegen/typescript": "^4.0.6"}}