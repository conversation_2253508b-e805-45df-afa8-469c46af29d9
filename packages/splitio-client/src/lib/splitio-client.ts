/* eslint-disable functional/no-classes, functional/no-this-expressions */
import { SplitFactory } from "@splitsoftware/splitio";
import SplitIO from "@splitsoftware/splitio/types/splitio";

class SplitIOClient {
  private client: SplitIO.IClient | undefined;

  constructor() {
    try {
      const factory = SplitFactory({
        core: {
          authorizationKey: process.env.SPLITIO_API_KEY! || "",
        },
      });
      this.client = factory.client();
    } catch (error) {
      console.log(error);
    }
  }

  public getClient(): SplitIO.IClient | undefined {
    return this.client;
  }

  public getTreatment(feature: string, config: SplitIO.Attributes = {}): string | null {
    if (!this.client) {
      return null;
    }
    return this.client.getTreatment("none", feature, config);
  }

  public on(feature: string, config: SplitIO.Attributes = {}): boolean {
    return this.client?.getTreatment("none", feature, config) === "on";
  }

  public async track(key: string, trafficType: string, eventType: string, value?: number): Promise<boolean> {
    if (!this.client) {
      return false;
    }
    return this.client.track(key, trafficType, eventType, value);
  }

  public async destroy(): Promise<void> {
    if (this.client) {
      await this.client.destroy();
    }
  }
}

const on = async (feature: string, config: SplitIO.Attributes = {}): Promise<boolean> => {
  const split = new SplitIOClient();
  await split.getClient()!.ready();
  const state = split.on(feature, config);
  await split.destroy();
  return state;
};

export { on };

export default async () => {
  const split = new SplitIOClient();
  await split.getClient()!.ready();
  return split;
};
