{"name": "splitio-client", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/splitio-client/src", "projectType": "library", "tags": [], "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/packages/splitio-client", "main": "packages/splitio-client/src/index.ts", "tsConfig": "packages/splitio-client/tsconfig.lib.json", "assets": ["packages/splitio-client/*.md"]}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"]}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "packages/splitio-client/jest.config.ts"}}}}