import Helper from "../src/helper";

describe("serialize", () => {
  it("should serialize a string", () => {
    expect(Helper.serialize("just a message")).toEqual("just a message");
  });

  it("should serialize a number", () => {
    expect(Helper.serialize(10)).toEqual("10");
  });

  it("should serialize an undefined", () => {
    expect(Helper.serialize(undefined)).toEqual(undefined);
  });

  it("should serialize an object", () => {
    const o = { x: 1, y: 2 };
    expect(Helper.serialize(o, true)).toEqual(JSON.stringify(o, null, 2));
    expect(Helper.serialize(o, false)).toEqual(o);
  });

  it("should serialize an error", () => {
    const error = new Error("Test Exception");
    const expected = Helper.serialize(error);

    expect(typeof expected).toEqual("string");
    expect((expected as string).startsWith("Error: Test Exception")).toBe(true);
  });
});
