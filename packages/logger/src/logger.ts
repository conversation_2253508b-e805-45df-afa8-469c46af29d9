import { createLogger, format } from "winston";

import Console from "./transport-console";
import Slack from "./transport-slack";

import Helper from "./helper";

const isLocal = process.env.NODE_ENV === "local";

const logger = createLogger({
  level: "info",
  exitOnError: false,
  format: format.json()
});

logger.add(Console);

if (Slack) logger.add(Slack);

export default logger;

export const log = (label: string, level: string, message: string, payload?: unknown) => {
  if (isLocal) {
    const output = !payload
      ? `${message}\n`
      : `${message}\n${Helper.serialize(payload, true)}\n`;

    return logger.log({ level, message: output });
  }

  return logger.log({
    level, label, message, payload: Helper.serialize(payload, false)
  });
};
