/* eslint-disable complexity, sonarjs/cognitive-complexity */
const serialize = (payload: unknown, beautify = false): object | string | undefined => {
  if (typeof payload === "undefined") {
    return undefined;
  }

  if (payload instanceof Error && payload.stack) {
    return payload.stack;
  }

  if (typeof payload === "string" || typeof payload === "number") {
    return String(payload);
  }

  if (payload !== null && typeof payload === "object") {
    return beautify
      ? JSON.stringify(payload, null, 2)
      : payload;
  }

  return JSON.stringify(payload);
};

export default { serialize };
