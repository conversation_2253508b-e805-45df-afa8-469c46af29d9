{"name": "logger", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/logger/src", "projectType": "library", "tags": [], "targets": {"codegen": {"executor": "nx:run-script", "options": {"script": "codegen"}}, "build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/packages/logger", "main": "packages/logger/src/index.ts", "tsConfig": "packages/logger/tsconfig.lib.json", "assets": ["packages/logger/*.md"]}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"]}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "packages/logger/jest.config.ts"}}}}