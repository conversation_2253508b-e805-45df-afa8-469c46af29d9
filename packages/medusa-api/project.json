{"name": "medusa-api", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/medusa-api/src", "projectType": "library", "tags": [], "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/packages/medusa-api", "main": "packages/medusa-api/src/index.ts", "tsConfig": "packages/medusa-api/tsconfig.lib.json", "assets": ["packages/medusa-api/*.md"]}}}}