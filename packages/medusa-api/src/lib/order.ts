import HttpClient from "./http-client";

const get = async (orderId: string) => {
  return HttpClient.get(`/store/orders/${orderId}`);
};
const updateMetadata = async (orderId: string, metadata: Record<string, unknown>) => {
  return HttpClient.patch(`/store/orders/${orderId}/metadata`, { metadata });
};
const addTrackingInfoToOrderFulfillment = async (orderId: string, trackingInfo: {
  netsuiteOrderId?: string | null,
  carrierName?: string | null,
  trackingNumber?: string | null,
  trackingUrl?: string | null,
  orderStatus?: string | null,
}) => {
  return HttpClient.patch(`/store/orders/${orderId}/tracking`, { trackingInfo });
};

export default {
  addTrackingInfoToOrderFulfillment,
  get,
  updateMetadata
};
