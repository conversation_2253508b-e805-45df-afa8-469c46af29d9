{"name": "slack-api", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/slack-api/src", "projectType": "library", "tags": [], "targets": {"codegen": {"executor": "nx:run-script", "options": {"script": "codegen"}}, "build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/packages/slack-api", "main": "packages/slack-api/src/index.ts", "tsConfig": "packages/slack-api/tsconfig.lib.json", "assets": ["packages/slack-api/*.md"]}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"]}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "packages/slack-api/jest.config.ts"}}}}