/* eslint-disable functional/no-classes, functional/no-this-expressions */
import { WebClient, ChatPostMessageArguments } from "@slack/web-api";

const SLACK_TOKEN = process.env.SLACK_TOKEN;

const slack = new WebClient(SLACK_TOKEN);

const postMessage = async (payload: ChatPostMessageArguments) => {
  return slack.chat.postMessage(payload);
};

const postText = async (channel: string, text: string) => {
  return postMessage({
    channel,
    text,
    unfurl_links: false,
    unfurl_media: false
  });
};

const codeBlock = (text: string): string => `\`\`\`${text}\`\`\``;

export default { postMessage, postText, codeBlock };
