version: '3.8'

services:
  nxapp-backend-base:
    restart: always
    build:
      context: .
      dockerfile: Dockerfile.dev
    environment:
      DATABASE_URL: '****************************************/store?schema=public'
      TEMPORAL_URL: 'temporal:7233'
      # NX_REJECT_UNKNOWN_LOCAL_CACHE: 0
    volumes:
      - .:/app
      # - node_modules:/app/node_modules
      # - nx-cache:/app/node_modules/.cache/nx
    networks:
      # - temporal-network
      - default
      - snap
    depends_on:
      - store-db
