# Disable Prettier -- YOU SHOULD NOT USE PRETTIER
**/*
# Use ESLint --fix instead. Do not use <PERSON><PERSON><PERSON>.
#   Why? Prettier is extremely opinionated and overformats code
#   in ways that are not compatible with ESLint. <PERSON>ttier also harms
#   understanding more than ESLint fix, as it allows for lazy coding
#   practices that are difficult to read.
#
# == REASON 1:
# Take this real world example:
#
#     export const userAssociations: Resolver<
#       ResolverTypeWrapper<
#          UserAssociations
#       >,
#       unknown,
#       unknown,
#       RequireFields<
#         QueryUserAssociationsArgs,
#         "id"
#       >
#     > = async (_, { id }, __, info) => {
#
# This declares a function and we have not gotten to the body of the function yet,
# we are caught up in a convoluted mess of function declaration and type definitions.
#
# The less obscure way to define this function would be to define a type:
#     type UserResolver = Resolver<
#       ResolverTypeWrapper<UserAssociations>,
#       unknown,
#       unknown,
#       RequireFields<QueryUserAssociationsArgs, "id">
#     >
#
# And then declare the function:
#     const userAssociations: UserResolver = async (_, { id }, __, info) => { ...  };
#
# This produces a much more legible function declaration. However, the path of least resistance
# is to blindly type out all code needed to exhaustion and then have prettier wrap it for us.
# Because of this, the developer never has to think about whether their code is actually legible,
# and they get a false sense of security that Prettier is providing value.
#
# == REASON 2:
# Prettier is extremely opinionated and has a certain philosophy and agenda that is not
# pragmatic. If you tell Prettier that lines should be a certain length, but you decide
# that an array is more readable like this:
#
#     const allowedStates = [
#       "Florida",
#       "California",
#       "Nevada",
#       "Washington",
#       "Tennessee",
#     ];
#
# Prettier will take your nicely formatted code and collapse it to the following:
#     const allowedStates = ["Florida", "California", "Nevada", "Washington", "Tennessee"];
#
# There is no way to stop prettier or tell it that you don't like what it's doing. They
# have a strong belief in "thoughtless formatting" to an extent that they become
# hostile to humans. Their belief is that the human should bend to the unforgiving rules
# of Prettier, even when Prettier yields less-readable code.
#
# Be good to your fellow humans. Write code for humans. Leave Prettier for the robots.
