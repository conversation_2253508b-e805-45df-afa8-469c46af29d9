#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

export CI=true
echo "🐶 Running Pre-Commit Hooks..."
echo "💄 Running ESLint with Autofix"

if (npm run lint:fix); then
    echo "✅ ESLint Passed."
    exit 0
else
    echo "😨 ESLint failed."
    echo "❌ Your git commit was halted due to failed linting."
    echo "❌ You can skip and commit anyways by using the '--no-verify' option on your commit."
    echo "❌ The linting errors must be resolved in your Pull Request or will not be merged."
    exit 1
fi
