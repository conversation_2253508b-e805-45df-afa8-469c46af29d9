#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

export CI=true
echo "🐶 Running Pre-Push Hooks..."
echo "🧪 Running Tests..."

if (npm run test); then
    echo "✅ Tests Passed"
    echo "📈 Getting Coverage on the Code You Wrote..."
    if (npm run test:coverage); then
        echo "✅ Code Coverage Passed"
        exit 0
    else
        echo "😨 Code Coverage Failed"
        echo "❌ Your git push was halted due to not testing your changes."
        echo "❌ You can skip and push anyways by using the '--no-verify' option on your commit."
        echo "❌ You must test your changes in your Pull Request or it will not be merged."
        echo
        echo "❌ If this was caused by fixing a linting error, the change must be approved"
        echo "❌ by a Director of Engineering or DevOps"
        exit 1
    fi
else
    echo "😨 Tests Failed"
    echo "❌ Your git push was halted due broken tests."
    echo "❌ You can skip and push anyways by using the '--no-verify' option on your commit."
    echo "❌ Your tests must pass in your Pull Request or it will not be merged."
    exit 1
fi
