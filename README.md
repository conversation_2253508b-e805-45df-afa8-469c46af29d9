# Store Monorepo

> **Looking for Store Magento? See the [`store-ecommerce`](https://github.com/snap-mobile/store-ecommerce) repository.**

> **Looking for Store Dashboard V1.0? See the [`store-dashboard-legacy`](https://github.com/snap-mobile/store-dashboard-legacy) repository.**

This repository is intended to serve as the new central monorepo for Store and its constituent Dashboard UIs. The Magento site is currently hosted as `store-ecommerce`. In the future, we envision hosting the e-commerce side in this repo as well using Magento's "PWA" utilities. For now, these 2 repositories are separate. Additionally, there is a legacy Store Dashboard codebase written in Vue.js instead of React. We intend to perform all new development against this repo and run both apps alongside each other. Eventually we expect this application to cross the threshold of taking over all functionality from the legacy Store Dashboard repository.

## Getting Started

To get started with Store Monorepo locally, follow these steps:

1. Clone the repository

    ```sh
    <NAME_EMAIL>:snap-mobile/store.git
    ```

2. Install Node

    ```sh
    asdf install
    ```

3. Install all dependencies

    ```sh
    pnpm install
    ```

4. Initialize Environment variables

    Copy the `.envrc.example` and populate all environment variables within. (ask the Store team for appropriate values)

    After setting up your .envrc, you will need to run:

    ```sh
    direnv allow
    ```

## Development

1. Run the app using the follwing:

    ```sh
    nx run store:serve
    ```

2. The Docker build compiles the app using:

    ```sh
    nx run store:build
    ```
