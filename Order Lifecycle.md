# Order Lifecycle

* order created - should be creating externalId with `SO_SnapV4${order.orderId}` in `createBaseNetsuiteOrder` workflow activity

* marco webhook (giving us tracking id)
  * find by netsuiteId & externalId -- my order's external is null - id of status should match `SO_SnapV4...`
  * save tracking_number in medusa & legacy_order table (wasn't saving for me locally tho)

* easypost webhook (giving us tracking update)
  * use tracking id to find order
  * call graph easypost handler
    * call workflow `handleTrackingStatus` with Order and status.

* workflow - handleTrackingStatus
  * map statuses to either SHIPPED or DELIVERED else throw
  * if (shippingReceivedAt is not null -> return success true/already handled)
  * updateDBOrderStatus(order.id, mappedStatus) - updates recievedAt and deliveredAt
  * create Email input
    * uses order.fundraiserId to find store
    * cleanUrl store.logoWebHeaderUrl
    * iterate through order.products //local has this empty 😩
  * sendEmail

Update

* order created
